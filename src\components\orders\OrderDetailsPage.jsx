import React, { useState, useEffect } from 'react'
import { 
  ChevronRight, 
  Calendar, 
  FileText, 
  MessageCircle, 
  Upload, 
  Download,
  CheckCircle, 
  Clock, 
  AlertCircle, 
  User, 
  DollarSign,
  Activity,
  Zap,
  FolderOpen,
  Send,
  Eye,
  Plus,
  Users
} from 'lucide-react'
import useStore from '../../store/useStore'
import OrderTimeline from './OrderTimeline'

const OrderDetailsPage = ({ orderId, onBack }) => {
  const { orders, user } = useStore()
  const [activeTab, setActiveTab] = useState('required')
  const [uploadedLinks, setUploadedLinks] = useState([])
  const [linkInput, setLinkInput] = useState('')
  const [messageContent, setMessageContent] = useState('')

  // العثور على الطلب
  const order = orders.find(o => o.id === orderId)

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">الطلب غير موجود</h3>
          <p className="text-gray-500 mb-4">لم يتم العثور على الطلب المطلوب</p>
          <button 
            onClick={onBack}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            العودة للقائمة
          </button>
        </div>
      </div>
    )
  }

  const getStatusConfig = (status) => {
    switch (status) {
      case 'completed':
        return {
          icon: CheckCircle,
          label: 'مكتمل',
          color: 'bg-green-100 text-green-800 border-green-200',
          iconColor: 'text-green-600'
        }
      case 'in_progress':
        return {
          icon: Activity,
          label: 'قيد التنفيذ',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          iconColor: 'text-blue-600'
        }
      case 'pending':
        return {
          icon: Clock,
          label: 'في الانتظار',
          color: 'bg-orange-100 text-orange-800 border-orange-200',
          iconColor: 'text-orange-600'
        }
      default:
        return {
          icon: Clock,
          label: 'غير محدد',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          iconColor: 'text-gray-600'
        }
    }
  }

  const calculateProgress = (order) => {
    if (order.status === 'completed') return 100
    if (order.status === 'in_progress') return 65
    if (order.status === 'pending') return 25
    return 0
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleLinkSubmit = () => {
    if (linkInput.trim()) {
      const newLink = {
        id: Date.now(),
        url: linkInput,
        uploadDate: new Date().toISOString(),
        status: 'pending'
      }
      setUploadedLinks([...uploadedLinks, newLink])
      setLinkInput('')
    }
  }

  const statusConfig = getStatusConfig(order.status)
  const progress = calculateProgress(order)
  const StatusIcon = statusConfig.icon

  // بيانات الإجراء الحالي
  const nextAction = {
    title: order.status === 'pending' ? 'مراجعة المتطلبات' :
           order.status === 'in_progress' ? 'تنفيذ المرحلة الثانية' :
           'تسليم المشروع النهائي',
    description: order.status === 'pending' ? 'يرجى مراجعة المتطلبات والموافقة على بدء العمل' :
                 order.status === 'in_progress' ? 'جاري العمل على تطوير الواجهات الأساسية للموقع' :
                 'المشروع جاهز للتسليم النهائي',
    urgency: order.priority === 'urgent' ? 'urgent' : 'normal',
    dueDate: '2024-07-01',
    estimatedTime: '3 أيام',
    requiredSignatures: '2',
    detailedInstructions: order.status === 'pending' ? 
      'يرجى مراجعة المستندات المرفقة والموافقة على:\n• التصميم المبدئي\n• المتطلبات التقنية\n• الجدول الزمني المقترح' :
      order.status === 'in_progress' ?
      'المرحلة الحالية تتضمن:\n• تطوير الصفحة الرئيسية\n• إعداد قاعدة البيانات\n• تصميم لوحة التحكم' :
      'المشروع مكتمل ويتضمن:\n• جميع الصفحات المطلوبة\n• لوحة التحكم الإدارية\n• دليل الاستخدام',
    documents: [
      { id: 1, name: 'التصميم المبدئي.pdf', size: '2.5 MB', version: 'v1.0' },
      { id: 2, name: 'المتطلبات التقنية.docx', size: '1.2 MB', version: 'v2.1' }
    ]
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white/95 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <button
                onClick={onBack}
                className="btn btn-ghost btn-sm p-2"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-large">صفحة التفاصيل</h1>
                <p className="text-caption">{order.title} • #{order.order_number}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 space-x-reverse">
              <span className={`badge ${
                order.status === 'completed' ? 'badge-success' :
                order.status === 'in_progress' ? 'badge-primary' :
                order.status === 'pending' ? 'badge-warning' :
                'badge-gray'
              }`}>
                <StatusIcon className="w-3 h-3 ml-1" />
                {statusConfig.label}
              </span>
              {order.priority && (
                <span className={`badge ${
                  order.priority === 'urgent' ? 'badge-error' :
                  order.priority === 'normal' ? 'badge-primary' :
                  'badge-gray'
                }`}>
                  {order.priority === 'urgent' ? '🔴 عاجل' :
                   order.priority === 'normal' ? '🔵 عادي' : '⚪ منخفض'}
                </span>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-12 gap-6">
          {/* الشريط الجانبي - معلومات المشروع */}
          <div className="col-span-4">
            <div className="card sticky top-24">
              {/* التقدم العام */}
              <div className="p-4 border-b border-gray-200/50">
                <h3 className="text-base font-semibold text-gray-900 mb-3">التقدم العام</h3>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="relative inline-flex items-center justify-center w-32 h-32">
                      <svg className="w-32 h-32 transform -rotate-90">
                        <circle
                          cx="64"
                          cy="64"
                          r="56"
                          stroke="currentColor"
                          strokeWidth="12"
                          fill="none"
                          className="text-gray-200"
                        />
                        <circle
                          cx="64"
                          cy="64"
                          r="56"
                          stroke="currentColor"
                          strokeWidth="12"
                          fill="none"
                          strokeDasharray={351.86}
                          strokeDashoffset={351.86 * (1 - progress / 100)}
                          className={`transition-all duration-500 ${statusConfig.iconColor}`}
                        />
                      </svg>
                      <span className="absolute text-3xl font-bold text-gray-900">{progress}%</span>
                    </div>
                  </div>
                  <div className="progress">
                    <div
                      className={`progress-bar ${
                        order.status === 'completed' ? 'bg-green-500' :
                        order.status === 'in_progress' ? 'bg-blue-500' :
                        'bg-orange-500'
                      }`}
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* معلومات المشروع */}
              <div className="p-4 border-b border-gray-200/50">
                <h3 className="text-base font-semibold text-gray-900 mb-3">معلومات المشروع</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-subtitle">تاريخ البداية</span>
                    <span className="text-body font-medium">{formatDate(order.created_at)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-subtitle">التاريخ المتوقع</span>
                    <span className="text-body font-medium">{formatDate(order.due_date)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-subtitle">رقم الطلب</span>
                    <span className="text-body font-medium">{order.order_number}</span>
                  </div>
                </div>
              </div>

              {/* الفريق المكلف */}
              <div className="p-4 border-b border-gray-200/50">
                <h3 className="text-base font-semibold text-gray-900 mb-3">الفريق المكلف</h3>
                <div className="space-y-3">
                  {[
                    { id: 1, name: 'أحمد محمد', role: 'مطور رئيسي', avatar: '👨‍💻' },
                    { id: 2, name: 'فاطمة علي', role: 'مصممة UI/UX', avatar: '👩‍🎨' },
                    { id: 3, name: 'محمد أحمد', role: 'مدير المشروع', avatar: '👨‍💼' }
                  ].map((member) => (
                    <div key={member.id} className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-9 h-9 bg-gray-100/60 rounded-2xl flex items-center justify-center text-base">
                        {member.avatar}
                      </div>
                      <div className="flex-1">
                        <p className="text-body font-medium">{member.name}</p>
                        <p className="text-caption">{member.role}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* المعلومات المالية */}
              <div className="p-4">
                <h3 className="text-base font-semibold text-gray-900 mb-3">المعلومات المالية</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-subtitle">إجمالي القيمة</span>
                    <span className="text-body font-medium">{order.estimated_cost?.toLocaleString()} ريال</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-subtitle">المدة المقدرة</span>
                    <span className="text-body font-medium">{order.estimated_duration} يوم</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="col-span-8">
            {/* التبويبات والمحتوى */}
            <div className="card">
              <nav className="flex border-b border-gray-200/50">
                {[
                  { id: 'required', label: 'المطلوب منك', icon: AlertCircle },
                  { id: 'current', label: 'يُحدث حاليًا', icon: Activity },
                  { id: 'timeline', label: 'الجدول الزمني', icon: Calendar }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 flex items-center justify-center px-5 py-3 text-subtitle font-medium border-b-2 transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 bg-blue-50/50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50/60'
                    }`}
                  >
                    <tab.icon className="w-4 h-4 ml-2" />
                    {tab.label}
                  </button>
                ))}
              </nav>

              {/* محتوى التبويبات */}
              <div className="p-6">

              {/* تبويب المطلوب منك */}
              {activeTab === 'required' && (
                <div className="space-y-6">
                  <div className="bg-yellow-50/60 border border-yellow-200/60 p-5 rounded-2xl">
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="w-14 h-14 bg-yellow-100 rounded-2xl flex items-center justify-center shadow-sm">
                        <AlertCircle className="w-7 h-7 text-yellow-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-yellow-900 mb-2">
                          مطلوب موافقتك على التصميم المبدئي
                        </h3>
                        <p className="text-yellow-800 mb-4">
                          يرجى مراجعة التصميم المبدئي للموقع والموافقة عليه لبدء مرحلة التطوير
                        </p>
                        <div className="flex items-center space-x-4 space-x-reverse text-sm text-yellow-700">
                          <div className="flex items-center space-x-1 space-x-reverse">
                            <Clock className="w-4 h-4" />
                            <span>مطلوب خلال 3 أيام</span>
                          </div>
                          <div className="flex items-center space-x-1 space-x-reverse">
                            <User className="w-4 h-4" />
                            <span>يتطلب موافقة المدير</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* قائمة المهام المطلوبة */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900">المهام المطلوبة منك:</h4>

                    {[
                      {
                        id: 1,
                        title: 'مراجعة التصميم المبدئي',
                        description: 'مراجعة والموافقة على التصميم المبدئي للصفحة الرئيسية',
                        status: 'pending',
                        priority: 'urgent',
                        dueDate: '2024-07-01'
                      },
                      {
                        id: 2,
                        title: 'تقديم المحتوى النصي',
                        description: 'تقديم النصوص والمحتوى المطلوب للموقع',
                        status: 'pending',
                        priority: 'normal',
                        dueDate: '2024-07-03'
                      },
                      {
                        id: 3,
                        title: 'اختيار الألوان النهائية',
                        description: 'اختيار لوحة الألوان النهائية للموقع',
                        status: 'completed',
                        priority: 'normal',
                        dueDate: '2024-06-28'
                      }
                    ].map((task) => (
                      <div key={task.id} className="bg-white border border-gray-200 rounded-xl p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 space-x-reverse mb-2">
                              <h5 className="font-medium text-gray-900">{task.title}</h5>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                task.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                                task.priority === 'normal' ? 'bg-blue-100 text-blue-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {task.priority === 'urgent' ? 'عاجل' : 'عادي'}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{task.description}</p>
                            <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                              <span>موعد التسليم: {task.dueDate}</span>
                            </div>
                          </div>
                          <div className="flex items-center">
                            {task.status === 'completed' ? (
                              <CheckCircle className="w-6 h-6 text-green-500" />
                            ) : (
                              <Clock className="w-6 h-6 text-yellow-500" />
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* تبويب يُحدث حاليًا */}
              {activeTab === 'current' && nextAction && (
                <div className="space-y-6">
                  {/* رأس الإجراء */}
                  <div className={`p-5 rounded-2xl border ${
                    nextAction.urgency === 'urgent'
                      ? 'bg-red-50/60 border-red-200/60'
                      : 'bg-blue-50/60 border-blue-200/60'
                  }`}>
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className={`w-14 h-14 rounded-2xl flex items-center justify-center shadow-sm ${
                        nextAction.urgency === 'urgent'
                          ? 'bg-red-100'
                          : 'bg-blue-100'
                      }`}>
                        <AlertCircle className={`w-7 h-7 ${
                          nextAction.urgency === 'urgent'
                            ? 'text-red-600'
                            : 'text-blue-600'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <h3 className={`text-xl font-semibold mb-2 ${
                          nextAction.urgency === 'urgent'
                            ? 'text-red-900'
                            : 'text-blue-900'
                        }`}>
                          {nextAction.title}
                        </h3>
                        <p className={`text-body ${
                          nextAction.urgency === 'urgent'
                            ? 'text-red-700'
                            : 'text-blue-700'
                        }`}>
                          {nextAction.description}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* معلومات الإجراء */}
                  <div className="grid grid-cols-3 gap-3">
                    <div className="bg-gray-50/60 rounded-xl p-4 text-center">
                      <Clock className="w-5 h-5 text-gray-500 mx-auto mb-2" />
                      <p className="text-caption mb-1">الموعد النهائي</p>
                      <p className="text-subtitle font-semibold">{nextAction.dueDate}</p>
                    </div>
                    <div className="bg-gray-50/60 rounded-xl p-4 text-center">
                      <Activity className="w-5 h-5 text-gray-500 mx-auto mb-2" />
                      <p className="text-caption mb-1">الوقت المقدر</p>
                      <p className="text-subtitle font-semibold">{nextAction.estimatedTime}</p>
                    </div>
                    <div className="bg-gray-50/60 rounded-xl p-4 text-center">
                      <Users className="w-5 h-5 text-gray-500 mx-auto mb-2" />
                      <p className="text-caption mb-1">التواقيع المطلوبة</p>
                      <p className="text-subtitle font-semibold">{nextAction.requiredSignatures}</p>
                    </div>
                  </div>

                  {/* التعليمات التفصيلية */}
                  {nextAction.detailedInstructions && (
                    <div className="bg-blue-50/60 rounded-xl p-5">
                      <h4 className="text-subtitle font-semibold text-blue-900 mb-3">التعليمات التفصيلية:</h4>
                      <div className="text-body text-blue-800 whitespace-pre-line">
                        {nextAction.detailedInstructions}
                      </div>
                    </div>
                  )}

                  {/* أزرار الإجراءات */}
                  <div className="flex justify-center space-x-3 space-x-reverse">
                    <button className="btn btn-secondary flex items-center">
                      <MessageCircle className="w-4 h-4 ml-2" />
                      إرسال رسالة
                    </button>
                    <button className="btn btn-primary flex items-center bg-green-500 hover:bg-green-600">
                      <CheckCircle className="w-4 h-4 ml-2" />
                      تأكيد إتمام الإجراء
                    </button>
                  </div>
                </div>
              )}

                {/* تبويب الجدول الزمني */}
                {activeTab === 'timeline' && (
                  <OrderTimeline order={order} />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OrderDetailsPage
