import React, { useState } from 'react'
import { 
  Search, 
  Bell, 
  Settings, 
  User,
  ChevronDown,
  Moon,
  Sun,
  Globe
} from 'lucide-react'
import useStore from '../../store/useStore'

const Header = ({ user, searchTerm, onSearchChange }) => {
  const { signOut, theme, toggleTheme } = useStore()
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)

  const handleSignOut = async () => {
    if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      await signOut()
    }
  }

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* البحث */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute right-3 top-3 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في النظام..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* أدوات الهيدر */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* زر تبديل الثيم */}
          <button
            onClick={toggleTheme}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            title={theme === 'light' ? 'التبديل للوضع الليلي' : 'التبديل للوضع النهاري'}
          >
            {theme === 'light' ? <Moon className="w-5 h-5" /> : <Sun className="w-5 h-5" />}
          </button>

          {/* الإشعارات */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors relative"
            >
              <Bell className="w-5 h-5" />
              {/* نقطة الإشعارات الجديدة */}
              <span className="absolute top-1 left-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>

            {/* قائمة الإشعارات */}
            {showNotifications && (
              <div className="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div className="p-4 border-b border-gray-200">
                  <h3 className="font-semibold text-gray-900">الإشعارات</h3>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  <div className="p-4 text-center text-gray-500">
                    لا توجد إشعارات جديدة
                  </div>
                </div>
                <div className="p-3 border-t border-gray-200">
                  <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                    عرض جميع الإشعارات
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* الإعدادات */}
          <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
            <Settings className="w-5 h-5" />
          </button>

          {/* قائمة المستخدم */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-3 space-x-reverse p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-teal-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                {user?.logo || user?.name?.charAt(0) || 'ن'}
              </div>
              <div className="text-right hidden sm:block">
                <p className="text-sm font-medium text-gray-900">
                  {user?.name || user?.organization_name}
                </p>
                <p className="text-xs text-gray-500">
                  {user?.membership_level === 'premium' ? 'عضوية مميزة' :
                   user?.membership_level === 'gold' ? 'عضوية ذهبية' :
                   'عضوية أساسية'}
                </p>
              </div>
              <ChevronDown className="w-4 h-4 text-gray-400" />
            </button>

            {/* قائمة المستخدم المنسدلة */}
            {showUserMenu && (
              <div className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div className="p-2">
                  <button className="w-full flex items-center space-x-3 space-x-reverse px-3 py-2 text-right hover:bg-gray-100 rounded-lg transition-colors">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-700">الملف الشخصي</span>
                  </button>
                  <button className="w-full flex items-center space-x-3 space-x-reverse px-3 py-2 text-right hover:bg-gray-100 rounded-lg transition-colors">
                    <Settings className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-700">الإعدادات</span>
                  </button>
                  <button className="w-full flex items-center space-x-3 space-x-reverse px-3 py-2 text-right hover:bg-gray-100 rounded-lg transition-colors">
                    <Globe className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-700">اللغة</span>
                  </button>
                  <hr className="my-2" />
                  <button
                    onClick={handleSignOut}
                    className="w-full flex items-center space-x-3 space-x-reverse px-3 py-2 text-right hover:bg-red-50 text-red-600 rounded-lg transition-colors"
                  >
                    <span className="text-sm">تسجيل الخروج</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
