<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/nama-logo.svg" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="نماء الاحترافية - نظام إدارة العملاء المتطور" />
    <meta name="keywords" content="نماء, إدارة العملاء, الجمعيات الخيرية, الحوكمة" />
    <meta name="author" content="نماء الاحترافية" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="نماء الاحترافية - نظام إدارة العملاء" />
    <meta property="og:description" content="نظام متطور لإدارة طلبات العملاء وتتبع مراحل العمل" />
    <meta property="og:type" content="website" />
    <meta property="og:locale" content="ar_SA" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <title>نماء الاحترافية - نظام إدارة العملاء</title>
    
    <style>
      body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, Helvetica, sans-serif;
        direction: rtl;
      }
      
      /* Loading Screen */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1e3a8a 0%, #14b8a6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        background: white;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        font-weight: bold;
        color: #1e3a8a;
        animation: pulse 2s infinite;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
      <div class="loading-logo">ن</div>
    </div>
    
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    
    <script>
      // Hide loading screen when page loads
      window.addEventListener('load', () => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          setTimeout(() => {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
              loadingScreen.remove();
            }, 500);
          }, 1000);
        }
      });
    </script>
  </body>
</html>
