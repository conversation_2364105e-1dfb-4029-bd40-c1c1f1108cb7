import React, { useState, useEffect } from 'react'
import {
  AlertCircle,
  Activity,
  Calendar,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Clock,
  CheckCircle,
  XCircle,
  Settings,
  FileText,
  User,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Send
} from 'lucide-react'
import {
  getServiceRequirements,
  addServiceRequirement,
  updateServiceRequirement,
  deleteServiceRequirement,
  completeServiceRequirement,
  getServiceActions,
  addServiceAction,
  deleteServiceAction,
  getServiceTimeline,
  addServiceTimelinePhase,
  updateServiceTimelinePhase,
  deleteServiceTimelinePhase
} from '../../lib/serviceData'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const ServiceDataManagement = ({ customer, serviceItem }) => {
  const [activeTab, setActiveTab] = useState('requirements')
  const [loading, setLoading] = useState(false)
  
  // بيانات المطالب
  const [requirements, setRequirements] = useState([])
  const [showAddRequirement, setShowAddRequirement] = useState(false)
  const [newRequirement, setNewRequirement] = useState({
    title: '',
    description: '',
    priority: 'normal',
    requires_file_upload: false,
    file_upload_method: '',
    file_upload_details: ''
  })
  const [editingRequirement, setEditingRequirement] = useState(null)

  // إدارة ردود المدير على الإنجازات
  const [showResponseModal, setShowResponseModal] = useState(false)
  const [selectedCompletion, setSelectedCompletion] = useState(null)
  const [adminResponse, setAdminResponse] = useState('')

  // بيانات الأحداث
  const [actions, setActions] = useState([])
  const [showAddAction, setShowAddAction] = useState(false)
  const [newAction, setNewAction] = useState({
    title: '',
    description: '',
    action_type: 'update'
  })

  // بيانات الجدول الزمني
  const [timeline, setTimeline] = useState([])
  const [showAddPhase, setShowAddPhase] = useState(false)
  const [newPhase, setNewPhase] = useState({
    phase_name: '',
    description: '',
    duration_text: '',
    status: 'pending',
    start_date: '',
    end_date: '',
    order_index: 0
  })
  const [editingPhase, setEditingPhase] = useState(null)

  // جلب جميع البيانات
  const loadAllData = async () => {
    if (!customer || !serviceItem) return

    try {
      setLoading(true)

      // جلب المطالب
      const { data: requirementsData } = await getServiceRequirements(serviceItem.id, customer.id)
      setRequirements(requirementsData || [])

      // جلب الأحداث
      const { data: actionsData } = await getServiceActions(serviceItem.id, customer.id)
      setActions(actionsData || [])

      // جلب الجدول الزمني
      const { data: timelineData } = await getServiceTimeline(serviceItem.id, customer.id)
      setTimeline(timelineData || [])

    } catch (error) {
      console.error('Error loading service data:', error)
      toast.error('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadAllData()
  }, [customer, serviceItem])

  // إدارة المطالب
  const handleAddRequirement = async () => {
    if (!newRequirement.title.trim()) {
      toast.error('يرجى إدخال عنوان المطلب')
      return
    }

    try {
      const { data, error } = await addServiceRequirement(serviceItem.id, customer.id, newRequirement)
      if (error) throw error
      
      setRequirements([data, ...requirements])
      setNewRequirement({
        title: '',
        description: '',
        priority: 'normal',
        requires_file_upload: false,
        file_upload_method: '',
        file_upload_details: ''
      })
      setShowAddRequirement(false)
      toast.success('تم إضافة المطلب بنجاح')
    } catch (error) {
      console.error('Error adding requirement:', error)
      toast.error('حدث خطأ في إضافة المطلب')
    }
  }

  const handleUpdateRequirement = async (requirementId, updates) => {
    try {
      const { data, error } = await updateServiceRequirement(requirementId, updates)
      if (error) throw error
      
      setRequirements(requirements.map(req => 
        req.id === requirementId ? { ...req, ...updates } : req
      ))
      setEditingRequirement(null)
      toast.success('تم تحديث المطلب بنجاح')
    } catch (error) {
      console.error('Error updating requirement:', error)
      toast.error('حدث خطأ في تحديث المطلب')
    }
  }

  const handleDeleteRequirement = async (requirementId) => {
    if (!confirm('هل أنت متأكد من حذف هذا المطلب؟')) return

    try {
      const { error } = await deleteServiceRequirement(requirementId)
      if (error) throw error

      setRequirements(requirements.filter(req => req.id !== requirementId))
      toast.success('تم حذف المطلب بنجاح')
    } catch (error) {
      console.error('Error deleting requirement:', error)
      toast.error('حدث خطأ في حذف المطلب')
    }
  }

  // الرد على إنجاز العميل
  const handleAdminResponse = async (status) => {
    if (!selectedCompletion) return

    try {
      const user = (await supabase.auth.getUser()).data.user

      const { data, error } = await updateServiceRequirement(selectedCompletion.id, {
        admin_response: adminResponse.trim(),
        admin_approval_status: status,
        admin_responded_at: new Date().toISOString(),
        admin_responded_by: user?.id
      })

      if (error) throw error

      // إعادة تحميل البيانات لضمان التحديث
      await loadAllData()

      setShowResponseModal(false)
      setSelectedCompletion(null)
      setAdminResponse('')

      const statusText = status === 'approved' ? 'موافقة' : 'رفض'
      toast.success(`تم ${statusText} الإنجاز بنجاح!`)
    } catch (error) {
      console.error('Error responding to completion:', error)
      toast.error('حدث خطأ في الرد على الإنجاز')
    }
  }

  // فتح نموذج الرد
  const openResponseModal = (requirement) => {
    setSelectedCompletion(requirement)
    setAdminResponse('')
    setShowResponseModal(true)
  }

  // إدارة الأحداث
  const handleAddAction = async () => {
    if (!newAction.title.trim()) {
      toast.error('يرجى إدخال عنوان الحدث')
      return
    }

    try {
      const { data, error } = await addServiceAction(serviceItem.id, customer.id, newAction)
      if (error) throw error
      
      setActions([data, ...actions])
      setNewAction({ title: '', description: '', action_type: 'update' })
      setShowAddAction(false)
      toast.success('تم إضافة الحدث بنجاح')
    } catch (error) {
      console.error('Error adding action:', error)
      toast.error('حدث خطأ في إضافة الحدث')
    }
  }

  const handleDeleteAction = async (actionId) => {
    if (!confirm('هل أنت متأكد من حذف هذا الحدث؟')) return
    
    try {
      const { error } = await deleteServiceAction(actionId)
      if (error) throw error
      
      setActions(actions.filter(action => action.id !== actionId))
      toast.success('تم حذف الحدث بنجاح')
    } catch (error) {
      console.error('Error deleting action:', error)
      toast.error('حدث خطأ في حذف الحدث')
    }
  }

  // إدارة الجدول الزمني
  const handleAddPhase = async () => {
    if (!newPhase.phase_name.trim()) {
      toast.error('يرجى إدخال اسم المرحلة')
      return
    }

    try {
      const phaseData = {
        ...newPhase,
        order_index: timeline.length
      }
      
      const { data, error } = await addServiceTimelinePhase(serviceItem.id, customer.id, phaseData)
      if (error) throw error
      
      setTimeline([...timeline, data])
      setNewPhase({
        phase_name: '',
        description: '',
        duration_text: '',
        status: 'pending',
        start_date: '',
        end_date: '',
        order_index: 0
      })
      setShowAddPhase(false)
      toast.success('تم إضافة المرحلة بنجاح')
    } catch (error) {
      console.error('Error adding phase:', error)
      toast.error('حدث خطأ في إضافة المرحلة')
    }
  }

  const handleUpdatePhase = async (phaseId, updates) => {
    try {
      const { data, error } = await updateServiceTimelinePhase(phaseId, updates)
      if (error) throw error
      
      setTimeline(timeline.map(phase => 
        phase.id === phaseId ? { ...phase, ...updates } : phase
      ))
      setEditingPhase(null)
      toast.success('تم تحديث المرحلة بنجاح')
    } catch (error) {
      console.error('Error updating phase:', error)
      toast.error('حدث خطأ في تحديث المرحلة')
    }
  }

  const handleDeletePhase = async (phaseId) => {
    if (!confirm('هل أنت متأكد من حذف هذه المرحلة؟')) return
    
    try {
      const { error } = await deleteServiceTimelinePhase(phaseId)
      if (error) throw error
      
      setTimeline(timeline.filter(phase => phase.id !== phaseId))
      toast.success('تم حذف المرحلة بنجاح')
    } catch (error) {
      console.error('Error deleting phase:', error)
      toast.error('حدث خطأ في حذف المرحلة')
    }
  }

  if (!customer || !serviceItem) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-full flex items-center justify-center">
        <div className="text-center p-4">
          <Settings className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <h4 className="text-sm font-medium text-gray-900 mb-1">اختر عنصر</h4>
          <p className="text-gray-500 text-xs">
            اختر عنصر خدمة لإدارة بياناته
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-full flex flex-col">
      {/* Header مضغوط */}
      <div className="p-3 border-b border-gray-200 flex-shrink-0">
        <div>
          <h3 className="text-base font-semibold text-gray-900 truncate">
            {serviceItem.title}
          </h3>
          <p className="text-xs text-gray-500 truncate">
            {customer.full_name}
          </p>
        </div>
      </div>

      {/* التبويبات مضغوطة */}
      <nav className="flex border-b border-gray-200 flex-shrink-0">
        {[
          { id: 'requirements', label: 'مطالب', icon: AlertCircle, count: requirements.length },
          { id: 'actions', label: 'أحداث', icon: Activity, count: actions.length },
          { id: 'timeline', label: 'جدول', icon: Calendar, count: timeline.length }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center px-2 py-2 text-xs font-medium border-b-2 transition-all duration-200 ${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="w-3 h-3 ml-1" />
            {tab.label}
            {tab.count > 0 && (
              <span className={`ml-1 px-1 py-0.5 rounded-full text-xs font-medium ${
                activeTab === tab.id
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </nav>

      {/* محتوى التبويبات */}
      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-4">جاري تحميل البيانات...</p>
          </div>
        ) : (
          <div className="p-3">
            {/* تبويب المطالب */}
            {activeTab === 'requirements' && (
              <div className="space-y-4">
                {/* رأس القسم مع زر الإضافة */}
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-semibold text-gray-900">
                    المطالب ({requirements.length})
                  </h4>
                  <button
                    onClick={() => setShowAddRequirement(true)}
                    className="btn btn-primary btn-xs"
                  >
                    <Plus className="w-3 h-3 ml-1" />
                    إضافة
                  </button>
                </div>

                {/* نموذج إضافة مطلب جديد */}
                {showAddRequirement && (
                  <div className="bg-blue-50 border border-blue-200 p-4 rounded-xl">
                    <div className="space-y-3">
                      <input
                        type="text"
                        placeholder="عنوان المطلب..."
                        value={newRequirement.title}
                        onChange={(e) => setNewRequirement({...newRequirement, title: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <textarea
                        placeholder="وصف المطلب (اختياري)..."
                        value={newRequirement.description}
                        onChange={(e) => setNewRequirement({...newRequirement, description: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows="3"
                      />
                      <select
                        value={newRequirement.priority}
                        onChange={(e) => setNewRequirement({...newRequirement, priority: e.target.value})}
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="low">منخفض</option>
                        <option value="normal">عادي</option>
                        <option value="high">مهم</option>
                        <option value="urgent">عاجل</option>
                      </select>

                      {/* خيار رفع الملف */}
                      <div className="space-y-3">
                        <label className="flex items-center space-x-2 space-x-reverse">
                          <input
                            type="checkbox"
                            checked={newRequirement.requires_file_upload}
                            onChange={(e) => setNewRequirement({...newRequirement, requires_file_upload: e.target.checked})}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-700">يتطلب رفع ملف</span>
                        </label>

                        {newRequirement.requires_file_upload && (
                          <div className="space-y-3 bg-gray-50 p-3 rounded-lg">
                            <select
                              value={newRequirement.file_upload_method}
                              onChange={(e) => setNewRequirement({...newRequirement, file_upload_method: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                              <option value="">اختر طريقة الرفع</option>
                              <option value="whatsapp">إرسال عبر واتساب</option>
                              <option value="link">مشاركة رابط</option>
                              <option value="other">أخرى</option>
                            </select>

                            {newRequirement.file_upload_method && (
                              <textarea
                                placeholder={
                                  newRequirement.file_upload_method === 'whatsapp' ? 'رقم الواتساب أو تعليمات الإرسال...' :
                                  newRequirement.file_upload_method === 'link' ? 'رابط الرفع أو التعليمات...' :
                                  'تفاصيل طريقة الرفع...'
                                }
                                value={newRequirement.file_upload_details}
                                onChange={(e) => setNewRequirement({...newRequirement, file_upload_details: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                rows="2"
                              />
                            )}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={handleAddRequirement}
                          className="btn btn-primary btn-sm"
                        >
                          <Save className="w-4 h-4 ml-2" />
                          حفظ
                        </button>
                        <button
                          onClick={() => {
                            setShowAddRequirement(false)
                            setNewRequirement({ title: '', description: '', priority: 'normal' })
                          }}
                          className="btn btn-ghost btn-sm"
                        >
                          <X className="w-4 h-4 ml-2" />
                          إلغاء
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* قائمة المطالب */}
                <div className="space-y-3">
                  {requirements.length > 0 ? (
                    requirements.map((requirement) => (
                      <div key={requirement.id} className="bg-white border border-gray-200 rounded-lg p-4">
                        {editingRequirement === requirement.id ? (
                          // نموذج التعديل
                          <div className="space-y-3">
                            <input
                              type="text"
                              value={requirement.title}
                              onChange={(e) => setRequirements(requirements.map(req => 
                                req.id === requirement.id ? {...req, title: e.target.value} : req
                              ))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <button
                                onClick={() => handleUpdateRequirement(requirement.id, {
                                  title: requirement.title,
                                  is_completed: !requirement.is_completed
                                })}
                                className="btn btn-primary btn-sm"
                              >
                                حفظ
                              </button>
                              <button
                                onClick={() => setEditingRequirement(null)}
                                className="btn btn-ghost btn-sm"
                              >
                                إلغاء
                              </button>
                            </div>
                          </div>
                        ) : (
                          // عرض المطلب
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 space-x-reverse mb-2">
                                <h5 className="text-sm font-medium text-gray-900">{requirement.title}</h5>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  requirement.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                                  requirement.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                                  requirement.priority === 'normal' ? 'bg-blue-100 text-blue-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {requirement.priority === 'urgent' ? 'عاجل' :
                                   requirement.priority === 'high' ? 'مهم' :
                                   requirement.priority === 'normal' ? 'عادي' : 'منخفض'}
                                </span>
                                {requirement.is_completed && (
                                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    مكتمل
                                  </span>
                                )}
                              </div>
                              {requirement.description && (
                                <p className="text-xs text-gray-600 mb-2">{requirement.description}</p>
                              )}

                              {/* معلومات رفع الملف */}
                              {requirement.requires_file_upload && (
                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-2">
                                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                                    <FileText className="w-4 h-4 text-blue-600" />
                                    <span className="text-xs font-medium text-blue-800">يتطلب رفع ملف</span>
                                  </div>
                                  {requirement.file_upload_method && (
                                    <div className="space-y-1">
                                      <p className="text-xs text-blue-700">
                                        <span className="font-medium">طريقة الرفع:</span> {
                                          requirement.file_upload_method === 'whatsapp' ? 'إرسال عبر واتساب' :
                                          requirement.file_upload_method === 'link' ? 'مشاركة رابط' :
                                          'أخرى'
                                        }
                                      </p>
                                      {requirement.file_upload_details && (
                                        <p className="text-xs text-blue-600">{requirement.file_upload_details}</p>
                                      )}
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* معلومات الإنجاز */}
                              {requirement.is_completed && (
                                <div className="space-y-2 mb-2">
                                  {/* تعليق العميل */}
                                  {requirement.completion_comment && (
                                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                                      <div className="flex items-center space-x-2 space-x-reverse mb-1">
                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                        <span className="text-xs font-medium text-green-800">تعليق العميل</span>
                                      </div>
                                      <p className="text-xs text-green-700">{requirement.completion_comment}</p>
                                      {requirement.completed_at && (
                                        <p className="text-xs text-green-600 mt-1">
                                          تم الإنجاز في {new Date(requirement.completed_at).toLocaleDateString('ar-SA')}
                                        </p>
                                      )}
                                    </div>
                                  )}

                                  {/* رد المدير */}
                                  {requirement.admin_response && (
                                    <div className={`border rounded-lg p-3 ${
                                      requirement.admin_approval_status === 'approved'
                                        ? 'bg-blue-50 border-blue-200'
                                        : 'bg-red-50 border-red-200'
                                    }`}>
                                      <div className="flex items-center space-x-2 space-x-reverse mb-1">
                                        {requirement.admin_approval_status === 'approved' ? (
                                          <ThumbsUp className="w-4 h-4 text-blue-600" />
                                        ) : (
                                          <ThumbsDown className="w-4 h-4 text-red-600" />
                                        )}
                                        <span className={`text-xs font-medium ${
                                          requirement.admin_approval_status === 'approved'
                                            ? 'text-blue-800'
                                            : 'text-red-800'
                                        }`}>
                                          رد المدير - {requirement.admin_approval_status === 'approved' ? 'موافقة' : 'رفض'}
                                        </span>
                                      </div>
                                      <p className={`text-xs ${
                                        requirement.admin_approval_status === 'approved'
                                          ? 'text-blue-700'
                                          : 'text-red-700'
                                      }`}>
                                        {requirement.admin_response}
                                      </p>
                                      {requirement.admin_responded_at && (
                                        <p className="text-xs text-gray-500 mt-1">
                                          تم الرد في {new Date(requirement.admin_responded_at).toLocaleDateString('ar-SA')}
                                        </p>
                                      )}
                                    </div>
                                  )}

                                  {/* زر الرد للمدير */}
                                  {requirement.is_completed && !requirement.admin_response && (
                                    <button
                                      onClick={() => openResponseModal(requirement)}
                                      className="w-full px-3 py-2 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-1 space-x-reverse"
                                    >
                                      <MessageSquare className="w-3 h-3" />
                                      <span>رد على الإنجاز</span>
                                    </button>
                                  )}
                                </div>
                              )}

                              <p className="text-xs text-gray-500">
                                تم إضافته في {new Date(requirement.created_at).toLocaleDateString('ar-SA')}
                              </p>
                            </div>
                            <div className="flex items-center space-x-1 space-x-reverse">
                              <button
                                onClick={() => handleUpdateRequirement(requirement.id, {
                                  is_completed: !requirement.is_completed
                                })}
                                className={`p-1 rounded transition-colors ${
                                  requirement.is_completed 
                                    ? 'text-green-500 hover:text-green-700 hover:bg-green-50' 
                                    : 'text-gray-400 hover:text-green-500 hover:bg-green-50'
                                }`}
                                title={requirement.is_completed ? 'إلغاء الإكمال' : 'تمييز كمكتمل'}
                              >
                                <CheckCircle className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => setEditingRequirement(requirement.id)}
                                className="p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
                                title="تعديل"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteRequirement(requirement.id)}
                                className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                                title="حذف"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <AlertCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                      <h4 className="text-base font-medium text-gray-900 mb-2">لا توجد مطالب</h4>
                      <p className="text-gray-500 text-sm">لم يتم إضافة أي مطالب لهذه الخدمة بعد</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* تبويب الأحداث */}
            {activeTab === 'actions' && (
              <div className="space-y-4">
                {/* رأس القسم مع زر الإضافة */}
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-semibold text-gray-900">
                    الأحداث ({actions.length})
                  </h4>
                  <button
                    onClick={() => setShowAddAction(true)}
                    className="btn btn-primary btn-xs"
                  >
                    <Plus className="w-3 h-3 ml-1" />
                    إضافة
                  </button>
                </div>

                {/* نموذج إضافة حدث جديد */}
                {showAddAction && (
                  <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
                    <div className="space-y-3">
                      <input
                        type="text"
                        placeholder="عنوان الحدث..."
                        value={newAction.title}
                        onChange={(e) => setNewAction({...newAction, title: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      />
                      <textarea
                        placeholder="وصف الحدث..."
                        value={newAction.description}
                        onChange={(e) => setNewAction({...newAction, description: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        rows="3"
                      />
                      <select
                        value={newAction.action_type}
                        onChange={(e) => setNewAction({...newAction, action_type: e.target.value})}
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      >
                        <option value="update">تحديث</option>
                        <option value="milestone">معلم مهم</option>
                        <option value="issue">مشكلة</option>
                        <option value="completion">إنجاز</option>
                        <option value="note">ملاحظة</option>
                      </select>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={handleAddAction}
                          className="btn btn-primary btn-sm"
                        >
                          <Save className="w-4 h-4 ml-2" />
                          حفظ
                        </button>
                        <button
                          onClick={() => {
                            setShowAddAction(false)
                            setNewAction({ title: '', description: '', action_type: 'update' })
                          }}
                          className="btn btn-ghost btn-sm"
                        >
                          <X className="w-4 h-4 ml-2" />
                          إلغاء
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* قائمة الأحداث */}
                <div className="space-y-3">
                  {actions.length > 0 ? (
                    actions.map((action) => (
                      <div key={action.id} className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 space-x-reverse mb-2">
                              <h5 className="text-sm font-medium text-gray-900">{action.title}</h5>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                action.action_type === 'milestone' ? 'bg-blue-100 text-blue-800' :
                                action.action_type === 'issue' ? 'bg-red-100 text-red-800' :
                                action.action_type === 'completion' ? 'bg-green-100 text-green-800' :
                                action.action_type === 'note' ? 'bg-purple-100 text-purple-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {action.action_type === 'milestone' ? 'معلم مهم' :
                                 action.action_type === 'issue' ? 'مشكلة' :
                                 action.action_type === 'completion' ? 'إنجاز' :
                                 action.action_type === 'note' ? 'ملاحظة' : 'تحديث'}
                              </span>
                            </div>
                            {action.description && (
                              <p className="text-xs text-gray-600 mb-2">{action.description}</p>
                            )}
                            <p className="text-xs text-gray-500">
                              تم إضافته في {new Date(action.created_at).toLocaleDateString('ar-SA')} في {new Date(action.created_at).toLocaleTimeString('ar-SA')}
                            </p>
                          </div>
                          <div className="flex items-center space-x-1 space-x-reverse">
                            <Activity className={`w-4 h-4 ${
                              action.action_type === 'milestone' ? 'text-blue-500' :
                              action.action_type === 'issue' ? 'text-red-500' :
                              action.action_type === 'completion' ? 'text-green-500' :
                              'text-gray-500'
                            }`} />
                            <button
                              onClick={() => handleDeleteAction(action.id)}
                              className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                              title="حذف"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Activity className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                      <h4 className="text-base font-medium text-gray-900 mb-2">لا توجد أحداث</h4>
                      <p className="text-gray-500 text-sm">لم يتم إضافة أي أحداث لهذه الخدمة بعد</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* تبويب الجدول الزمني */}
            {activeTab === 'timeline' && (
              <div className="space-y-4">
                {/* رأس القسم مع زر الإضافة */}
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-semibold text-gray-900">
                    الجدول الزمني ({timeline.length})
                  </h4>
                  <button
                    onClick={() => setShowAddPhase(true)}
                    className="btn btn-primary btn-xs"
                  >
                    <Plus className="w-3 h-3 ml-1" />
                    إضافة
                  </button>
                </div>

                {/* نموذج إضافة مرحلة جديدة */}
                {showAddPhase && (
                  <div className="bg-purple-50 border border-purple-200 p-4 rounded-xl">
                    <div className="space-y-3">
                      <input
                        type="text"
                        placeholder="اسم المرحلة..."
                        value={newPhase.phase_name}
                        onChange={(e) => setNewPhase({...newPhase, phase_name: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                      <textarea
                        placeholder="وصف المرحلة..."
                        value={newPhase.description}
                        onChange={(e) => setNewPhase({...newPhase, description: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        rows="2"
                      />
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                        <input
                          type="text"
                          placeholder="المدة (مثل: 3-5 أيام)"
                          value={newPhase.duration_text}
                          onChange={(e) => setNewPhase({...newPhase, duration_text: e.target.value})}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                        <input
                          type="date"
                          placeholder="تاريخ البداية"
                          value={newPhase.start_date}
                          onChange={(e) => setNewPhase({...newPhase, start_date: e.target.value})}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                        <input
                          type="date"
                          placeholder="تاريخ النهاية"
                          value={newPhase.end_date}
                          onChange={(e) => setNewPhase({...newPhase, end_date: e.target.value})}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                      </div>
                      <select
                        value={newPhase.status}
                        onChange={(e) => setNewPhase({...newPhase, status: e.target.value})}
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      >
                        <option value="pending">في الانتظار</option>
                        <option value="in-progress">قيد التنفيذ</option>
                        <option value="completed">مكتملة</option>
                        <option value="cancelled">ملغية</option>
                      </select>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={handleAddPhase}
                          className="btn btn-primary btn-sm"
                        >
                          <Save className="w-4 h-4 ml-2" />
                          حفظ
                        </button>
                        <button
                          onClick={() => {
                            setShowAddPhase(false)
                            setNewPhase({
                              phase_name: '',
                              description: '',
                              duration_text: '',
                              status: 'pending',
                              start_date: '',
                              end_date: '',
                              order_index: 0
                            })
                          }}
                          className="btn btn-ghost btn-sm"
                        >
                          <X className="w-4 h-4 ml-2" />
                          إلغاء
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* قائمة المراحل */}
                <div className="space-y-3">
                  {timeline.length > 0 ? (
                    timeline.map((phase, index) => (
                      <div key={phase.id} className="bg-white border border-gray-200 rounded-lg p-4">
                        {editingPhase === phase.id ? (
                          // نموذج التعديل
                          <div className="space-y-3">
                            <input
                              type="text"
                              value={phase.phase_name}
                              onChange={(e) => setTimeline(timeline.map(p =>
                                p.id === phase.id ? {...p, phase_name: e.target.value} : p
                              ))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            />
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <select
                                value={phase.status}
                                onChange={(e) => setTimeline(timeline.map(p =>
                                  p.id === phase.id ? {...p, status: e.target.value} : p
                                ))}
                                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                              >
                                <option value="pending">في الانتظار</option>
                                <option value="in-progress">قيد التنفيذ</option>
                                <option value="completed">مكتملة</option>
                                <option value="cancelled">ملغية</option>
                              </select>
                              <button
                                onClick={() => handleUpdatePhase(phase.id, {
                                  phase_name: phase.phase_name,
                                  status: phase.status
                                })}
                                className="btn btn-primary btn-sm"
                              >
                                حفظ
                              </button>
                              <button
                                onClick={() => setEditingPhase(null)}
                                className="btn btn-ghost btn-sm"
                              >
                                إلغاء
                              </button>
                            </div>
                          </div>
                        ) : (
                          // عرض المرحلة
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3 space-x-reverse">
                              {/* رقم المرحلة */}
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold ${
                                phase.status === 'completed' ? 'bg-green-100 text-green-800' :
                                phase.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                                phase.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {index + 1}
                              </div>

                              <div className="flex-1">
                                <div className="flex items-center space-x-2 space-x-reverse mb-2">
                                  <h5 className="text-sm font-medium text-gray-900">{phase.phase_name}</h5>
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    phase.status === 'completed' ? 'bg-green-100 text-green-800' :
                                    phase.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                                    phase.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                    'bg-gray-100 text-gray-800'
                                  }`}>
                                    {phase.status === 'completed' ? 'مكتملة' :
                                     phase.status === 'in-progress' ? 'قيد التنفيذ' :
                                     phase.status === 'cancelled' ? 'ملغية' : 'في الانتظار'}
                                  </span>
                                </div>

                                {phase.description && (
                                  <p className="text-xs text-gray-600 mb-2">{phase.description}</p>
                                )}

                                <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                                  {phase.duration_text && (
                                    <div className="flex items-center">
                                      <Clock className="w-3 h-3 ml-1" />
                                      {phase.duration_text}
                                    </div>
                                  )}
                                  {phase.start_date && (
                                    <div className="flex items-center">
                                      <Calendar className="w-3 h-3 ml-1" />
                                      {new Date(phase.start_date).toLocaleDateString('ar-SA')}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center space-x-1 space-x-reverse">
                              <button
                                onClick={() => handleUpdatePhase(phase.id, {
                                  status: phase.status === 'completed' ? 'pending' : 'completed'
                                })}
                                className={`p-1 rounded transition-colors ${
                                  phase.status === 'completed'
                                    ? 'text-green-500 hover:text-green-700 hover:bg-green-50'
                                    : 'text-gray-400 hover:text-green-500 hover:bg-green-50'
                                }`}
                                title={phase.status === 'completed' ? 'إلغاء الإكمال' : 'تمييز كمكتملة'}
                              >
                                <CheckCircle className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => setEditingPhase(phase.id)}
                                className="p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
                                title="تعديل"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeletePhase(phase.id)}
                                className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                                title="حذف"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                      <h4 className="text-base font-medium text-gray-900 mb-2">لا توجد مراحل</h4>
                      <p className="text-gray-500 text-sm">لم يتم إضافة أي مراحل للجدول الزمني بعد</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* نموذج الرد على الإنجاز */}
      {showResponseModal && selectedCompletion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-lg w-full">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                الرد على إنجاز: {selectedCompletion.title}
              </h3>
            </div>

            <div className="p-6 space-y-4">
              {/* عرض تعليق العميل */}
              {selectedCompletion.completion_comment && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="flex items-center space-x-2 space-x-reverse mb-1">
                    <User className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-green-800">تعليق العميل</span>
                  </div>
                  <p className="text-sm text-green-700">{selectedCompletion.completion_comment}</p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رد المدير
                </label>
                <textarea
                  value={adminResponse}
                  onChange={(e) => setAdminResponse(e.target.value)}
                  placeholder="اكتب ردك على إنجاز العميل..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows="4"
                />
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <button
                  onClick={() => handleAdminResponse('approved')}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <ThumbsUp className="w-4 h-4" />
                  <span>موافقة</span>
                </button>
                <button
                  onClick={() => handleAdminResponse('rejected')}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <ThumbsDown className="w-4 h-4" />
                  <span>رفض</span>
                </button>
                <button
                  onClick={() => setShowResponseModal(false)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ServiceDataManagement
