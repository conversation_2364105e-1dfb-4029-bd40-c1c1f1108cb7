import React from 'react'
import {
  BarChart3,
  FileText,
  Bell,
  User,
  UserCog,
  Settings,
  LogOut,
  Home,
  Calendar,
  MessageSquare,
  Archive,
  HelpCircle,
  Shield,
  ChevronRight,
  ChevronLeft,
  Menu
} from 'lucide-react'
import useStore from '../../store/useStore'
import useSidebar from '../../hooks/useSidebar'

const Sidebar = ({ selectedView, onViewChange, unreadCount = 0 }) => {
  const { signOut, user } = useStore()
  const { isCollapsed, toggleSidebar } = useSidebar()

  // التحقق من الصلاحيات الإدارية
  const isAdmin = user?.account_type === 'admin' || user?.email === '<EMAIL>'

  const menuItems = [
    {
      id: 'overview',
      label: 'الرئيسية',
      icon: Home,
      badge: null
    },
    {
      id: 'services',
      label: 'إدارة الخدمات',
      icon: FileText,
      badge: null
    },
    ...(isAdmin ? [
      {
        id: 'admin',
        label: 'إدارة الإجراءات',
        icon: Shield,
        badge: null
      },
      {
        id: 'customers',
        label: 'إدارة العملاء',
        icon: UserCog,
        badge: null
      },
      {
        id: 'advertisements',
        label: 'إدارة الإعلانات',
        icon: Settings,
        badge: null
      }
    ] : []),
    {
      id: 'profile',
      label: 'الملف الشخصي',
      icon: User,
      badge: null
    }
  ]

  const handleSignOut = async () => {
    if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      await signOut()
    }
  }

  const handleNavigation = (itemId) => {
    onViewChange(itemId)
    // إغلاق السايدبار تلقائياً عند اختيار تبويب (إذا كان مفتوح)
    if (!isCollapsed) {
      toggleSidebar()
    }
  }

  return (
    <>
      {/* Overlay للخلفية عند فتح السايدبار */}
      {!isCollapsed && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-30 lg:hidden"
          onClick={toggleSidebar}
        />
      )}

      {/* السايدبار */}
      <div className={`${
        isCollapsed ? 'w-16' : 'w-64'
      } bg-white/95 backdrop-blur-xl border-r border-gray-200/50 flex flex-col h-screen fixed top-0 right-0 z-40 transition-all duration-300 ease-in-out shadow-xl`}>
      {/* الشعار وزر الطي */}
      <div className="p-4 relative">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-9 h-9 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-sm">
              <span className="text-white font-semibold text-base">ن</span>
            </div>
            {!isCollapsed && (
              <div>
                <h1 className="text-base font-semibold text-gray-900">نما</h1>
                <p className="text-xs text-gray-500">نظام إدارة العملاء</p>
              </div>
            )}
          </div>

          {/* زر الطي */}
          <button
            onClick={toggleSidebar}
            className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-500 hover:text-gray-700"
            title={isCollapsed ? 'توسيع السايدبار' : 'طي السايدبار'}
          >
            {isCollapsed ? (
              <ChevronLeft className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* القائمة الرئيسية */}
      <nav className="flex-1 px-3 space-y-1">
        {menuItems.map((item) => {
          const Icon = item.icon
          const isActive = selectedView === item.id

          return (
            <div key={item.id} className="relative group">
              <button
                onClick={() => handleNavigation(item.id)}
                className={`w-full flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'} px-3 py-2 rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'bg-blue-500 text-white shadow-sm'
                    : 'text-gray-600 hover:bg-gray-100/60 hover:text-gray-900'
                }`}
                title={isCollapsed ? item.label : ''}
              >
                <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3 space-x-reverse'}`}>
                  <Icon className={`w-4 h-4 ${isActive ? 'text-white' : 'text-gray-500'}`} />
                  {!isCollapsed && (
                    <span className="font-medium text-sm">{item.label}</span>
                  )}
                </div>

                {!isCollapsed && item.badge && (
                  <span className={`text-xs rounded-full px-2 py-0.5 min-w-[18px] text-center font-medium ${
                    isActive
                      ? 'bg-white/20 text-white'
                      : 'bg-red-500 text-white'
                  }`}>
                    {item.badge}
                  </span>
                )}
              </button>

              {/* Tooltip عند الطي */}
              {isCollapsed && (
                <div className="absolute right-full top-1/2 transform -translate-y-1/2 mr-2 px-2 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  {item.label}
                  <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900"></div>
                </div>
              )}
            </div>
          )
        })}
      </nav>

      {/* القائمة السفلية */}
      <div className="p-4 border-t border-gray-200/50 space-y-2">
        {/* زر تسجيل الخروج */}
        <div className="relative group">
          <button
            onClick={handleSignOut}
            className={`w-full flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3 space-x-reverse'} px-3 py-2.5 rounded-xl text-red-500 hover:bg-red-50/60 transition-all duration-200`}
            title={isCollapsed ? 'تسجيل الخروج' : ''}
          >
            <LogOut className="w-4 h-4" />
            {!isCollapsed && (
              <span className="font-medium text-sm">تسجيل الخروج</span>
            )}
          </button>

          {/* Tooltip عند الطي */}
          {isCollapsed && (
            <div className="absolute right-full top-1/2 transform -translate-y-1/2 mr-2 px-2 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              تسجيل الخروج
              <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900"></div>
            </div>
          )}
        </div>
      </div>
      </div>
    </>
  )
}

export default Sidebar
