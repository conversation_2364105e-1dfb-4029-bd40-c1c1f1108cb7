<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- خلفية متدرجة -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#14b8a6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- الخلفية المستديرة -->
  <rect width="64" height="64" rx="16" fill="url(#gradient)"/>
  
  <!-- حرف النون -->
  <text x="32" y="45" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="white" text-anchor="middle">ن</text>
  
  <!-- نقطة صغيرة للتميز -->
  <circle cx="48" cy="16" r="4" fill="white" opacity="0.8"/>
</svg>
