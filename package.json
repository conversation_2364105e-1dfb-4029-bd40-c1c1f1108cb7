{"name": "nama-client-system", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "preview": "vite preview", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "deploy": "npm run build && netlify deploy --prod --dir=dist", "deploy:preview": "npm run build && netlify deploy --dir=dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@supabase/supabase-js": "^2.39.0", "lucide-react": "^0.344.0", "react-router-dom": "^6.20.1", "date-fns": "^3.0.6", "react-hot-toast": "^2.4.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vitest": "^1.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@vitest/ui": "^1.0.4", "@vitest/coverage-v8": "^1.0.4", "jsdom": "^23.0.1"}}