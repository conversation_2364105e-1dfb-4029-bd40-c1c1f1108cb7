import React, { useState, useEffect } from 'react'
import { 
  Mail, 
  Eye, 
  EyeOff, 
  LogIn, 
  AlertCircle,
  Shield,
  Zap
} from 'lucide-react'
import useStore from '../../store/useStore'
import LoadingSpinner from '../ui/LoadingSpinner'
import toast from 'react-hot-toast'

const LoginPage = () => {
  const { signIn, isLoading } = useStore()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(true)
  const [errors, setErrors] = useState({})

  // بيانات تجريبية للاختبار
  const demoAccounts = [
    {
      email: '<EMAIL>',
      password: '123456',
      name: 'شركة نماء للخدمات الرقمية',
      logo: '🏢',
      type: 'admin'
    },
    {
      email: '<EMAIL>',
      password: '123456',
      name: 'مؤسسة روافد التنمية',
      logo: '🌱',
      type: 'gold'
    },
    {
      email: '<EMAIL>',
      password: '123456',
      name: 'جمعية نور الخيرية',
      logo: '💡',
      type: 'basic'
    },
    {
      email: '<EMAIL>',
      password: '123456',
      name: 'جمعية ديم الخيرية',
      logo: '🌟',
      type: 'gold'
    },
    {
      email: '<EMAIL>',
      password: '123456',
      name: 'جمعية جمان الخيرية',
      logo: '🌸',
      type: 'basic'
    }
  ]

  // التحقق من صحة البيانات
  const validateForm = () => {
    const newErrors = {}

    if (!formData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }

    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // معالج تسجيل الدخول
  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      toast.error('يرجى تصحيح الأخطاء في النموذج')
      return
    }

    // مسح البيانات المحلية قبل تسجيل الدخول
    localStorage.clear()
    sessionStorage.clear()

    const result = await signIn(formData.email, formData.password)

    if (!result.success) {
      toast.error(result.error || 'فشل في تسجيل الدخول')
    }
  }

  // تعبئة بيانات الحساب التجريبي
  const fillDemoAccount = (account) => {
    setFormData({
      email: account.email,
      password: account.password
    })
    setErrors({})
    toast.success(`تم تعبئة بيانات ${account.name}`)
  }

  // معالج تغيير البيانات
  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // مسح الخطأ عند التعديل
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-teal-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8 animate-fade-in">
        
        {/* الشعار والعنوان */}
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-900 to-teal-500 rounded-3xl shadow-2xl mb-6 transform hover:scale-105 transition-transform duration-300">
            <span className="text-white text-4xl font-bold">ن</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2 text-gradient">
            نماء الاحترافية
          </h1>
          <p className="text-lg text-gray-600">نظام إدارة العملاء المتطور</p>

          {/* رسالة نجاح إعداد النظام */}
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800 text-center">
              ✅ تم إعداد النظام بنجاح! يمكنك الآن تسجيل الدخول
            </p>
          </div>
          
          {/* مؤشرات الميزات */}
          <div className="flex items-center justify-center space-x-6 space-x-reverse mt-4">
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
              <Shield className="w-4 h-4 text-green-500" />
              <span>آمن</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
              <Zap className="w-4 h-4 text-blue-500" />
              <span>سريع</span>
            </div>
          </div>
        </div>

        {/* نموذج تسجيل الدخول */}
        <div className="bg-white rounded-2xl shadow-xl p-8 backdrop-filter backdrop-blur-lg bg-opacity-95 border border-gray-100">
          <form onSubmit={handleSubmit} className="space-y-6">
            
            {/* البريد الإلكتروني */}
            <div>
              <label className="form-label">
                البريد الإلكتروني
              </label>
              <div className="relative">
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`form-input pr-12 ${errors.email ? 'border-red-300 focus:ring-red-500' : ''}`}
                  placeholder="<EMAIL>"
                  dir="ltr"
                />
                <Mail className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 ml-1" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* كلمة المرور */}
            <div>
              <label className="form-label">
                كلمة المرور
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className={`form-input pr-12 ${errors.password ? 'border-red-300 focus:ring-red-500' : ''}`}
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-3.5 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 ml-1" />
                  {errors.password}
                </p>
              )}
            </div>

            {/* تذكرني ونسيت كلمة المرور */}
            <div className="flex items-center justify-between">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="ml-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors"
                />
                <span className="text-sm text-gray-700">تذكرني</span>
              </label>
              <button 
                type="button"
                className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
              >
                نسيت كلمة المرور؟
              </button>
            </div>

            {/* زر تسجيل الدخول */}
            <button
              type="submit"
              disabled={isLoading}
              className="btn btn-primary w-full py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size="small" color="white" />
                  <span className="mr-2">جاري تسجيل الدخول...</span>
                </>
              ) : (
                <>
                  <LogIn className="w-5 h-5 ml-2" />
                  تسجيل الدخول
                </>
              )}
            </button>
          </form>

          {/* الحسابات التجريبية */}
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-teal-50 rounded-xl border border-blue-100">
            <p className="text-sm font-medium text-blue-900 mb-3 text-center">
              حسابات تجريبية للاختبار:
            </p>
            <div className="space-y-2">
              {demoAccounts.map((account, index) => (
                <button
                  key={index}
                  onClick={() => fillDemoAccount(account)}
                  className="btn btn-ghost w-full flex justify-between items-center p-3 bg-white rounded-xl hover:shadow-md transition-all duration-200 border border-transparent hover:border-blue-200"
                >
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <span className="text-2xl">{account.logo}</span>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{account.name}</p>
                      <p className="text-xs text-gray-500">{account.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      account.type === 'admin' ? 'bg-red-100 text-red-800' :
                      account.type === 'gold' ? 'bg-yellow-100 text-yellow-800' :
                      account.type === 'premium' ? 'bg-purple-100 text-purple-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {account.type === 'admin' ? 'إداري' :
                       account.type === 'gold' ? 'ذهبي' :
                       account.type === 'premium' ? 'بريميوم' :
                       'أساسي'}
                    </span>
                    <span className="text-xs font-mono text-gray-400">123456</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* التذييل */}
        <div className="text-center space-y-3">
          <p className="text-sm text-gray-500">
            جميع الحقوق محفوظة © 2024 نماء الاحترافية
          </p>
          <div className="flex items-center justify-center space-x-4 space-x-reverse text-xs">
            <a href="#" className="text-gray-400 hover:text-gray-600 transition-colors">
              سياسة الخصوصية
            </a>
            <span className="text-gray-300">•</span>
            <a href="#" className="text-gray-400 hover:text-gray-600 transition-colors">
              الشروط والأحكام
            </a>
            <span className="text-gray-300">•</span>
            <a href="#" className="text-gray-400 hover:text-gray-600 transition-colors">
              اتصل بنا
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
