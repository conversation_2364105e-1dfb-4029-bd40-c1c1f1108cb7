# دليل المساهمة في نماء الاحترافية

نرحب بمساهماتكم في تطوير نظام نماء الاحترافية! هذا الدليل يوضح كيفية المساهمة بفعالية.

## 🤝 كيفية المساهمة

### 1. Fork المشروع

1. انقر على زر "Fork" في أعلى الصفحة
2. استنسخ المشروع المنسوخ إلى جهازك:
```bash
git clone https://github.com/your-username/nama-client-system.git
cd nama-client-system
```

### 2. إعداد البيئة المحلية

```bash
# تثبيت التبعيات
npm install

# إعداد متغيرات البيئة
cp .env.example .env
# حدث ملف .env بالقيم الصحيحة

# تشغيل التطبيق
npm run dev
```

### 3. إنشاء Branch جديد

```bash
git checkout -b feature/your-feature-name
# أو
git checkout -b fix/your-bug-fix
```

### 4. تطوير الميزة أو إصلاح الخطأ

- اتبع معايير الكود المحددة أدناه
- أضف تعليقات واضحة باللغة العربية
- اختبر التغييرات محلياً

### 5. Commit التغييرات

```bash
git add .
git commit -m "feat: إضافة ميزة جديدة لإدارة الطلبات"
# أو
git commit -m "fix: إصلاح مشكلة في تسجيل الدخول"
```

### 6. Push إلى GitHub

```bash
git push origin feature/your-feature-name
```

### 7. إنشاء Pull Request

1. اذهب إلى صفحة المشروع الأصلي
2. انقر على "New Pull Request"
3. اختر branch الخاص بك
4. اكتب وصفاً واضحاً للتغييرات

## 📝 معايير الكود

### JavaScript/React

```javascript
// ✅ جيد
const handleSubmit = async (formData) => {
  try {
    const result = await submitForm(formData)
    toast.success('تم الحفظ بنجاح')
    return result
  } catch (error) {
    toast.error('حدث خطأ في الحفظ')
    console.error('Submit error:', error)
  }
}

// ❌ سيء
const handleSubmit = (formData) => {
  submitForm(formData).then(result => {
    alert('تم الحفظ')
  }).catch(err => {
    alert('خطأ')
  })
}
```

### CSS/Tailwind

```jsx
// ✅ جيد - استخدام classes منظمة
<div className="flex items-center justify-between p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
  <h3 className="text-lg font-semibold text-gray-900">العنوان</h3>
</div>

// ❌ سيء - classes غير منظمة
<div className="flex p-6 bg-white items-center border justify-between rounded-lg shadow-sm border-gray-200 hover:shadow-md transition-shadow">
```

### التعليقات

```javascript
// ✅ جيد - تعليقات واضحة بالعربية
// تحديث حالة الطلب وإرسال إشعار للعميل
const updateRequestStatus = async (requestId, newStatus) => {
  // التحقق من صحة البيانات
  if (!requestId || !newStatus) {
    throw new Error('معرف الطلب والحالة الجديدة مطلوبان')
  }
  
  // تحديث قاعدة البيانات
  const result = await database.requests.updateStatus(requestId, newStatus)
  
  // إرسال إشعار للعميل
  await notificationService.sendStatusUpdate(requestId, newStatus)
  
  return result
}

// ❌ سيء - بدون تعليقات أو تعليقات غير واضحة
const updateRequestStatus = async (requestId, newStatus) => {
  const result = await database.requests.updateStatus(requestId, newStatus)
  await notificationService.sendStatusUpdate(requestId, newStatus)
  return result
}
```

## 🧪 الاختبار

### تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
npm run test

# تشغيل الاختبارات مع المراقبة
npm run test:watch

# تشغيل اختبارات التغطية
npm run test:coverage
```

### كتابة اختبارات جديدة

```javascript
// مثال على اختبار مكون
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import LoginPage from '../components/auth/LoginPage'

describe('LoginPage', () => {
  it('يجب أن يعرض نموذج تسجيل الدخول', () => {
    render(<LoginPage />)
    
    expect(screen.getByLabelText('البريد الإلكتروني')).toBeInTheDocument()
    expect(screen.getByLabelText('كلمة المرور')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'تسجيل الدخول' })).toBeInTheDocument()
  })
  
  it('يجب أن يعرض رسالة خطأ عند إدخال بيانات خاطئة', async () => {
    const mockSignIn = vi.fn().mockRejectedValue(new Error('بيانات خاطئة'))
    
    render(<LoginPage signIn={mockSignIn} />)
    
    fireEvent.change(screen.getByLabelText('البريد الإلكتروني'), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText('كلمة المرور'), {
      target: { value: 'wrongpassword' }
    })
    fireEvent.click(screen.getByRole('button', { name: 'تسجيل الدخول' }))
    
    expect(await screen.findByText('بيانات خاطئة')).toBeInTheDocument()
  })
})
```

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ عن خطأ

1. تأكد من أن الخطأ لم يتم الإبلاغ عنه مسبقاً
2. جرب إعادة إنتاج الخطأ
3. تحقق من أن لديك أحدث إصدار

### معلومات مطلوبة في تقرير الخطأ

```markdown
## وصف الخطأ
وصف واضح ومختصر للخطأ.

## خطوات إعادة الإنتاج
1. اذهب إلى '...'
2. انقر على '...'
3. مرر إلى أسفل إلى '...'
4. شاهد الخطأ

## السلوك المتوقع
وصف واضح لما كنت تتوقع حدوثه.

## لقطات الشاشة
إذا كان ذلك مناسباً، أضف لقطات شاشة لتوضيح المشكلة.

## معلومات البيئة
- نظام التشغيل: [مثل iOS]
- المتصفح: [مثل Chrome, Safari]
- إصدار المتصفح: [مثل 22]
- إصدار التطبيق: [مثل 1.0.0]

## معلومات إضافية
أي معلومات أخرى حول المشكلة.
```

## 💡 اقتراح ميزات جديدة

### قبل اقتراح ميزة

1. تأكد من أن الميزة لم يتم اقتراحها مسبقاً
2. فكر في كيفية تحسين تجربة المستخدم
3. تأكد من أن الميزة تتماشى مع أهداف المشروع

### معلومات مطلوبة في اقتراح الميزة

```markdown
## ملخص الميزة
وصف مختصر للميزة المقترحة.

## المشكلة التي تحلها
وصف المشكلة التي ستحلها هذه الميزة.

## الحل المقترح
وصف تفصيلي للحل المقترح.

## البدائل المدروسة
وصف أي حلول بديلة تم النظر فيها.

## معلومات إضافية
أي معلومات أخرى أو لقطات شاشة حول اقتراح الميزة.
```

## 📋 قائمة المراجعة للـ Pull Request

قبل إرسال Pull Request، تأكد من:

- [ ] الكود يتبع معايير المشروع
- [ ] جميع الاختبارات تمر بنجاح
- [ ] تم إضافة اختبارات للميزات الجديدة
- [ ] التعليقات واضحة ومفيدة
- [ ] تم تحديث الوثائق إذا لزم الأمر
- [ ] لا توجد console.log أو تعليقات تطوير
- [ ] تم اختبار التغييرات محلياً
- [ ] العنوان والوصف واضحان

## 🏷️ تسمية Commits

استخدم التنسيق التالي:

```
type(scope): description

feat(auth): إضافة تسجيل دخول بالبصمة
fix(dashboard): إصلاح مشكلة تحميل البيانات
docs(readme): تحديث تعليمات التثبيت
style(ui): تحسين تصميم الأزرار
refactor(api): إعادة هيكلة دوال قاعدة البيانات
test(auth): إضافة اختبارات لتسجيل الدخول
chore(deps): تحديث التبعيات
```

### أنواع Commits

- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تغييرات في الوثائق
- `style`: تغييرات في التصميم (لا تؤثر على المنطق)
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تعديل اختبارات
- `chore`: مهام صيانة

## 🎯 أولويات التطوير

### عالية الأولوية
- إصلاح الأخطاء الأمنية
- إصلاح الأخطاء التي تؤثر على الوظائف الأساسية
- تحسينات الأداء

### متوسطة الأولوية
- ميزات جديدة مطلوبة من المستخدمين
- تحسينات تجربة المستخدم
- تحسينات الكود

### منخفضة الأولوية
- ميزات تجريبية
- تحسينات التصميم
- تحديث التبعيات

## 📞 التواصل

- **GitHub Issues**: للأخطاء واقتراحات الميزات
- **GitHub Discussions**: للأسئلة والنقاشات العامة
- **البريد الإلكتروني**: <EMAIL>

---

شكراً لمساهمتكم في تطوير نماء الاحترافية! 🙏
