# البدء السريع - نماء الاحترافية 🚀

دليل سريع لتشغيل نظام نماء الاحترافية في أقل من 10 دقائق!

## ⚡ التشغيل السريع (للتطوير)

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-username/nama-client-system.git
cd nama-client-system
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد متغيرات البيئة
```bash
cp .env.example .env
```

### 4. تشغيل التطبيق
```bash
npm run dev
```

🎉 **التطبيق جاهز على**: http://localhost:3000

## 🔑 حسابات تجريبية

### الحساب الأول
- **البريد**: `<EMAIL>`
- **كلمة المرور**: `123456`
- **النوع**: جمعية الأمل الخيرية (بريميوم)

### الحساب الثاني
- **البريد**: `<EMAIL>`
- **كلمة المرور**: `123456`
- **النوع**: جمعية روافد التنموية (ذهبي)

## 🗄️ إعداد Supabase (للإنتاج)

### 1. إنشاء مشروع
1. اذهب إلى [supabase.com](https://supabase.com)
2. أنشئ مشروع جديد
3. احفظ URL و API Key

### 2. إعداد قاعدة البيانات
```sql
-- انسخ والصق محتوى ملف supabase-setup.sql
-- في SQL Editor في Supabase
```

### 3. تحديث متغيرات البيئة
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

## 🌐 النشر على Netlify

### الطريقة السريعة
1. ارفع المشروع إلى GitHub
2. اذهب إلى [netlify.com](https://netlify.com)
3. انقر "New site from Git"
4. اختر repository
5. أضف متغيرات البيئة
6. انقر "Deploy"

### إعدادات النشر
- **Build command**: `npm run build`
- **Publish directory**: `dist`

## 📱 الميزات المتاحة حالياً

### ✅ جاهز للاستخدام
- [x] تسجيل الدخول والمصادقة
- [x] واجهة عربية كاملة مع RTL
- [x] تصميم متجاوب
- [x] نظام الإشعارات
- [x] إدارة الحالة مع Zustand

### 🔄 قيد التطوير
- [ ] لوحة التحكم الكاملة
- [ ] إدارة الطلبات
- [ ] نظام المراسلات
- [ ] رفع المستندات
- [ ] التقارير والإحصائيات

## 🛠️ أوامر مفيدة

```bash
# تشغيل التطبيق للتطوير
npm run dev

# بناء التطبيق للإنتاج
npm run build

# معاينة البناء
npm run preview

# فحص الكود
npm run lint

# إصلاح مشاكل الكود
npm run lint:fix

# تشغيل الاختبارات
npm run test
```

## 🎯 الخطوات التالية

### للمطورين
1. راجع ملف `CONTRIBUTING.md` لمعايير المساهمة
2. اطلع على `src/store/useStore.js` لفهم إدارة الحالة
3. ابدأ بتطوير المكونات في `src/components/`

### للمستخدمين
1. راجع ملف `DEPLOYMENT.md` لتعليمات النشر
2. اطلع على `README.md` للوثائق الكاملة
3. تواصل معنا للدعم الفني

## 🆘 حل المشاكل الشائعة

### المشكلة: خطأ في تثبيت التبعيات
```bash
# احذف node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install
```

### المشكلة: التطبيق لا يعمل
```bash
# تأكد من إصدار Node.js
node --version  # يجب أن يكون 18+

# تأكد من متغيرات البيئة
cat .env
```

### المشكلة: خطأ في Supabase
1. تحقق من صحة URL و API Key
2. تأكد من تشغيل SQL setup
3. راجع RLS policies

## 📞 الدعم السريع

### مشاكل تقنية
- **GitHub Issues**: [رابط المشروع]/issues
- **البريد**: <EMAIL>

### استفسارات عامة
- **البريد**: <EMAIL>
- **الهاتف**: +966 50 123 4567

## 🎉 مبروك!

أنت الآن جاهز لاستخدام نظام نماء الاحترافية! 

للمزيد من التفاصيل، راجع:
- 📖 [README.md](README.md) - الوثائق الكاملة
- 🚀 [DEPLOYMENT.md](DEPLOYMENT.md) - دليل النشر
- 🤝 [CONTRIBUTING.md](CONTRIBUTING.md) - دليل المساهمة

---

**تم إعداد هذا الدليل بواسطة فريق نماء الاحترافية** ❤️
