import React, { useState, useEffect } from 'react'
import { 
  FileText, 
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  Clock,
  ChevronRight,
  Settings,
  Activity,
  AlertCircle,
  CheckCircle,
  XCircle,
  Star,
  BarChart3
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const CustomerServiceManagement = ({ customer, onServiceSelect, selectedService }) => {
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all') // all, active, inactive
  const [serviceStats, setServiceStats] = useState({})

  // جلب الخدمات من قاعدة البيانات
  const loadServices = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) throw error
      setServices(data || [])
      
      // جلب إحصائيات كل خدمة للعميل
      if (customer && data) {
        await loadServiceStats(data, customer.id)
      }
    } catch (error) {
      console.error('Error loading services:', error)
      toast.error('حدث خطأ في تحميل الخدمات')
    } finally {
      setLoading(false)
    }
  }

  // جلب إحصائيات الخدمات للعميل (مجموع عناصر كل خدمة)
  const loadServiceStats = async (servicesList, customerId) => {
    try {
      const stats = {}

      for (const service of servicesList) {
        // جلب عناصر الخدمة أولاً
        const { data: serviceItems } = await supabase
          .from('service_items')
          .select('id')
          .eq('service_id', service.id)

        if (serviceItems && serviceItems.length > 0) {
          const itemIds = serviceItems.map(item => item.id)

          // جلب عدد المطالب لجميع عناصر الخدمة
          const { count: requirementsCount } = await supabase
            .from('service_requirements')
            .select('*', { count: 'exact', head: true })
            .in('service_item_id', itemIds)
            .eq('customer_id', customerId)

          // جلب عدد الأحداث لجميع عناصر الخدمة
          const { count: actionsCount } = await supabase
            .from('service_actions')
            .select('*', { count: 'exact', head: true })
            .in('service_item_id', itemIds)
            .eq('customer_id', customerId)

          // جلب عدد مراحل الجدول الزمني لجميع عناصر الخدمة
          const { count: timelineCount } = await supabase
            .from('service_timeline')
            .select('*', { count: 'exact', head: true })
            .in('service_item_id', itemIds)
            .eq('customer_id', customerId)

          stats[service.id] = {
            requirements: requirementsCount || 0,
            actions: actionsCount || 0,
            timeline: timelineCount || 0
          }
        } else {
          stats[service.id] = {
            requirements: 0,
            actions: 0,
            timeline: 0
          }
        }
      }

      setServiceStats(stats)
    } catch (error) {
      console.error('Error loading service stats:', error)
    }
  }

  useEffect(() => {
    if (customer) {
      loadServices()
    }
  }, [customer])

  // فلترة الخدمات
  const filteredServices = services.filter(service => {
    const matchesSearch = 
      service.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = 
      filterStatus === 'all' || 
      (filterStatus === 'active' && service.is_active) ||
      (filterStatus === 'inactive' && !service.is_active)

    return matchesSearch && matchesStatus
  })

  if (!customer) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-full flex items-center justify-center">
        <div className="text-center p-4">
          <FileText className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <h4 className="text-sm font-medium text-gray-900 mb-1">اختر عميل</h4>
          <p className="text-gray-500 text-xs">
            اختر عميل لعرض خدماته
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-full flex flex-col">
      {/* Header مضغوط */}
      <div className="p-3 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-base font-semibold text-gray-900">
              خدمات العميل
            </h3>
            <p className="text-xs text-gray-500 truncate">
              {customer.full_name}
            </p>
          </div>
          <button className="btn btn-primary btn-xs">
            <Plus className="w-3 h-3 ml-1" />
            ربط
          </button>
        </div>

        {/* البحث فقط */}
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
          <input
            type="text"
            placeholder="البحث..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pr-8 pl-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-xs"
          />
        </div>
      </div>

      {/* قائمة الخدمات */}
      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-4">جاري تحميل الخدمات...</p>
          </div>
        ) : filteredServices.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {filteredServices.map((service) => {
              const stats = serviceStats[service.id] || { requirements: 0, actions: 0, timeline: 0 }
              const hasData = stats.requirements > 0 || stats.actions > 0 || stats.timeline > 0
              
              return (
                <div
                  key={service.id}
                  className={`p-4 hover:bg-gray-50 cursor-pointer transition-all duration-200 ${
                    selectedService?.id === service.id ? 'bg-green-50 border-r-4 border-green-500' : ''
                  }`}
                  onClick={() => onServiceSelect && onServiceSelect(service)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      {/* معلومات الخدمة مضغوطة */}
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <FileText className="w-4 h-4 text-green-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-xs font-semibold text-gray-900 truncate">
                            {service.title}
                          </h4>
                          <div className="flex items-center space-x-3 space-x-reverse text-xs text-gray-500">
                            <span>{stats.requirements + stats.actions + stats.timeline} عنصر</span>
                            {hasData && <span className="text-green-600">• نشط</span>}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* حالة وإجراءات */}
                    <div className="flex items-center space-x-1 space-x-reverse">
                      {/* مؤشر وجود البيانات */}
                      <div className={`w-2 h-2 rounded-full ${
                        hasData ? 'bg-green-500' : 'bg-gray-300'
                      }`} title={hasData ? 'يحتوي على بيانات' : 'لا توجد بيانات'}></div>

                      {/* زر الاختيار */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          onServiceSelect && onServiceSelect(service)
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded transition-colors"
                        title="اختيار الخدمة"
                      >
                        <ChevronRight className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="p-8 text-center">
            <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">لا توجد خدمات</h4>
            <p className="text-gray-500 text-sm">
              {searchTerm ? 'لا توجد نتائج تطابق البحث' : 'لم يتم ربط أي خدمات بهذا العميل بعد'}
            </p>
            <button className="mt-4 btn btn-primary btn-sm">
              <Plus className="w-4 h-4 ml-2" />
              ربط خدمة جديدة
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default CustomerServiceManagement
