import React, { useState, useEffect } from 'react'
import {
  BarChart3,
  Users,
  FileText,
  Clock,
  TrendingUp,
  Bell,
  Search,
  Plus,
  Filter,
  Calendar,
  DollarSign,
  CheckCircle,
  AlertCircle,
  XCircle,
  Activity,
  Settings,
  Award,
  Target,
  Star,
  Zap
} from 'lucide-react'
import useStore from '../../store/useStore'
import useSidebar from '../../hooks/useSidebar'
import toast from 'react-hot-toast'
import AdminDashboard from '../admin/AdminDashboard'
import AdvertisementsManager from '../admin/AdvertisementsManager'
import LoadingSpinner from '../ui/LoadingSpinner'
import StatsCard from '../ui/StatsCard'
import ServicesPage from '../services/ServicesPage'
import ServiceItemsPage from '../services/ServiceItemsPage'
import AdminServiceItemsPage from '../services/AdminServiceItemsPage'
import CustomersPage from '../customers/CustomersPage'
import Sidebar from './Sidebar'
import Header from './Header'
import PageHeader from '../layout/PageHeader'
import { supabase } from '../../lib/supabase'

const Dashboard = () => {
  const {
    user,
    notifications,
    unreadCount
  } = useStore()

  const { gridCols } = useSidebar()

  // التحقق من نوع المستخدم
  const isAdmin = user?.account_type === 'admin' || user?.email === '<EMAIL>'

  const [selectedView, setSelectedView] = useState('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedService, setSelectedService] = useState(null)
  const [selectedServiceItem, setSelectedServiceItem] = useState(null)

  const [achievements, setAchievements] = useState([])
  const [loadingAchievements, setLoadingAchievements] = useState(true)
  const [statsCards, setStatsCards] = useState([])
  const [loadingStatsCards, setLoadingStatsCards] = useState(true)

  // تحميل الإنجازات من قاعدة البيانات
  const loadAchievements = async () => {
    try {
      setLoadingAchievements(true)

      let query = supabase
        .from('achievements')
        .select('*')
        .eq('is_active', true)

      // عرض الإنجازات العامة والخاصة بالعميل
      if (!isAdmin) {
        // للعملاء: عرض الإنجازات العامة أو الخاصة بهم
        query = query.or(`is_global.eq.true,customer_id.eq.${user.id}`)
      }

      const { data, error } = await query.order('sort_order', { ascending: true })

      if (error) throw error
      setAchievements(data || [])
    } catch (error) {
      console.error('خطأ في تحميل الإنجازات:', error)
      // استخدام البيانات الافتراضية في حالة الخطأ
      setAchievements([
        {
          id: 1,
          title: 'إكمال 15 مشروع',
          description: 'تم إنجاز 15 مشروع بنجاح خلال هذا الشهر',
          value: '15 مشروع',
          improvement: '+25% عن الشهر الماضي',
          icon: 'CheckCircle',
          color: 'green'
        },
        {
          id: 2,
          title: 'رضا العملاء 98%',
          description: 'تقييم ممتاز من العملاء على جودة الخدمات',
          value: '98%',
          improvement: '+5% تحسن',
          icon: 'Users',
          color: 'blue'
        },
        {
          id: 3,
          title: 'نمو الإيرادات',
          description: 'زيادة 30% في الإيرادات مقارنة بالشهر الماضي',
          value: '450,000 ريال',
          improvement: '+30% نمو',
          icon: 'DollarSign',
          color: 'purple'
        },
        {
          id: 4,
          title: 'تحسين الأداء',
          description: 'تقليل وقت التسليم بنسبة 20%',
          value: 'متوسط 18 يوم',
          improvement: '-20% تحسن',
          icon: 'Activity',
          color: 'yellow'
        }
      ])
    } finally {
      setLoadingAchievements(false)
    }
  }

  // تحميل بطاقات الإحصائيات من قاعدة البيانات
  const loadStatsCards = async () => {
    try {
      setLoadingStatsCards(true)

      let query = supabase
        .from('stats_cards')
        .select('*')
        .eq('is_active', true)

      // عرض البطاقات العامة والخاصة بالعميل
      if (!isAdmin) {
        // للعملاء: عرض البطاقات العامة أو الخاصة بهم
        query = query.or(`is_global.eq.true,customer_id.eq.${user.id}`)
      }

      const { data, error } = await query.order('sort_order', { ascending: true })

      if (error) throw error
      setStatsCards(data || [])
    } catch (error) {
      console.error('خطأ في تحميل بطاقات الإحصائيات:', error)
      // استخدام البيانات الافتراضية في حالة الخطأ
      setStatsCards([
        {
          id: 1,
          title: 'الخدمات المتاحة',
          description: 'عدد الخدمات المتاحة حالياً',
          value: '12',
          trend: '+3 جديدة',
          trend_direction: 'up',
          icon: 'FileText',
          color: 'blue'
        },
        {
          id: 2,
          title: 'العملاء النشطون',
          description: 'عدد العملاء النشطين هذا الشهر',
          value: '24',
          trend: '+8 جدد',
          trend_direction: 'up',
          icon: 'Users',
          color: 'green'
        },
        {
          id: 3,
          title: 'الطلبات المكتملة',
          description: 'عدد الطلبات المنجزة بنجاح',
          value: '156',
          trend: '+12 هذا الشهر',
          trend_direction: 'up',
          icon: 'CheckCircle',
          color: 'purple'
        },
        {
          id: 4,
          title: 'معدل الرضا',
          description: 'معدل رضا العملاء عن الخدمات',
          value: '98.5%',
          trend: '+2.5% تحسن',
          trend_direction: 'up',
          icon: 'TrendingUp',
          color: 'teal'
        }
      ])
    } finally {
      setLoadingStatsCards(false)
    }
  }

  useEffect(() => {
    loadAchievements()
    loadStatsCards()
  }, [user, isAdmin]) // إعادة تحميل البيانات عند تغيير المستخدم أو نوع الحساب

  // الحصول على الأيقونة
  const getAchievementIcon = (iconName) => {
    const icons = {
      CheckCircle,
      Users,
      DollarSign,
      Activity,
      TrendingUp,
      Award
    }
    return icons[iconName] || CheckCircle
  }

  // الحصول على ألوان الإنجاز
  const getAchievementColorClasses = (colorName) => {
    const colors = {
      green: { bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-600' },
      blue: { bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-600' },
      purple: { bg: 'bg-purple-50', border: 'border-purple-200', text: 'text-purple-600' },
      yellow: { bg: 'bg-yellow-50', border: 'border-yellow-200', text: 'text-yellow-600' },
      red: { bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-600' },
      teal: { bg: 'bg-teal-50', border: 'border-teal-200', text: 'text-teal-600' }
    }
    return colors[colorName] || colors.green
  }

  // الحصول على أيقونة بطاقة الإحصائيات
  const getStatsCardIcon = (iconName) => {
    const icons = {
      FileText,
      Users,
      CheckCircle,
      TrendingUp,
      DollarSign,
      Activity,
      Clock,
      Award,
      Target,
      Star,
      Zap,
      BarChart3
    }
    return icons[iconName] || FileText
  }





  // عرض صفحة عناصر الخدمة
  if (selectedService && !selectedServiceItem) {
    return (
      <div className="min-h-screen bg-gray-50 flex" dir="rtl">
        <Sidebar
          selectedView={selectedView}
          onViewChange={setSelectedView}
          unreadCount={unreadCount}
        />
        <div className="flex-1 flex flex-col mr-16">
          <main className="flex-1 overflow-auto">
            {isAdmin ? (
              <AdminServiceItemsPage
                service={selectedService}
                onBack={() => setSelectedService(null)}
              />
            ) : (
              <ServiceItemsPage
                service={selectedService}
                onItemSelect={(item) => setSelectedServiceItem(item)}
                onBack={() => setSelectedService(null)}
              />
            )}
          </main>
        </div>
      </div>
    )
  }

  // ملاحظة: تم حذف ServiceItemDetailsPage - الوظيفة متاحة في ServiceItemsPage

  return (
    <div className="min-h-screen bg-gray-50 flex" dir="rtl">
      {/* الشريط الجانبي */}
      <Sidebar 
        selectedView={selectedView} 
        onViewChange={setSelectedView}
        unreadCount={unreadCount}
      />

      {/* المحتوى الرئيسي - دائماً موسع */}
      <div className="flex-1 flex flex-col mr-16">
        {/* المحتوى */}
        <main className="flex-1 overflow-auto">
          {selectedView === 'overview' && (
            <div className="space-y-6">
              {/* Header الجديد */}
              <PageHeader
                title="الصفحة الرئيسية"
                subtitle="مرحباً بك في نظام إدارة العملاء"
                showUserInfo={true}
              />

              {/* المحتوى الرئيسي */}
              <div className="px-6 space-y-6">
              {/* تقسيم الصفحة إلى قسمين */}
              <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">

                {/* القسم الأيمن: الترحيب */}
                <div className="space-y-6">
                  {/* بطاقة الترحيب */}
                  <div className="bg-gradient-to-r from-blue-600 to-teal-500 rounded-xl p-6 text-white">
                    <h1 className="text-2xl font-bold mb-2">
                      مرحباً بك، {user?.name || user?.organization_name}
                    </h1>
                    <p className="opacity-90">
                      إليك نظرة سريعة على أداء مشاريعك اليوم
                    </p>
                  </div>

                  {/* بطاقات الإحصائيات */}
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">الإحصائيات</h3>
                    {isAdmin && (
                      <button
                        onClick={() => setSelectedView('advertisements')}
                        className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="إدارة الإعلانات"
                      >
                        <Settings className="w-4 h-4" />
                      </button>
                    )}
                  </div>

                  {loadingStatsCards ? (
                    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                      {[1, 2, 3, 4].map((i) => (
                        <div key={i} className="bg-gray-100 rounded-xl p-6 animate-pulse">
                          <div className="h-4 bg-gray-200 rounded mb-2"></div>
                          <div className="h-8 bg-gray-200 rounded mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded"></div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                      {statsCards.map((card) => {
                        const cardIcon = getStatsCardIcon(card.icon)
                        return (
                          <StatsCard
                            key={card.id}
                            title={card.title}
                            value={card.value}
                            description={card.description}
                            icon={cardIcon}
                            color={card.color}
                            trend={card.trend}
                            trendDirection={card.trend_direction}
                          />
                        )
                      })}
                    </div>
                  )}
                </div>

                {/* القسم الأيسر: أبرز الإنجازات */}
                <div className="space-y-6">
                  <div className="bg-white rounded-xl shadow-sm p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-xl font-bold text-gray-900 flex items-center">
                        <TrendingUp className="w-6 h-6 text-green-500 ml-2" />
                        أبرز الإنجازات
                      </h2>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-sm text-gray-500">هذا الشهر</span>
                        {isAdmin && (
                          <button
                            onClick={() => setSelectedView('advertisements')}
                            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                            title="إدارة الإعلانات"
                          >
                            <Settings className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      {loadingAchievements ? (
                        <div className="text-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                          <p className="text-gray-500 mt-2 text-sm">جاري تحميل الإنجازات...</p>
                        </div>
                      ) : achievements.length > 0 ? (
                        achievements.map((achievement) => {
                          const Icon = getAchievementIcon(achievement.icon)
                          const colorClasses = getAchievementColorClasses(achievement.color)

                          return (
                            <div
                              key={achievement.id}
                              className={`flex items-start space-x-4 space-x-reverse p-4 ${colorClasses.bg} rounded-lg border ${colorClasses.border}`}
                            >
                              <div className="flex-shrink-0">
                                <Icon className={`w-6 h-6 ${colorClasses.text}`} />
                              </div>
                              <div className="flex-1">
                                <h3 className="font-semibold text-gray-900">{achievement.title}</h3>
                                <p className="text-sm text-gray-600">{achievement.description}</p>
                                <div className="flex items-center space-x-2 space-x-reverse mt-1">
                                  <span className={`text-xs font-medium ${colorClasses.text}`}>
                                    {achievement.value}
                                  </span>
                                  {achievement.improvement && (
                                    <span className="text-xs text-gray-500">
                                      {achievement.improvement}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          )
                        })
                      ) : (
                        <div className="text-center py-8">
                          <Award className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-base font-medium text-gray-900 mb-2">لا توجد إنجازات</h3>
                          <p className="text-gray-500 text-sm">لم يتم إضافة أي إنجازات بعد</p>
                          {isAdmin && (
                            <button
                              onClick={() => setSelectedView('advertisements')}
                              className="mt-4 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                            >
                              إدارة الإعلانات
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* الأنشطة الحديثة */}
              <div className="bg-white rounded-xl shadow-sm">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-gray-900">
                      الأنشطة الحديثة
                    </h2>
                    <button
                      onClick={() => setSelectedView('services')}
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      عرض الخدمات
                    </button>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="text-center py-8">
                    <div className="text-gray-400 mb-4">
                      <FileText className="w-16 h-16 mx-auto" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">الأنشطة الحديثة</h3>
                    <p className="text-gray-500">ستظهر هنا آخر الأنشطة والتحديثات</p>
                  </div>
                </div>
              </div>
              </div>
            </div>
          )}

          {selectedView === 'services' && (
            <ServicesPage
              onServiceSelect={(service) => setSelectedService(service)}
            />
          )}



          {selectedView === 'admin' && (
            <AdminDashboard />
          )}

          {selectedView === 'customers' && (
            <CustomersPage />
          )}

          {selectedView === 'advertisements' && (
            <div className="space-y-6">
              <PageHeader
                title="إدارة الإعلانات"
                subtitle="إدارة الإنجازات وبطاقات الإحصائيات المخصصة للعملاء"
              />
              <div className="px-6">
                <AdvertisementsManager
                  onClose={() => setSelectedView('overview')}
                  onDataChange={() => {
                    loadAchievements()
                    loadStatsCards()
                  }}
                />
              </div>
            </div>
          )}

          {selectedView === 'profile' && (
            <div className="space-y-6">
              {/* Header الجديد */}
              <PageHeader
                title="الملف الشخصي"
                subtitle="إدارة معلومات حسابك وإعداداتك"
                showUserInfo={true}
              />

              {/* المحتوى الرئيسي */}
              <div className="px-6 space-y-6">

              <div className="bg-white rounded-xl shadow-sm p-6">
                <div className="flex items-center space-x-4 space-x-reverse mb-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-teal-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                    {user?.logo || user?.name?.charAt(0) || 'ن'}
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                      {user?.name || user?.organization_name}
                    </h2>
                    <p className="text-gray-600">{user?.email}</p>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-2 ${
                      user?.membership_level === 'premium' ? 'bg-purple-100 text-purple-800' :
                      user?.membership_level === 'gold' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {user?.membership_level === 'premium' ? 'عضوية مميزة' :
                       user?.membership_level === 'gold' ? 'عضوية ذهبية' :
                       'عضوية أساسية'}
                    </span>
                  </div>
                </div>

                <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اسم المؤسسة
                    </label>
                    <input
                      type="text"
                      value={user?.organization_name || ''}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الشخص المسؤول
                    </label>
                    <input
                      type="text"
                      value={user?.contact_person || ''}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهاتف
                    </label>
                    <input
                      type="text"
                      value={user?.phone || ''}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      العنوان
                    </label>
                    <input
                      type="text"
                      value={user?.address || ''}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                  </div>
                </div>
              </div>
              </div>
            </div>
          )}
        </main>
      </div>


    </div>
  )
}

export default Dashboard
