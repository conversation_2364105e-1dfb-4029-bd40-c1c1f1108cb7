import React, { useState, useEffect } from 'react'
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Settings,
  Users,
  Globe,
  Eye,
  EyeOff,
  Award,
  BarChart3,
  Filter,
  Search,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  DollarSign,
  Activity,
  Target,
  Star,
  Zap,
  FileText
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'
import useStore from '../../store/useStore'

const AdvertisementsManager = ({ onClose, onDataChange }) => {
  const { user } = useStore()
  const [activeTab, setActiveTab] = useState('achievements')
  const [previewCustomerId, setPreviewCustomerId] = useState('')
  const [achievements, setAchievements] = useState([])
  const [statsCards, setStatsCards] = useState([])
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedCustomer, setSelectedCustomer] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [previewAchievements, setPreviewAchievements] = useState([])
  const [previewStatsCards, setPreviewStatsCards] = useState([])
  const [loadingPreview, setLoadingPreview] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    value: '',
    improvement: '',
    icon: '',
    color: 'blue',
    customer_id: '',
    is_global: true,
    visibility: 'global',
    sort_order: 1,
    trend: '',
    trend_direction: 'up'
  })

  // تحميل العملاء
  const loadCustomers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, organization_name, email, account_type')
        .eq('account_type', 'customer')
        .order('name', { ascending: true })

      if (error) {
        console.error('خطأ في تحميل العملاء:', error)
        // استخدام البيانات التجريبية في حالة الخطأ
        const fallbackCustomers = [
          { id: '1', name: 'شركة الأمل للتقنية', organization_name: 'شركة الأمل للتقنية', email: '<EMAIL>' },
          { id: '2', name: 'مؤسسة النور للخدمات', organization_name: 'مؤسسة النور للخدمات', email: '<EMAIL>' },
          { id: '3', name: 'شركة المستقبل الرقمي', organization_name: 'شركة المستقبل الرقمي', email: '<EMAIL>' }
        ]
        setCustomers(fallbackCustomers)
        return
      }

      // معالجة البيانات لضمان وجود الحقول المطلوبة
      const processedCustomers = data?.map(customer => ({
        ...customer,
        display_name: customer.organization_name || customer.name || customer.email
      })) || []

      setCustomers(processedCustomers)
      console.log('تم تحميل العملاء:', processedCustomers)
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error)
      // استخدام البيانات التجريبية في حالة الخطأ
      const fallbackCustomers = [
        { id: '1', name: 'شركة الأمل للتقنية', organization_name: 'شركة الأمل للتقنية', email: '<EMAIL>' },
        { id: '2', name: 'مؤسسة النور للخدمات', organization_name: 'مؤسسة النور للخدمات', email: '<EMAIL>' },
        { id: '3', name: 'شركة المستقبل الرقمي', organization_name: 'شركة المستقبل الرقمي', email: '<EMAIL>' }
      ]
      setCustomers(fallbackCustomers)
    }
  }

  // تحميل الإنجازات
  const loadAchievements = async () => {
    try {
      const { data, error } = await supabase
        .from('achievements')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true })

      if (error) throw error

      // إضافة معلومات العميل يدوياً
      const achievementsWithCustomer = data?.map(achievement => {
        const customer = achievement.customer_id ?
          customers.find(c => c.id === achievement.customer_id) : null
        return {
          ...achievement,
          customer: customer ? {
            ...customer,
            display_name: customer.display_name || customer.organization_name || customer.name
          } : null
        }
      }) || []

      setAchievements(achievementsWithCustomer)
    } catch (error) {
      console.error('خطأ في تحميل الإنجازات:', error)
    }
  }

  // تحميل بطاقات الإحصائيات
  const loadStatsCards = async () => {
    try {
      const { data, error } = await supabase
        .from('stats_cards')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true })

      if (error) throw error

      // إضافة معلومات العميل يدوياً
      const cardsWithCustomer = data?.map(card => {
        const customer = card.customer_id ?
          customers.find(c => c.id === card.customer_id) : null
        return {
          ...card,
          customer: customer ? {
            ...customer,
            display_name: customer.display_name || customer.organization_name || customer.name
          } : null
        }
      }) || []

      setStatsCards(cardsWithCustomer)
    } catch (error) {
      console.error('خطأ في تحميل بطاقات الإحصائيات:', error)
    }
  }

  // تحميل البيانات
  const loadData = async () => {
    setLoading(true)
    // تحميل العملاء أولاً ثم البيانات الأخرى
    await loadCustomers()
    await Promise.all([
      loadAchievements(),
      loadStatsCards()
    ])
    setLoading(false)
  }

  useEffect(() => {
    loadData()
  }, [])

  // فلترة البيانات
  const filteredAchievements = achievements.filter(item => {
    const matchesCustomer = selectedCustomer === 'all' ||
                           (selectedCustomer === 'global' && item.is_global) ||
                           (item.customer_id === selectedCustomer)
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCustomer && matchesSearch
  })

  const filteredStatsCards = statsCards.filter(item => {
    const matchesCustomer = selectedCustomer === 'all' ||
                           (selectedCustomer === 'global' && item.is_global) ||
                           (item.customer_id === selectedCustomer)
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCustomer && matchesSearch
  })

  // حساب إحصائيات الفلترة للتبويب الحالي
  const getCustomerStatsForCurrentTab = (customerId) => {
    if (activeTab === 'achievements') {
      return achievements.filter(item =>
        customerId === 'global' ? item.is_global : item.customer_id === customerId
      ).length
    } else if (activeTab === 'stats') {
      return statsCards.filter(item =>
        customerId === 'global' ? item.is_global : item.customer_id === customerId
      ).length
    }
    return 0
  }

  // حساب العدد الكلي للتبويب الحالي
  const getTotalForCurrentTab = () => {
    if (activeTab === 'achievements') {
      return achievements.length
    } else if (activeTab === 'stats') {
      return statsCards.length
    }
    return 0
  }

  // تغيير رؤية العنصر
  const toggleVisibility = async (id, type, currentVisibility) => {
    try {
      const table = type === 'achievement' ? 'achievements' : 'stats_cards'
      const newVisibility = currentVisibility === 'global' ? 'customer' : 'global'
      const isGlobal = newVisibility === 'global'

      const { error } = await supabase
        .from(table)
        .update({ 
          visibility: newVisibility,
          is_global: isGlobal,
          customer_id: isGlobal ? null : (editingItem?.customer_id || null)
        })
        .eq('id', id)

      if (error) throw error

      toast.success(`تم تحديث الرؤية إلى ${newVisibility === 'global' ? 'عام للجميع' : 'خاص بالعميل'}`)
      loadData()
      if (onDataChange) onDataChange()
    } catch (error) {
      console.error('خطأ في تحديث الرؤية:', error)
      toast.error('فشل في تحديث الرؤية')
    }
  }

  // حذف عنصر
  const deleteItem = async (id, type) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) return

    try {
      const table = type === 'achievement' ? 'achievements' : 'stats_cards'
      const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('تم حذف العنصر بنجاح')
      loadData()
      if (onDataChange) onDataChange()
    } catch (error) {
      console.error('خطأ في حذف العنصر:', error)
      toast.error('فشل في حذف العنصر')
    }
  }

  // إعداد النموذج للإضافة
  const handleAdd = () => {
    setFormData({
      title: '',
      description: '',
      value: '',
      improvement: '',
      icon: activeTab === 'achievements' ? 'CheckCircle' : 'FileText',
      color: 'blue',
      customer_id: '',
      is_global: true,
      visibility: 'global',
      sort_order: 1,
      trend: '',
      trend_direction: 'up'
    })
    setEditingItem(null)
    setShowAddModal(true)
  }

  // إعداد النموذج للتعديل
  const handleEdit = (item) => {
    setFormData({
      title: item.title || '',
      description: item.description || '',
      value: item.value || '',
      improvement: item.improvement || '',
      icon: item.icon || '',
      color: item.color || 'blue',
      customer_id: item.customer_id || '',
      is_global: item.is_global || false,
      visibility: item.visibility || 'customer',
      sort_order: item.sort_order || 1,
      trend: item.trend || '',
      trend_direction: item.trend_direction || 'up'
    })
    setEditingItem(item)
    setShowAddModal(true)
  }

  // حفظ العنصر (إضافة أو تعديل)
  const handleSave = async () => {
    if (!formData.title.trim()) {
      toast.error('يرجى إدخال العنوان')
      return
    }

    if (!formData.value.trim()) {
      toast.error('يرجى إدخال القيمة')
      return
    }

    // التحقق من اختيار العميل إذا كان العنصر خاص
    if (!formData.is_global && !formData.customer_id) {
      toast.error('يرجى اختيار العميل للعنصر الخاص')
      return
    }

    try {
      const table = activeTab === 'achievements' ? 'achievements' : 'stats_cards'
      const data = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        value: formData.value.trim(),
        icon: formData.icon,
        color: formData.color,
        customer_id: formData.is_global ? null : formData.customer_id,
        is_global: formData.is_global,
        visibility: formData.is_global ? 'global' : 'customer',
        sort_order: parseInt(formData.sort_order) || 1,
        is_active: true,
        updated_at: new Date().toISOString()
      }

      // إضافة حقول خاصة بالإنجازات
      if (activeTab === 'achievements') {
        data.improvement = formData.improvement.trim()
      }

      // إضافة حقول خاصة بالإحصائيات
      if (activeTab === 'stats') {
        data.trend = formData.trend.trim()
        data.trend_direction = formData.trend_direction
      }

      // إضافة created_at للعناصر الجديدة
      if (!editingItem) {
        data.created_at = new Date().toISOString()
      }

      console.log('حفظ البيانات:', data) // للتتبع

      let result
      if (editingItem) {
        // تعديل
        result = await supabase
          .from(table)
          .update(data)
          .eq('id', editingItem.id)
          .select()
      } else {
        // إضافة
        result = await supabase
          .from(table)
          .insert([data])
          .select()
      }

      if (result.error) {
        console.error('خطأ في قاعدة البيانات:', result.error)
        throw result.error
      }

      console.log('تم الحفظ بنجاح:', result.data) // للتتبع

      toast.success(editingItem ? 'تم تحديث العنصر بنجاح' : 'تم إضافة العنصر بنجاح')
      setShowAddModal(false)
      setEditingItem(null)

      // إعادة تحميل البيانات
      await loadData()
      if (onDataChange) onDataChange()

    } catch (error) {
      console.error('خطأ في حفظ العنصر:', error)
      toast.error(`فشل في حفظ العنصر: ${error.message || 'خطأ غير معروف'}`)
    }
  }

  // تحميل بيانات المعاينة للعميل المختار
  const loadPreviewData = async (customerId) => {
    if (!customerId) {
      setPreviewAchievements([])
      setPreviewStatsCards([])
      return
    }

    try {
      setLoadingPreview(true)

      // تحميل الإنجازات للمعاينة
      const achievementsQuery = supabase
        .from('achievements')
        .select('*')
        .eq('is_active', true)
        .or(`is_global.eq.true,customer_id.eq.${customerId}`)
        .order('sort_order', { ascending: true })

      // تحميل بطاقات الإحصائيات للمعاينة
      const statsQuery = supabase
        .from('stats_cards')
        .select('*')
        .eq('is_active', true)
        .or(`is_global.eq.true,customer_id.eq.${customerId}`)
        .order('sort_order', { ascending: true })

      const [achievementsResult, statsResult] = await Promise.all([
        achievementsQuery,
        statsQuery
      ])

      if (achievementsResult.error) throw achievementsResult.error
      if (statsResult.error) throw statsResult.error

      setPreviewAchievements(achievementsResult.data || [])
      setPreviewStatsCards(statsResult.data || [])

    } catch (error) {
      console.error('خطأ في تحميل بيانات المعاينة:', error)
      setPreviewAchievements([])
      setPreviewStatsCards([])
    } finally {
      setLoadingPreview(false)
    }
  }

  // تحديث المعاينة عند تغيير العميل المختار
  useEffect(() => {
    if (activeTab === 'preview') {
      loadPreviewData(previewCustomerId)
    }
  }, [previewCustomerId, activeTab])

  // الحصول على الأيقونة
  const getIcon = (iconName) => {
    const icons = {
      CheckCircle,
      Users,
      FileText,
      TrendingUp,
      Award,
      BarChart3,
      DollarSign,
      Activity,
      Target,
      Star,
      Zap
    }
    return icons[iconName] || CheckCircle
  }

  // الحصول على ألوان العنصر
  const getColorClasses = (colorName) => {
    const colors = {
      green: { bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-600', icon: 'text-green-500' },
      blue: { bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-600', icon: 'text-blue-500' },
      purple: { bg: 'bg-purple-50', border: 'border-purple-200', text: 'text-purple-600', icon: 'text-purple-500' },
      yellow: { bg: 'bg-yellow-50', border: 'border-yellow-200', text: 'text-yellow-600', icon: 'text-yellow-500' },
      red: { bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-600', icon: 'text-red-500' },
      teal: { bg: 'bg-teal-50', border: 'border-teal-200', text: 'text-teal-600', icon: 'text-teal-500' }
    }
    return colors[colorName] || colors.blue
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm">
        {/* رأس الصفحة */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">إدارة الإعلانات والمحتوى</h2>
              <p className="text-sm text-gray-600 mt-1">إدارة الإنجازات وبطاقات الإحصائيات المخصصة للعملاء</p>
            </div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="border-b border-gray-200">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('achievements')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'achievements'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Award className="w-4 h-4 inline ml-2" />
              الإنجازات
              {activeTab === 'achievements' && (
                <span className="mr-2 px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs">
                  {achievements.length}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab('stats')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'stats'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <BarChart3 className="w-4 h-4 inline ml-2" />
              بطاقات الإحصائيات
              {activeTab === 'stats' && (
                <span className="mr-2 px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs">
                  {statsCards.length}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab('preview')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'preview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Eye className="w-4 h-4 inline ml-2" />
              معاينة العميل
            </button>
          </nav>
        </div>

        {/* أدوات التحكم */}
        <div className="p-6 border-b border-gray-200">
          {/* مؤشر الفلتر النشط */}
          {selectedCustomer !== 'all' && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Filter className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-blue-800">
                  الفلتر النشط: {
                    selectedCustomer === 'global'
                      ? 'العناصر العامة فقط'
                      : customers.find(c => c.id === selectedCustomer)?.display_name ||
                        customers.find(c => c.id === selectedCustomer)?.organization_name ||
                        customers.find(c => c.id === selectedCustomer)?.name ||
                        'عميل محدد'
                  }
                </span>
                <button
                  onClick={() => setSelectedCustomer('all')}
                  className="text-blue-600 hover:text-blue-800 text-sm underline"
                >
                  إزالة الفلتر
                </button>
              </div>
            </div>
          )}

          <div className="flex flex-col md:flex-row gap-4">
            {/* البحث */}
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في العناصر..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* فلتر العملاء */}
            <div className="relative">
              <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={selectedCustomer}
                onChange={(e) => setSelectedCustomer(e.target.value)}
                className="pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[250px]"
              >
                <option value="all">
                  جميع {activeTab === 'achievements' ? 'الإنجازات' : 'البطاقات'} ({getTotalForCurrentTab()})
                </option>
                <option value="global">
                  {activeTab === 'achievements' ? 'الإنجازات' : 'البطاقات'} العامة فقط ({getCustomerStatsForCurrentTab('global')})
                </option>
                <optgroup label="العملاء">
                  {customers.map((customer) => {
                    const count = getCustomerStatsForCurrentTab(customer.id)
                    return (
                      <option key={customer.id} value={customer.id}>
                        {customer.display_name || customer.organization_name || customer.name} ({count})
                      </option>
                    )
                  })}
                </optgroup>
              </select>
            </div>

            {/* زر الإضافة */}
            <button
              onClick={handleAdd}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="w-4 h-4" />
              <span>إضافة {activeTab === 'achievements' ? 'إنجاز' : 'بطاقة'}</span>
            </button>
          </div>
        </div>

        {/* المحتوى */}
        <div className="p-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-4">جاري تحميل البيانات...</p>
            </div>
          ) : activeTab === 'preview' ? (
            /* تبويب المعاينة */
            <div className="space-y-6">
              {/* اختيار العميل للمعاينة */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <Eye className="w-5 h-5 text-blue-600" />
                  <div className="flex-1">
                    <h3 className="font-medium text-blue-900">معاينة إعلانات العميل</h3>
                    <p className="text-sm text-blue-700 mt-1">اختر عميل لمعاينة الإعلانات التي ستظهر له في الصفحة الرئيسية</p>
                  </div>
                </div>
                <div className="mt-4">
                  <select
                    value={previewCustomerId}
                    onChange={(e) => setPreviewCustomerId(e.target.value)}
                    className="w-full md:w-auto px-4 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                  >
                    <option value="">اختر عميل للمعاينة</option>
                    {customers.map((customer) => (
                      <option key={customer.id} value={customer.id}>
                        {customer.display_name || customer.organization_name || customer.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {previewCustomerId ? (
                loadingPreview ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-gray-500 mt-4">جاري تحميل المعاينة...</p>
                  </div>
                ) : (
                  <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
                    {/* معاينة الإنجازات */}
                    <div className="bg-white border border-gray-200 rounded-xl p-6">
                      <div className="flex items-center space-x-2 space-x-reverse mb-4">
                        <Award className="w-5 h-5 text-green-600" />
                        <h3 className="text-lg font-semibold text-gray-900">أبرز الإنجازات</h3>
                        <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                          {previewAchievements.length} إنجاز
                        </span>
                      </div>
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {previewAchievements.length > 0 ? (
                          previewAchievements.map((achievement) => {
                            const Icon = getIcon(achievement.icon)
                            const colorClasses = getColorClasses(achievement.color)
                            return (
                              <div
                                key={achievement.id}
                                className={`flex items-start space-x-3 space-x-reverse p-3 ${colorClasses.bg} rounded-lg border ${colorClasses.border}`}
                              >
                                <div className="flex-shrink-0">
                                  <Icon className={`w-5 h-5 ${colorClasses.icon}`} />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-medium text-gray-900 text-sm">{achievement.title}</h4>
                                  <p className="text-xs text-gray-600 mt-1">{achievement.description}</p>
                                  <div className="flex items-center space-x-2 space-x-reverse mt-2">
                                    <span className={`text-xs font-medium ${colorClasses.text}`}>
                                      {achievement.value}
                                    </span>
                                    {achievement.improvement && (
                                      <span className="text-xs text-gray-500">
                                        {achievement.improvement}
                                      </span>
                                    )}
                                    <span className={`px-1.5 py-0.5 rounded text-xs ${
                                      achievement.is_global
                                        ? 'bg-green-100 text-green-700'
                                        : 'bg-blue-100 text-blue-700'
                                    }`}>
                                      {achievement.is_global ? 'عام' : 'خاص'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            )
                          })
                        ) : (
                          <div className="text-center py-8">
                            <Award className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                            <p className="text-gray-500 text-sm">لا توجد إنجازات لهذا العميل</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* معاينة بطاقات الإحصائيات */}
                    <div className="bg-white border border-gray-200 rounded-xl p-6">
                      <div className="flex items-center space-x-2 space-x-reverse mb-4">
                        <BarChart3 className="w-5 h-5 text-blue-600" />
                        <h3 className="text-lg font-semibold text-gray-900">الإحصائيات</h3>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          {previewStatsCards.length} بطاقة
                        </span>
                      </div>
                      <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 max-h-96 overflow-y-auto">
                        {previewStatsCards.length > 0 ? (
                          previewStatsCards.map((card) => {
                            const Icon = getIcon(card.icon)
                            const colorClasses = getColorClasses(card.color)
                            return (
                              <div
                                key={card.id}
                                className={`p-4 ${colorClasses.bg} rounded-lg border ${colorClasses.border} relative`}
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center space-x-2 space-x-reverse mb-2">
                                      <Icon className={`w-4 h-4 ${colorClasses.icon}`} />
                                      <h4 className="font-medium text-gray-900 text-sm">{card.title}</h4>
                                    </div>
                                    <div className={`text-lg font-bold ${colorClasses.text} mb-1`}>
                                      {card.value}
                                    </div>
                                    {card.trend && (
                                      <div className="text-xs text-gray-600">
                                        {card.trend}
                                      </div>
                                    )}
                                  </div>
                                  <span className={`px-1.5 py-0.5 rounded text-xs ${
                                    card.is_global
                                      ? 'bg-green-100 text-green-700'
                                      : 'bg-blue-100 text-blue-700'
                                  }`}>
                                    {card.is_global ? 'عام' : 'خاص'}
                                  </span>
                                </div>
                              </div>
                            )
                          })
                        ) : (
                          <div className="col-span-2 text-center py-8">
                            <BarChart3 className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                            <p className="text-gray-500 text-sm">لا توجد بطاقات إحصائيات لهذا العميل</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )
              ) : (
                <div className="text-center py-12">
                  <Eye className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">اختر عميل للمعاينة</h3>
                  <p className="text-gray-500">اختر عميل من القائمة أعلاه لمعاينة الإعلانات التي ستظهر له</p>
                </div>
              )}
            </div>
          ) : activeTab === 'achievements' ? (
            /* عرض الإنجازات */
            <div className="space-y-4">
              {/* عداد النتائج */}
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">الإنجازات</h3>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                    {filteredAchievements.length} من {achievements.length}
                  </span>
                  {selectedCustomer !== 'all' && (
                    <span className="text-xs text-gray-500">مفلتر</span>
                  )}
                </div>
              </div>

              {filteredAchievements.length > 0 ? (
                filteredAchievements.map((achievement) => (
                  <div
                    key={achievement.id}
                    className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 space-x-reverse mb-2">
                          <h3 className="font-semibold text-gray-900">{achievement.title}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            achievement.is_global 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {achievement.is_global ? 'عام' : 'خاص'}
                          </span>
                          {achievement.customer && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              {achievement.customer.display_name || achievement.customer.organization_name || achievement.customer.name}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{achievement.description}</p>
                        <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                          <span>القيمة: {achievement.value || 'غير محدد'}</span>
                          <span>الترتيب: {achievement.sort_order}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => toggleVisibility(achievement.id, 'achievement', achievement.visibility)}
                          className={`p-2 rounded-lg transition-colors ${
                            achievement.is_global 
                              ? 'text-green-600 hover:bg-green-50' 
                              : 'text-blue-600 hover:bg-blue-50'
                          }`}
                          title={achievement.is_global ? 'جعله خاص' : 'جعله عام'}
                        >
                          {achievement.is_global ? <Globe className="w-4 h-4" /> : <Users className="w-4 h-4" />}
                        </button>
                        <button
                          onClick={() => handleEdit(achievement)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          title="تعديل"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => deleteItem(achievement.id, 'achievement')}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12">
                  <Award className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد إنجازات</h3>
                  <p className="text-gray-500">لم يتم العثور على إنجازات تطابق المعايير المحددة</p>
                </div>
              )}
            </div>
          ) : (
            /* عرض بطاقات الإحصائيات */
            <div className="space-y-4">
              {/* عداد النتائج */}
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">بطاقات الإحصائيات</h3>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                    {filteredStatsCards.length} من {statsCards.length}
                  </span>
                  {selectedCustomer !== 'all' && (
                    <span className="text-xs text-gray-500">مفلتر</span>
                  )}
                </div>
              </div>

              {filteredStatsCards.length > 0 ? (
                filteredStatsCards.map((card) => (
                  <div
                    key={card.id}
                    className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 space-x-reverse mb-2">
                          <h3 className="font-semibold text-gray-900">{card.title}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            card.is_global 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {card.is_global ? 'عام' : 'خاص'}
                          </span>
                          {card.customer && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              {card.customer.display_name || card.customer.organization_name || card.customer.name}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{card.description}</p>
                        <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                          <span>القيمة: {card.value}</span>
                          <span>الاتجاه: {card.trend || 'غير محدد'}</span>
                          <span>الترتيب: {card.sort_order}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => toggleVisibility(card.id, 'stats', card.visibility)}
                          className={`p-2 rounded-lg transition-colors ${
                            card.is_global 
                              ? 'text-green-600 hover:bg-green-50' 
                              : 'text-blue-600 hover:bg-blue-50'
                          }`}
                          title={card.is_global ? 'جعله خاص' : 'جعله عام'}
                        >
                          {card.is_global ? <Globe className="w-4 h-4" /> : <Users className="w-4 h-4" />}
                        </button>
                        <button
                          onClick={() => handleEdit(card)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          title="تعديل"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => deleteItem(card.id, 'stats')}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12">
                  <BarChart3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بطاقات إحصائيات</h3>
                  <p className="text-gray-500">لم يتم العثور على بطاقات تطابق المعايير المحددة</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* نموذج الإضافة/التعديل */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  {editingItem ? 'تعديل' : 'إضافة'} {activeTab === 'achievements' ? 'إنجاز' : 'بطاقة إحصائيات'}
                </h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              {/* العنوان */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  العنوان *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل العنوان"
                />
              </div>

              {/* الوصف */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الوصف
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل الوصف"
                />
              </div>

              {/* القيمة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  القيمة *
                </label>
                <input
                  type="text"
                  value={formData.value}
                  onChange={(e) => setFormData({...formData, value: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="مثال: 15 مشروع، 98%، 450,000 ريال"
                />
              </div>

              {/* حقول خاصة بالإنجازات */}
              {activeTab === 'achievements' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    التحسن
                  </label>
                  <input
                    type="text"
                    value={formData.improvement}
                    onChange={(e) => setFormData({...formData, improvement: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: +25% عن الشهر الماضي"
                  />
                </div>
              )}

              {/* حقول خاصة بالإحصائيات */}
              {activeTab === 'stats' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاتجاه
                    </label>
                    <input
                      type="text"
                      value={formData.trend}
                      onChange={(e) => setFormData({...formData, trend: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثال: +3 جديدة"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اتجاه الاتجاه
                    </label>
                    <select
                      value={formData.trend_direction}
                      onChange={(e) => setFormData({...formData, trend_direction: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="up">صاعد</option>
                      <option value="down">نازل</option>
                      <option value="stable">مستقر</option>
                    </select>
                  </div>
                </div>
              )}

              {/* الأيقونة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الأيقونة
                </label>
                <select
                  value={formData.icon}
                  onChange={(e) => setFormData({...formData, icon: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {activeTab === 'achievements' ? (
                    <>
                      <option value="CheckCircle">علامة صح</option>
                      <option value="Users">المستخدمين</option>
                      <option value="DollarSign">الدولار</option>
                      <option value="Activity">النشاط</option>
                      <option value="TrendingUp">الاتجاه الصاعد</option>
                      <option value="Award">الجائزة</option>
                    </>
                  ) : (
                    <>
                      <option value="FileText">ملف نصي</option>
                      <option value="Users">المستخدمين</option>
                      <option value="CheckCircle">علامة صح</option>
                      <option value="TrendingUp">الاتجاه الصاعد</option>
                      <option value="BarChart3">الرسم البياني</option>
                      <option value="Target">الهدف</option>
                    </>
                  )}
                </select>
              </div>

              {/* اللون */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اللون
                </label>
                <select
                  value={formData.color}
                  onChange={(e) => setFormData({...formData, color: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="blue">أزرق</option>
                  <option value="green">أخضر</option>
                  <option value="purple">بنفسجي</option>
                  <option value="yellow">أصفر</option>
                  <option value="red">أحمر</option>
                  <option value="teal">تركوازي</option>
                </select>
              </div>

              {/* الرؤية */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الرؤية
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="visibility"
                      checked={formData.is_global}
                      onChange={() => setFormData({...formData, is_global: true, customer_id: ''})}
                      className="mr-2"
                    />
                    <span>عام للجميع</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="visibility"
                      checked={!formData.is_global}
                      onChange={() => setFormData({...formData, is_global: false})}
                      className="mr-2"
                    />
                    <span>خاص بعميل محدد</span>
                  </label>
                </div>
              </div>

              {/* اختيار العميل */}
              {!formData.is_global && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    العميل
                  </label>
                  <select
                    value={formData.customer_id}
                    onChange={(e) => setFormData({...formData, customer_id: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">اختر العميل</option>
                    {customers.map((customer) => (
                      <option key={customer.id} value={customer.id}>
                        {customer.display_name || customer.organization_name || customer.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* ترتيب العرض */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ترتيب العرض
                </label>
                <input
                  type="number"
                  value={formData.sort_order}
                  onChange={(e) => setFormData({...formData, sort_order: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  min="1"
                />
              </div>
            </div>

            {/* أزرار الحفظ والإلغاء */}
            <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-3 space-x-reverse">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <Save className="w-4 h-4" />
                <span>{editingItem ? 'تحديث' : 'إضافة'}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdvertisementsManager
