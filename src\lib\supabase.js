import { createClient } from '@supabase/supabase-js'

// إعدادات Supabase
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key'

// تسجيل إعدادات Supabase للتأكد من صحتها
console.log('🔧 Supabase Configuration:')
console.log('URL:', supabaseUrl)
console.log('Anon Key:', supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'Not found')
console.log('Environment:', import.meta.env.MODE)

// إنشاء عميل Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// دوال مساعدة للمصادقة
export const auth = {
  // تسجيل الدخول
  signIn: async (email, password) => {
    try {
      console.log('🔐 Attempting login with:', email)

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      console.log('🔐 Login response:', { data: !!data, error: error?.message })

      if (error) throw error
      return { user: data.user, session: data.session, error: null }
    } catch (error) {
      console.error('🔐 Login error:', error)
      return { user: null, session: null, error: error.message }
    }
  },

  // تسجيل الخروج
  signOut: async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      return { error: null }
    } catch (error) {
      return { error: error.message }
    }
  },

  // الحصول على المستخدم الحالي
  getCurrentUser: async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      if (error) throw error
      return { user, error: null }
    } catch (error) {
      return { user: null, error: error.message }
    }
  },

  // الاستماع لتغييرات المصادقة
  onAuthStateChange: (callback) => {
    return supabase.auth.onAuthStateChange(callback)
  }
}

// دوال قاعدة البيانات
export const database = {
  // المستخدمين
  users: {
    // الحصول على بيانات المستخدم
    getProfile: async (userId) => {
      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .single()
        
        if (error) throw error
        return { data, error: null }
      } catch (error) {
        return { data: null, error: error.message }
      }
    },

    // تحديث بيانات المستخدم
    updateProfile: async (userId, updates) => {
      try {
        const { data, error } = await supabase
          .from('users')
          .update(updates)
          .eq('id', userId)
          .select()
          .single()
        
        if (error) throw error
        return { data, error: null }
      } catch (error) {
        return { data: null, error: error.message }
      }
    }
  },

  // الطلبات
  requests: {
    // الحصول على طلبات المستخدم
    getUserRequests: async (userId) => {
      try {
        const { data, error } = await supabase
          .from('requests')
          .select(`
            *,
            timeline:request_timeline(*),
            communications:request_communications(*),
            documents:request_documents(*)
          `)
          .eq('client_id', userId)
          .order('created_at', { ascending: false })
        
        if (error) throw error
        return { data, error: null }
      } catch (error) {
        return { data: null, error: error.message }
      }
    },

    // الحصول على طلب محدد
    getRequest: async (requestId) => {
      try {
        const { data, error } = await supabase
          .from('requests')
          .select(`
            *,
            timeline:request_timeline(*),
            communications:request_communications(*),
            documents:request_documents(*),
            team:request_team(*)
          `)
          .eq('id', requestId)
          .single()
        
        if (error) throw error
        return { data, error: null }
      } catch (error) {
        return { data: null, error: error.message }
      }
    },

    // تحديث حالة الطلب
    updateStatus: async (requestId, status) => {
      try {
        const { data, error } = await supabase
          .from('requests')
          .update({ status, updated_at: new Date().toISOString() })
          .eq('id', requestId)
          .select()
          .single()
        
        if (error) throw error
        return { data, error: null }
      } catch (error) {
        return { data: null, error: error.message }
      }
    }
  },

  // المستندات
  documents: {
    // رفع مستند
    uploadDocument: async (requestId, file, metadata = {}) => {
      try {
        // رفع الملف إلى Storage
        const fileName = `${requestId}/${Date.now()}_${file.name}`
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('documents')
          .upload(fileName, file)
        
        if (uploadError) throw uploadError

        // حفظ معلومات المستند في قاعدة البيانات
        const { data, error } = await supabase
          .from('request_documents')
          .insert({
            request_id: requestId,
            name: file.name,
            file_path: uploadData.path,
            size: file.size,
            type: file.type,
            ...metadata
          })
          .select()
          .single()
        
        if (error) throw error
        return { data, error: null }
      } catch (error) {
        return { data: null, error: error.message }
      }
    },

    // الحصول على رابط تحميل المستند
    getDownloadUrl: async (filePath) => {
      try {
        const { data, error } = await supabase.storage
          .from('documents')
          .createSignedUrl(filePath, 3600) // صالح لساعة واحدة
        
        if (error) throw error
        return { url: data.signedUrl, error: null }
      } catch (error) {
        return { url: null, error: error.message }
      }
    }
  },

  // الإشعارات
  notifications: {
    // الحصول على إشعارات المستخدم
    getUserNotifications: async (userId) => {
      try {
        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(50)
        
        if (error) throw error
        return { data, error: null }
      } catch (error) {
        return { data: null, error: error.message }
      }
    },

    // إضافة إشعار جديد
    addNotification: async (notification) => {
      try {
        const { data, error } = await supabase
          .from('notifications')
          .insert(notification)
          .select()
          .single()
        
        if (error) throw error
        return { data, error: null }
      } catch (error) {
        return { data: null, error: error.message }
      }
    },

    // تحديد الإشعار كمقروء
    markAsRead: async (notificationId) => {
      try {
        const { data, error } = await supabase
          .from('notifications')
          .update({ read: true })
          .eq('id', notificationId)
          .select()
          .single()
        
        if (error) throw error
        return { data, error: null }
      } catch (error) {
        return { data: null, error: error.message }
      }
    }
  }
}

// دوال الاشتراكات المباشرة (Realtime)
export const realtime = {
  // الاستماع لتحديثات الطلبات
  subscribeToRequests: (userId, callback) => {
    return supabase
      .channel('requests')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'requests',
        filter: `client_id=eq.${userId}`
      }, callback)
      .subscribe()
  },

  // الاستماع لتحديثات الإشعارات
  subscribeToNotifications: (userId, callback) => {
    return supabase
      .channel('notifications')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${userId}`
      }, callback)
      .subscribe()
  },

  // إلغاء الاشتراك
  unsubscribe: (subscription) => {
    return supabase.removeChannel(subscription)
  }
}

export default supabase
