import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import { DATABASE_CONFIG } from '../../config/database'
import { 
  Database, 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  Users, 
  FileText, 
  MessageSquare,
  Bell,
  Activity
} from 'lucide-react'

const DatabaseTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('testing')
  const [tableStats, setTableStats] = useState({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    testConnection()
  }, [])

  const testConnection = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // اختبار الاتصال
      const { data, error } = await supabase
        .from('users')
        .select('count', { count: 'exact', head: true })

      if (error) {
        throw error
      }

      setConnectionStatus('connected')
      await loadTableStats()
      
    } catch (err) {
      setConnectionStatus('error')
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const loadTableStats = async () => {
    try {
      const stats = {}
      
      // إحصائيات المستخدمين
      const { count: usersCount } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
      stats.users = usersCount || 0

      // إحصائيات الطلبات
      const { count: ordersCount } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
      stats.orders = ordersCount || 0

      // إحصائيات المهام
      const { count: tasksCount } = await supabase
        .from('tasks')
        .select('*', { count: 'exact', head: true })
      stats.tasks = tasksCount || 0

      // إحصائيات الاتصالات
      const { count: communicationsCount } = await supabase
        .from('communications')
        .select('*', { count: 'exact', head: true })
      stats.communications = communicationsCount || 0

      setTableStats(stats)
      
    } catch (err) {
      console.error('خطأ في تحميل الإحصائيات:', err)
    }
  }

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="w-6 h-6 text-green-600" />
      case 'error':
        return <XCircle className="w-6 h-6 text-red-600" />
      default:
        return <RefreshCw className="w-6 h-6 text-blue-600 animate-spin" />
    }
  }

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'متصل بنجاح'
      case 'error':
        return 'خطأ في الاتصال'
      default:
        return 'جاري الاختبار...'
    }
  }

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200'
    }
  }

  return (
    <div className="space-y-6">
      {/* حالة الاتصال */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-large flex items-center">
            <Database className="w-6 h-6 ml-2" />
            اختبار قاعدة البيانات
          </h2>
          <button
            onClick={testConnection}
            disabled={loading}
            className="btn btn-ghost btn-sm"
          >
            <RefreshCw className={`w-4 h-4 ml-2 ${loading ? 'animate-spin' : ''}`} />
            إعادة اختبار
          </button>
        </div>

        <div className={`flex items-center space-x-3 space-x-reverse p-4 rounded-xl border ${getStatusColor()}`}>
          {getStatusIcon()}
          <div>
            <p className="font-medium">{getStatusText()}</p>
            {error && (
              <p className="text-sm mt-1 opacity-75">{error}</p>
            )}
          </div>
        </div>
      </div>

      {/* معلومات التكوين */}
      <div className="card p-6">
        <h3 className="text-title mb-4">معلومات التكوين</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50/60 rounded-xl p-4">
            <p className="text-caption mb-1">وضع قاعدة البيانات</p>
            <p className="text-subtitle font-semibold">
              {DATABASE_CONFIG.USE_DEMO_DATA ? 'بيانات تجريبية' : 'Supabase'}
            </p>
          </div>
          <div className="bg-gray-50/60 rounded-xl p-4">
            <p className="text-caption mb-1">رابط Supabase</p>
            <p className="text-subtitle font-semibold">
              {DATABASE_CONFIG.SUPABASE_URL ? '✅ مكتمل' : '❌ مفقود'}
            </p>
          </div>
          <div className="bg-gray-50/60 rounded-xl p-4">
            <p className="text-caption mb-1">مفتاح Supabase</p>
            <p className="text-subtitle font-semibold">
              {DATABASE_CONFIG.SUPABASE_ANON_KEY ? '✅ مكتمل' : '❌ مفقود'}
            </p>
          </div>
          <div className="bg-gray-50/60 rounded-xl p-4">
            <p className="text-caption mb-1">حالة الاتصال</p>
            <p className="text-subtitle font-semibold">
              {connectionStatus === 'connected' ? '✅ متصل' : 
               connectionStatus === 'error' ? '❌ خطأ' : '⏳ اختبار'}
            </p>
          </div>
        </div>
      </div>

      {/* إحصائيات الجداول */}
      {connectionStatus === 'connected' && (
        <div className="card p-6">
          <h3 className="text-title mb-4">إحصائيات الجداول</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center bg-blue-50/60 rounded-xl p-4">
              <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {tableStats.users || 0}
              </div>
              <div className="text-caption">المستخدمين</div>
            </div>
            
            <div className="text-center bg-green-50/60 rounded-xl p-4">
              <FileText className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-600 mb-1">
                {tableStats.orders || 0}
              </div>
              <div className="text-caption">الطلبات</div>
            </div>
            
            <div className="text-center bg-purple-50/60 rounded-xl p-4">
              <Activity className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-600 mb-1">
                {tableStats.tasks || 0}
              </div>
              <div className="text-caption">المهام</div>
            </div>
            
            <div className="text-center bg-orange-50/60 rounded-xl p-4">
              <MessageSquare className="w-8 h-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-orange-600 mb-1">
                {tableStats.communications || 0}
              </div>
              <div className="text-caption">الاتصالات</div>
            </div>
          </div>
        </div>
      )}

      {/* الجداول المتاحة */}
      <div className="card p-6">
        <h3 className="text-title mb-4">الجداول المتاحة</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {Object.entries(DATABASE_CONFIG.TABLES).map(([key, tableName]) => (
            <div key={key} className="flex items-center space-x-2 space-x-reverse p-3 bg-gray-50/60 rounded-lg">
              <Database className="w-4 h-4 text-gray-500" />
              <span className="text-body font-medium">{tableName}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default DatabaseTest
