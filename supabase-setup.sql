-- ==================== إعداد قاعدة البيانات - نماء الاحترافية ====================
-- هذا الملف يحتوي على جميع الجداول والسياسات المطلوبة للنظام

-- ==================== إنشاء الجداول ====================

-- جدول المستخدمين (العملاء)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    contact_person TEXT,
    phone TEXT,
    organization_name TEXT,
    logo TEXT DEFAULT '🏢',
    established_date DATE,
    membership_level TEXT DEFAULT 'basic' CHECK (membership_level IN ('basic', 'gold', 'premium')),
    membership_expiry DATE,
    address TEXT,
    registration_number TEXT,
    vat_number TEXT,
    bank_account TEXT,
    website TEXT,
    social_media JSONB DEFAULT '{}',
    preferences JSONB DEFAULT '{}',
    stats JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الخدمات
CREATE TABLE IF NOT EXISTS public.services (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    icon TEXT DEFAULT '📋',
    category TEXT NOT NULL,
    description TEXT,
    detailed_description TEXT,
    features TEXT[],
    duration TEXT,
    starting_price INTEGER,
    includes TEXT[],
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الطلبات
CREATE TABLE IF NOT EXISTS public.requests (
    id TEXT PRIMARY KEY,
    client_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    service_id TEXT REFERENCES public.services(id),
    service_name TEXT NOT NULL,
    service_icon TEXT DEFAULT '📋',
    category TEXT,
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'action-required', 'on-track', 'delayed', 'completed', 'cancelled')),
    health TEXT DEFAULT 'good' CHECK (health IN ('good', 'at-risk', 'critical')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    current_step TEXT,
    current_step_start_date DATE,
    submission_date DATE DEFAULT CURRENT_DATE,
    expected_completion DATE,
    actual_completion DATE,
    contract_info JSONB DEFAULT '{}',
    budget_info JSONB DEFAULT '{}',
    next_action JSONB,
    satisfaction JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول مراحل العمل
CREATE TABLE IF NOT EXISTS public.request_timeline (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id TEXT REFERENCES public.requests(id) ON DELETE CASCADE,
    step_number INTEGER NOT NULL,
    step_name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'current', 'completed', 'skipped')),
    start_date DATE,
    completion_date DATE,
    expected_duration TEXT,
    actual_duration TEXT,
    completed_by TEXT,
    deliverables TEXT[],
    subtasks JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(request_id, step_number)
);

-- جدول فريق العمل
CREATE TABLE IF NOT EXISTS public.request_team (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id TEXT REFERENCES public.requests(id) ON DELETE CASCADE,
    employee_id TEXT NOT NULL,
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    avatar TEXT DEFAULT '👤',
    is_lead BOOLEAN DEFAULT FALSE,
    assigned_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المراسلات
CREATE TABLE IF NOT EXISTS public.request_communications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id TEXT REFERENCES public.requests(id) ON DELETE CASCADE,
    from_type TEXT NOT NULL CHECK (from_type IN ('client', 'support', 'system')),
    from_name TEXT NOT NULL,
    from_avatar TEXT,
    to_type TEXT NOT NULL CHECK (to_type IN ('client', 'support', 'system')),
    to_name TEXT NOT NULL,
    subject TEXT,
    message TEXT NOT NULL,
    attachments TEXT[],
    is_read BOOLEAN DEFAULT FALSE,
    is_important BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المستندات
CREATE TABLE IF NOT EXISTS public.request_documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id TEXT REFERENCES public.requests(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    file_path TEXT,
    file_url TEXT,
    size_bytes INTEGER,
    file_type TEXT,
    category TEXT DEFAULT 'general',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'needs-revision')),
    uploaded_by TEXT,
    upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    version TEXT DEFAULT 'v1.0',
    checksum TEXT,
    is_required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول التحديثات
CREATE TABLE IF NOT EXISTS public.request_updates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id TEXT REFERENCES public.requests(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    category TEXT DEFAULT 'general',
    author TEXT,
    details TEXT,
    attachments_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الإشعارات
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error', 'action-required')),
    title TEXT NOT NULL,
    message TEXT,
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    related_request_id TEXT REFERENCES public.requests(id) ON DELETE SET NULL,
    action_url TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المخاطر
CREATE TABLE IF NOT EXISTS public.request_risks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id TEXT REFERENCES public.requests(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    impact TEXT DEFAULT 'medium' CHECK (impact IN ('low', 'medium', 'high')),
    probability TEXT DEFAULT 'medium' CHECK (probability IN ('low', 'medium', 'high')),
    mitigation TEXT,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'mitigated', 'occurred', 'closed')),
    identified_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==================== إنشاء الفهارس ====================

CREATE INDEX IF NOT EXISTS idx_requests_client_id ON public.requests(client_id);
CREATE INDEX IF NOT EXISTS idx_requests_status ON public.requests(status);
CREATE INDEX IF NOT EXISTS idx_requests_priority ON public.requests(priority);
CREATE INDEX IF NOT EXISTS idx_requests_created_at ON public.requests(created_at);

CREATE INDEX IF NOT EXISTS idx_timeline_request_id ON public.request_timeline(request_id);
CREATE INDEX IF NOT EXISTS idx_timeline_status ON public.request_timeline(status);

CREATE INDEX IF NOT EXISTS idx_communications_request_id ON public.request_communications(request_id);
CREATE INDEX IF NOT EXISTS idx_communications_created_at ON public.request_communications(created_at);

CREATE INDEX IF NOT EXISTS idx_documents_request_id ON public.request_documents(request_id);
CREATE INDEX IF NOT EXISTS idx_documents_status ON public.request_documents(status);

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at);

-- ==================== تفعيل Row Level Security ====================

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_timeline ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_team ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_risks ENABLE ROW LEVEL SECURITY;

-- ==================== إنشاء السياسات الأمنية ====================

-- سياسات المستخدمين
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- سياسات الخدمات (قراءة فقط للجميع)
CREATE POLICY "Services are viewable by authenticated users" ON public.services
    FOR SELECT USING (auth.role() = 'authenticated');

-- سياسات الطلبات
CREATE POLICY "Users can view own requests" ON public.requests
    FOR SELECT USING (auth.uid() = client_id);

CREATE POLICY "Users can update own requests" ON public.requests
    FOR UPDATE USING (auth.uid() = client_id);

-- سياسات مراحل العمل
CREATE POLICY "Users can view timeline of own requests" ON public.request_timeline
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.requests 
            WHERE id = request_timeline.request_id 
            AND client_id = auth.uid()
        )
    );

-- سياسات فريق العمل
CREATE POLICY "Users can view team of own requests" ON public.request_team
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.requests 
            WHERE id = request_team.request_id 
            AND client_id = auth.uid()
        )
    );

-- سياسات المراسلات
CREATE POLICY "Users can view communications of own requests" ON public.request_communications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.requests 
            WHERE id = request_communications.request_id 
            AND client_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert communications for own requests" ON public.request_communications
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.requests 
            WHERE id = request_communications.request_id 
            AND client_id = auth.uid()
        )
    );

-- سياسات المستندات
CREATE POLICY "Users can view documents of own requests" ON public.request_documents
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.requests 
            WHERE id = request_documents.request_id 
            AND client_id = auth.uid()
        )
    );

CREATE POLICY "Users can upload documents for own requests" ON public.request_documents
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.requests 
            WHERE id = request_documents.request_id 
            AND client_id = auth.uid()
        )
    );

-- سياسات التحديثات
CREATE POLICY "Users can view updates of own requests" ON public.request_updates
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.requests 
            WHERE id = request_updates.request_id 
            AND client_id = auth.uid()
        )
    );

-- سياسات الإشعارات
CREATE POLICY "Users can view own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- سياسات المخاطر
CREATE POLICY "Users can view risks of own requests" ON public.request_risks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.requests 
            WHERE id = request_risks.request_id 
            AND client_id = auth.uid()
        )
    );

-- ==================== إنشاء الدوال المساعدة ====================

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إضافة المشغلات للجداول
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_requests_updated_at BEFORE UPDATE ON public.requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_timeline_updated_at BEFORE UPDATE ON public.request_timeline
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_risks_updated_at BEFORE UPDATE ON public.request_risks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==================== إدراج البيانات التجريبية ====================

-- إدراج الخدمات
INSERT INTO public.services (id, name, icon, category, description, features, duration, starting_price, includes) VALUES
('foundation', 'خدمات التأسيس', '🏛️', 'foundation', 'خدمات شاملة لتأسيس الجمعيات والمؤسسات غير الربحية', 
 ARRAY['دراسة الجدوى', 'إعداد النظام الأساسي', 'التسجيل الرسمي', 'التراخيص'], 
 '2-3 أشهر', 20000, ARRAY['استشارات غير محدودة', 'مراجعات قانونية', 'دعم فني 6 أشهر']),

('financial', 'الخدمات المالية والإدارية', '💰', 'financial', 'حلول متكاملة للإدارة المالية والمحاسبية', 
 ARRAY['تركيب الأنظمة المحاسبية', 'اللوائح المالية', 'التدريب', 'التقارير المالية'], 
 '1-2 شهر', 30000, ARRAY['نظام محاسبي متكامل', 'تدريب 20 ساعة', 'دعم سنة كاملة']),

('governance', 'خدمة حوكمة الجمعيات', '⚖️', 'governance', 'تطبيق أفضل معايير الحوكمة المؤسسية', 
 ARRAY['تقييم الحوكمة', 'وضع اللوائح', 'تدريب الإدارة', 'إدارة المخاطر'], 
 '3-4 أشهر', 40000, ARRAY['تقييم شامل', 'خطة حوكمة متكاملة', 'تدريب 30 ساعة'])

ON CONFLICT (id) DO NOTHING;

-- ملاحظة: البيانات التجريبية للمستخدمين والطلبات سيتم إدراجها عبر التطبيق
