#!/bin/bash

# نماء الاحترافية - Build Script
# هذا السكريبت يبني المشروع للنشر

echo "🚀 بدء بناء مشروع نماء الاحترافية..."

# تحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً"
    echo "تحميل من: https://nodejs.org"
    exit 1
fi

# تحقق من وجود npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm غير مثبت"
    exit 1
fi

echo "✅ Node.js و npm متوفران"

# تثبيت التبعيات
echo "📦 تثبيت التبعيات..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت التبعيات"
    exit 1
fi

echo "✅ تم تثبيت التبعيات بنجاح"

# بناء المشروع
echo "🔨 بناء المشروع..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ فشل في بناء المشروع"
    exit 1
fi

echo "✅ تم بناء المشروع بنجاح"

# تحقق من وجود مجلد dist
if [ ! -d "dist" ]; then
    echo "❌ مجلد dist غير موجود"
    exit 1
fi

echo "📁 محتويات مجلد dist:"
ls -la dist/

echo ""
echo "🎉 تم بناء المشروع بنجاح!"
echo ""
echo "📋 خطوات النشر:"
echo "1. اذهب إلى https://app.netlify.com/drop"
echo "2. اسحب مجلد 'dist' إلى الصفحة"
echo "3. انتظر التحميل"
echo "4. احصل على الرابط!"
echo ""
echo "🧪 للاختبار المحلي:"
echo "npm run preview"
echo ""
echo "📞 للدعم: <EMAIL>"
