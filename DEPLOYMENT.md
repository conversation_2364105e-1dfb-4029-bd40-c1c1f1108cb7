# دليل النشر - نماء الاحترافية

هذا الدليل يوضح كيفية نشر نظام نماء الاحترافية على Netlify مع ربطه بـ Supabase.

## 📋 المتطلبات الأساسية

- [ ] حساب <PERSON>
- [ ] حساب Supabase
- [ ] حساب Netlify
- [ ] Node.js 18+ مثبت محلياً

## 🗄️ إعداد Supabase

### 1. إنشاء مشروع جديد

1. اذهب إلى [supabase.com](https://supabase.com)
2. انقر على "Start your project"
3. أنشئ مشروع جديد واختر:
   - **اسم المشروع**: nama-client-system
   - **كلمة مرور قاعدة البيانات**: اختر كلمة مرور قوية
   - **المنطقة**: اختر أقرب منطقة لك

### 2. إعداد قاعدة البيانات

1. اذهب إلى **SQL Editor** في لوحة تحكم Supabase
2. انسخ محتوى ملف `supabase-setup.sql` والصقه
3. انقر على **Run** لتنفيذ الاستعلامات

### 3. إعداد المصادقة

1. اذهب إلى **Authentication** > **Settings**
2. في **Site URL** أضف:
   ```
   https://your-app-name.netlify.app
   ```
3. في **Redirect URLs** أضف:
   ```
   https://your-app-name.netlify.app/**
   ```

### 4. إعداد Storage (اختياري)

1. اذهب إلى **Storage**
2. أنشئ bucket جديد باسم `documents`
3. اجعله public للقراءة

### 5. الحصول على مفاتيح API

1. اذهب إلى **Settings** > **API**
2. انسخ:
   - **Project URL**
   - **anon public key**

## 🚀 إعداد Netlify

### 1. ربط المشروع بـ GitHub

1. ارفع المشروع إلى GitHub repository
2. اذهب إلى [netlify.com](https://netlify.com)
3. انقر على "New site from Git"
4. اختر GitHub واختر repository المشروع

### 2. إعداد البناء

في إعدادات النشر:
- **Build command**: `npm run build`
- **Publish directory**: `dist`
- **Node version**: `18`

### 3. إعداد متغيرات البيئة

في **Site settings** > **Environment variables** أضف:

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
VITE_APP_NAME=نماء الاحترافية
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production
```

### 4. إعداد النطاق المخصص (اختياري)

1. اذهب إلى **Domain settings**
2. أضف نطاقك المخصص
3. اتبع التعليمات لإعداد DNS

## 🔧 إعداد التطوير المحلي

### 1. استنساخ المشروع

```bash
git clone https://github.com/your-username/nama-client-system.git
cd nama-client-system
```

### 2. تثبيت التبعيات

```bash
npm install
```

### 3. إعداد متغيرات البيئة

```bash
cp .env.example .env
```

ثم حدث ملف `.env`:

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
VITE_APP_NAME=نماء الاحترافية
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development
```

### 4. تشغيل التطبيق

```bash
npm run dev
```

## 📊 إضافة البيانات التجريبية

### 1. إنشاء المستخدمين التجريبيين

في **Authentication** > **Users** أضف:

**المستخدم الأول:**
- Email: `<EMAIL>`
- Password: `123456`

**المستخدم الثاني:**
- Email: `<EMAIL>`
- Password: `123456`

### 2. إضافة بيانات المستخدمين

في **SQL Editor** نفذ:

```sql
-- إضافة بيانات المستخدم الأول
INSERT INTO public.users (id, email, name, contact_person, phone, organization_name, logo, membership_level)
VALUES (
  'user-id-from-auth-users-table',
  '<EMAIL>',
  'جمعية الأمل الخيرية',
  'أحمد محمد السالم',
  '0501234567',
  'جمعية الأمل الخيرية',
  '🏢',
  'premium'
);

-- كرر نفس العملية للمستخدم الثاني
```

## 🔍 اختبار النشر

### 1. اختبار تسجيل الدخول

- جرب تسجيل الدخول بالحسابات التجريبية
- تأكد من عمل المصادقة بشكل صحيح

### 2. اختبار الوظائف

- تحقق من تحميل البيانات
- اختبر الإشعارات
- تأكد من عمل التحديثات المباشرة

### 3. اختبار الأداء

- استخدم أدوات المطور لفحص الأداء
- تحقق من سرعة التحميل
- اختبر على أجهزة مختلفة

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في الاتصال بـ Supabase:**
- تحقق من صحة URL و API Key
- تأكد من إعداد CORS في Supabase

**2. مشاكل في المصادقة:**
- تحقق من إعداد Redirect URLs
- تأكد من صحة Site URL

**3. مشاكل في النشر:**
- تحقق من Build logs في Netlify
- تأكد من صحة متغيرات البيئة

**4. مشاكل في قاعدة البيانات:**
- تحقق من RLS policies
- تأكد من صحة الجداول والفهارس

## 📈 مراقبة الأداء

### 1. إعداد Analytics

```javascript
// في ملف منفصل للتحليلات
export const analytics = {
  track: (event, properties) => {
    // إضافة Google Analytics أو أي أداة أخرى
  }
}
```

### 2. مراقبة الأخطاء

```javascript
// إعداد Sentry أو أداة مراقبة أخرى
import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: "YOUR_SENTRY_DSN",
});
```

## 🔄 التحديثات المستقبلية

### سير العمل للتحديثات

1. **التطوير المحلي**
   ```bash
   git checkout -b feature/new-feature
   # تطوير الميزة الجديدة
   git commit -m "Add new feature"
   git push origin feature/new-feature
   ```

2. **المراجعة والدمج**
   - إنشاء Pull Request
   - مراجعة الكود
   - دمج في main branch

3. **النشر التلقائي**
   - Netlify سينشر تلقائياً عند الدمج في main

## 📞 الدعم

في حالة وجود مشاكل:

1. راجع logs في Netlify
2. تحقق من Supabase logs
3. استخدم أدوات المطور في المتصفح
4. راجع الوثائق الرسمية لـ Supabase و Netlify

---

**تم إعداد هذا الدليل بواسطة فريق نماء الاحترافية**
