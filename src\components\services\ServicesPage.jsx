import React, { useState, useEffect } from 'react'
import {
  Globe,
  Palette,
  Smartphone,
  ShoppingCart,
  BarChart3,
  Shield,
  ArrowLeft,
  Star,
  Clock,
  DollarSign,
  FileText
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import useStore from '../../store/useStore'
import useSidebar from '../../hooks/useSidebar'
import ServiceRequestModal from './ServiceRequestModal'
import AddServiceModal from './AddServiceModal'
import EditServiceModal from './EditServiceModal'
import PageHeader from '../layout/PageHeader'
import toast from 'react-hot-toast'

const ServicesPage = ({ onServiceSelect, onBack }) => {
  const { user } = useStore()
  const { isCollapsed, gridCols } = useSidebar()

  const [services, setServices] = useState([])
  const [subscribedServices, setSubscribedServices] = useState([])
  const [loading, setLoading] = useState(true)
  const [showRequestModal, setShowRequestModal] = useState(false)
  const [selectedServiceForRequest, setSelectedServiceForRequest] = useState(null)
  const [showAddServiceModal, setShowAddServiceModal] = useState(false)
  const [showEditServiceModal, setShowEditServiceModal] = useState(false)
  const [selectedServiceForEdit, setSelectedServiceForEdit] = useState(null)

  // التحقق من نوع المستخدم
  const isAdmin = user?.account_type === 'admin' || user?.email === '<EMAIL>'

  // تحميل الخدمات من قاعدة البيانات
  const loadServices = async () => {
    try {
      setLoading(true)

      // تحميل جميع الخدمات المتاحة
      const { data: allServices, error: servicesError } = await supabase
        .from('services')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (servicesError) throw servicesError
      setServices(allServices || [])

      // للعملاء: تحميل الخدمات المشترك بها
      if (!isAdmin && user?.id) {
        const { data: userServices, error: userServicesError } = await supabase
          .from('user_services')
          .select(`
            *,
            services (*)
          `)
          .eq('user_id', user.id)
          .eq('is_active', true)

        if (userServicesError) throw userServicesError
        setSubscribedServices(userServices?.map(us => us.services) || [])
      }
    } catch (error) {
      console.error('خطأ في تحميل الخدمات:', error)
      toast.error('فشل في تحميل الخدمات')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadServices()
  }, [user?.id, isAdmin])

  // قائمة الخدمات التجريبية (احتياطية)
  const demoServices = [
    {
      id: 'web-development',
      title: 'تطوير المواقع الإلكترونية',
      description: 'تصميم وتطوير مواقع إلكترونية احترافية ومتجاوبة',
      icon: Globe,
      category: 'development',
      price: 'من 5,000 ريال',
      duration: '2-4 أسابيع',
      rating: 4.9,
      projects: 45,
      color: 'blue'
    },
    {
      id: 'ui-ux-design',
      title: 'تصميم واجهات المستخدم',
      description: 'تصميم واجهات مستخدم جذابة وسهلة الاستخدام',
      icon: Palette,
      category: 'design',
      price: 'من 3,000 ريال',
      duration: '1-3 أسابيع',
      rating: 4.8,
      projects: 32,
      color: 'purple'
    },
    {
      id: 'mobile-apps',
      title: 'تطوير تطبيقات الجوال',
      description: 'تطوير تطبيقات iOS و Android احترافية',
      icon: Smartphone,
      category: 'development',
      price: 'من 8,000 ريال',
      duration: '4-8 أسابيع',
      rating: 4.7,
      projects: 28,
      color: 'green'
    },
    {
      id: 'ecommerce',
      title: 'متاجر إلكترونية',
      description: 'إنشاء متاجر إلكترونية متكاملة مع أنظمة الدفع',
      icon: ShoppingCart,
      category: 'development',
      price: 'من 10,000 ريال',
      duration: '3-6 أسابيع',
      rating: 4.9,
      projects: 22,
      color: 'orange'
    },
    {
      id: 'digital-marketing',
      title: 'التسويق الرقمي',
      description: 'حملات تسويقية رقمية وإدارة وسائل التواصل',
      icon: BarChart3,
      category: 'marketing',
      price: 'من 2,000 ريال',
      duration: '1-2 أسابيع',
      rating: 4.6,
      projects: 38,
      color: 'pink'
    },
    {
      id: 'cybersecurity',
      title: 'الأمن السيبراني',
      description: 'حماية المواقع والتطبيقات من التهديدات السيبرانية',
      icon: Shield,
      category: 'security',
      price: 'من 4,000 ريال',
      duration: '1-3 أسابيع',
      rating: 4.8,
      projects: 15,
      color: 'red'
    }
  ]



  // الخدمات المتاحة للاشتراك (غير مشترك بها)
  const availableServices = services.filter(service =>
    !subscribedServices.some(sub => sub.id === service.id)
  )

  // الحصول على مكون الأيقونة
  const getIconComponent = (iconName) => {
    const icons = {
      Globe, Palette, Smartphone, ShoppingCart, BarChart3, Shield, FileText
    }
    return icons[iconName] || Globe
  }

  // ألوان البطاقات
  const getColorClasses = (color) => {
    const colors = {
      blue: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
      purple: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
      green: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
      orange: 'from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',
      pink: 'from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700',
      red: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700'
    }
    return colors[color] || colors.blue
  }

  return (
    <div className="space-y-6">
      {/* Header الجديد */}
      <PageHeader
        backButton={onBack ? {
          text: "العودة",
          onClick: onBack
        } : null}
        title="إدارة الخدمات"
        subtitle={isAdmin ? "إدارة وعرض جميع الخدمات المتاحة" : "خدماتك المشتركة والخدمات المتاحة للاشتراك"}
      />

      {/* المحتوى الرئيسي */}
      <div className="px-6 space-y-6">



      {/* المحتوى حسب نوع المستخدم */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-4">جاري تحميل الخدمات...</p>
        </div>
      ) : isAdmin ? (
        // واجهة المدير: جميع الخدمات مع إمكانية الإدارة
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">جميع الخدمات ({services.length})</h2>
            <button
              onClick={() => setShowAddServiceModal(true)}
              className="btn btn-primary"
            >
              <FileText className="w-4 h-4 ml-2" />
              إضافة خدمة جديدة
            </button>
          </div>

          <div className={`grid grid-cols-1 gap-6 ${gridCols.normal}`}>
            {services.map((service) => {
              const Icon = getIconComponent(service.icon)
              return (
                <div
                  key={service.id}
                  onClick={() => onServiceSelect(service)}
                  className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group overflow-hidden"
                >
                  {/* رأس البطاقة */}
                  <div className={`bg-gradient-to-r ${getColorClasses(service.color)} p-6 text-white`}>
                    <div className="flex items-center justify-between mb-4">
                      <Icon className="w-8 h-8" />
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Star className="w-4 h-4 fill-current" />
                        <span className="text-sm font-medium">{service.rating || '4.5'}</span>
                      </div>
                    </div>
                    <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                    <p className="text-sm opacity-90">{service.description}</p>
                  </div>

                  {/* محتوى البطاقة */}
                  <div className="p-6">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        {service.base_price && (
                          <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                            <DollarSign className="w-4 h-4" />
                            <span className="text-sm">من {service.base_price.toLocaleString()} ريال</span>
                          </div>
                        )}
                        {service.duration_weeks && (
                          <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                            <Clock className="w-4 h-4" />
                            <span className="text-sm">{service.duration_weeks} أسابيع</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                        <span className="text-sm text-gray-500">
                          خدمة إدارية
                        </span>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedServiceForEdit(service)
                              setShowEditServiceModal(true)
                            }}
                            className="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
                          >
                            تعديل الخدمة
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      ) : (
        // واجهة العميل: تقسيم الخدمات
        <div className="space-y-8">
          {/* القسم العلوي: الخدمات المشترك بها */}
          {subscribedServices.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">خدماتك المشتركة ({subscribedServices.length})</h2>
                <span className="text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full">نشطة</span>
              </div>

              <div className={`grid grid-cols-1 gap-6 ${gridCols.normal}`}>
                {subscribedServices.map((service) => {
                  const Icon = getIconComponent(service.icon)
                  return (
                    <div
                      key={service.id}
                      onClick={() => onServiceSelect(service)}
                      className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group overflow-hidden border-2 border-green-200"
                    >
                      {/* رأس البطاقة */}
                      <div className={`bg-gradient-to-r ${getColorClasses(service.color)} p-6 text-white relative`}>
                        <div className="absolute top-2 left-2">
                          <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">مشترك</span>
                        </div>
                        <div className="flex items-center justify-between mb-4">
                          <Icon className="w-8 h-8" />
                          <div className="flex items-center space-x-1 space-x-reverse">
                            <Star className="w-4 h-4 fill-current" />
                            <span className="text-sm font-medium">{service.rating || '4.5'}</span>
                          </div>
                        </div>
                        <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                        <p className="text-sm opacity-90">{service.description}</p>
                      </div>

                      {/* محتوى البطاقة */}
                      <div className="p-6">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                            <span className="text-sm text-green-600 font-medium">
                              خدمة نشطة
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                onServiceSelect(service)
                              }}
                              className="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
                            >
                              عرض التفاصيل
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* القسم السفلي: الخدمات المتاحة للاشتراك */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">الخدمات المتاحة للاشتراك ({availableServices.length})</h2>
              <span className="text-sm text-blue-600 bg-blue-100 px-3 py-1 rounded-full">متاحة</span>
            </div>

            {availableServices.length > 0 ? (
              <div className={`grid grid-cols-1 gap-6 ${gridCols.normal}`}>
                {availableServices.map((service) => {
                  const Icon = getIconComponent(service.icon)
                  return (
                    <div
                      key={service.id}
                      onClick={() => onServiceSelect(service)}
                      className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group overflow-hidden"
                    >
                      {/* رأس البطاقة */}
                      <div className={`bg-gradient-to-r ${getColorClasses(service.color)} p-6 text-white`}>
                        <div className="flex items-center justify-between mb-4">
                          <Icon className="w-8 h-8" />
                          <div className="flex items-center space-x-1 space-x-reverse">
                            <Star className="w-4 h-4 fill-current" />
                            <span className="text-sm font-medium">{service.rating || '4.5'}</span>
                          </div>
                        </div>
                        <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                        <p className="text-sm opacity-90">{service.description}</p>
                      </div>

                      {/* محتوى البطاقة */}
                      <div className="p-6">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            {service.base_price && (
                              <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                                <DollarSign className="w-4 h-4" />
                                <span className="text-sm">من {service.base_price.toLocaleString()} ريال</span>
                              </div>
                            )}
                            {service.duration_weeks && (
                              <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                                <Clock className="w-4 h-4" />
                                <span className="text-sm">{service.duration_weeks} أسابيع</span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                            <span className="text-sm text-gray-500">
                              خدمة متاحة
                            </span>
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  onServiceSelect(service)
                                }}
                                className="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
                              >
                                عرض التفاصيل
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  setSelectedServiceForRequest(service)
                                  setShowRequestModal(true)
                                }}
                                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                              >
                                طلب الخدمة
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-8 bg-gray-50 rounded-xl">
                <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">جميع الخدمات مشتركة</h3>
                <p className="text-gray-500">أنت مشترك في جميع الخدمات المتاحة</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* رسالة عدم وجود نتائج */}
      {!loading && services.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <FileText className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد خدمات</h3>
          <p className="text-gray-600">
            {isAdmin
              ? 'لا توجد خدمات متاحة حالياً'
              : 'لا توجد خدمات متاحة حالياً'
            }
          </p>
        </div>
      )}

      {/* نموذج طلب الخدمة */}
      <ServiceRequestModal
        service={selectedServiceForRequest}
        isOpen={showRequestModal}
        onClose={() => {
          setShowRequestModal(false)
          setSelectedServiceForRequest(null)
        }}
        onSuccess={() => {
          // يمكن إضافة منطق إضافي هنا
        }}
      />

      {/* نموذج إضافة خدمة جديدة */}
      <AddServiceModal
        isOpen={showAddServiceModal}
        onClose={() => setShowAddServiceModal(false)}
        onSuccess={(newService) => {
          // إضافة الخدمة الجديدة للقائمة
          setServices(prev => [newService, ...prev])
          loadServices() // إعادة تحميل القائمة للتأكد
        }}
      />

      {/* نموذج تعديل الخدمة */}
      <EditServiceModal
        isOpen={showEditServiceModal}
        onClose={() => {
          setShowEditServiceModal(false)
          setSelectedServiceForEdit(null)
        }}
        service={selectedServiceForEdit}
        onSuccess={(updatedService, action) => {
          if (action === 'delete') {
            // حذف الخدمة من القائمة
            setServices(prev => prev.filter(s => s.id !== selectedServiceForEdit.id))
          } else {
            // تحديث الخدمة في القائمة
            setServices(prev => prev.map(s =>
              s.id === updatedService.id ? updatedService : s
            ))
          }
          loadServices() // إعادة تحميل القائمة للتأكد
        }}
      />
      </div>
    </div>
  )
}

export default ServicesPage
