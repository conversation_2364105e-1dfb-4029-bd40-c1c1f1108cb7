import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase } from '../lib/supabase'
import toast from 'react-hot-toast'

const useStore = create(
  persist(
    (set, get) => ({
      // الحالة الأولية
      user: null,
      session: null,
      isLoading: false,
      isAuthenticated: false,
      
      // الطلبات
      orders: [],
      selectedOrder: null,
      ordersLoading: false,
      
      // الإشعارات
      notifications: [],
      unreadCount: 0,
      notificationsLoading: false,
      
      // إعدادات الواجهة
      sidebarOpen: true,
      theme: 'light',
      language: 'ar',

      // ==================== دوال المصادقة ====================
      
      // تسجيل الدخول
      signIn: async (email, password) => {
        set({ isLoading: true })

        try {
          console.log('🔐 بدء عملية تسجيل الدخول:', email)

          // مسح أي جلسة سابقة أولاً
          await supabase.auth.signOut()

          // تسجيل الدخول مع Supabase Auth
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
          })

          console.log('🔐 استجابة تسجيل الدخول:', {
            hasData: !!data,
            hasUser: !!data?.user,
            hasSession: !!data?.session,
            error: error?.message
          })

          if (error) {
            console.error('🔐 خطأ في تسجيل الدخول:', error)
            set({ isLoading: false })

            // معالجة أنواع مختلفة من الأخطاء
            if (error.message.includes('Invalid login credentials')) {
              toast.error('بيانات الدخول غير صحيحة')
            } else if (error.message.includes('Email not confirmed')) {
              toast.error('يرجى تأكيد البريد الإلكتروني أولاً')
            } else if (error.message.includes('Too many requests')) {
              toast.error('محاولات كثيرة، يرجى المحاولة لاحقاً')
            } else {
              toast.error('حدث خطأ في تسجيل الدخول: ' + error.message)
            }

            return { success: false, error: error.message }
          }

          // جلب بيانات المستخدم من جدول users
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

          if (userError) {
            console.error('خطأ في جلب بيانات المستخدم:', userError)
          }

          const user = userData || {
            id: data.user.id,
            email: data.user.email,
            name: data.user.email,
            organization_name: data.user.email
          }

          set({
            user: user,
            session: data.session,
            isAuthenticated: true,
            isLoading: false
          })

          // تحميل البيانات
          setTimeout(() => {
            get().loadOrders()
            get().loadNotifications()
          }, 100)

          toast.success(`مرحباً بك ${user.name}`)
          return { success: true }
          
        } catch (error) {
          set({ isLoading: false })
          toast.error('حدث خطأ في تسجيل الدخول')
          return { success: false, error: error.message }
        }
      },

      // تسجيل الخروج
      signOut: async () => {
        try {
          await supabase.auth.signOut()
          
          set({
            user: null,
            session: null,
            isAuthenticated: false,
            orders: [],
            notifications: [],
            selectedOrder: null
          })
          
          toast.success('تم تسجيل الخروج بنجاح')
          
        } catch (error) {
          toast.error('حدث خطأ في تسجيل الخروج')
        }
      },

      // التحقق من الجلسة
      checkAuth: async () => {
        try {
          const { data: { session } } = await supabase.auth.getSession()
          console.log('🔐 Session check:', session ? 'Found' : 'Not found')

          if (session) {
            // جلب بيانات المستخدم
            const { data: userData, error } = await supabase
              .from('users')
              .select('*')
              .eq('id', session.user.id)
              .single()

            console.log('👤 User data from checkAuth:', userData)

            if (!error && userData) {
              set({
                user: userData,
                session: session,
                isAuthenticated: true
              })

              console.log('✅ User set in store:', userData.name)

              // تحميل البيانات
              setTimeout(() => {
                get().loadOrders()
                get().loadNotifications()
              }, 100)
            }
          }
        } catch (error) {
          console.error('خطأ في التحقق من المصادقة:', error)
        }
      },

      // ==================== دوال الطلبات ====================
      
      // تحميل الطلبات
      loadOrders: async () => {
        const { user } = get()
        if (!user) {
          console.log('❌ No user found')
          return
        }

        set({ ordersLoading: true })

        try {
          // التحقق من نوع المستخدم
          console.log('👤 Full user object:', user)
          console.log('⚙️ User preferences:', user?.preferences)
          console.log('🔑 User type:', user?.preferences?.user_type)

          const isAdmin = user?.account_type === 'admin' || user?.email === '<EMAIL>'
          console.log('🔍 loadOrders - User:', user?.name, 'isAdmin:', isAdmin, 'account_type:', user?.account_type)

          let query

          if (!isAdmin) {
            // للعملاء: جلب طلباتهم فقط
            console.log('👤 Loading orders for user:', user.id)
            query = supabase
              .from('orders')
              .select(`
                *,
                users (
                  id,
                  name,
                  organization_name,
                  email
                )
              `)
              .eq('user_id', user.id)
          } else {
            // للإداري: جلب جميع الطلبات
            console.log('👑 Loading ALL orders for admin')
            query = supabase
              .from('orders')
              .select(`
                *,
                users (
                  id,
                  name,
                  organization_name,
                  email
                )
              `)
          }

          const { data, error } = await query.order('created_at', { ascending: false })

          if (error) {
            console.error('❌ Supabase error:', error)
            throw error
          }

          console.log('📊 Orders loaded:', data?.length, 'orders')
          console.log('📋 Orders data:', data)

          set({
            orders: data || [],
            ordersLoading: false
          })

        } catch (error) {
          set({ ordersLoading: false })
          console.error('حدث خطأ في تحميل الطلبات:', error)
          toast.error('حدث خطأ في تحميل الطلبات')
        }
      },

      // إنشاء طلب جديد
      createOrder: async (orderData) => {
        const { user } = get()
        if (!user) {
          toast.error('يجب تسجيل الدخول أولاً')
          return { success: false }
        }

        set({ ordersLoading: true })

        try {
          // إنشاء رقم طلب جديد
          const orderNumber = `ORD-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`

          const newOrder = {
            user_id: user.id,
            order_number: orderNumber,
            title: orderData.title,
            description: orderData.description,
            category: orderData.category,
            priority: orderData.priority || 'normal',
            status: 'pending',
            estimated_cost: parseFloat(orderData.estimated_cost) || 0,
            estimated_duration: parseInt(orderData.estimated_duration) || 30,
            payment_status: 'pending'
          }

          const { data, error } = await supabase
            .from('orders')
            .insert([newOrder])
            .select()
            .single()

          if (error) {
            throw error
          }

          // إضافة الطلب للقائمة المحلية
          const { orders } = get()
          set({
            orders: [data, ...orders],
            ordersLoading: false
          })

          toast.success('تم إنشاء الطلب بنجاح!')
          return { success: true, order: data }

        } catch (error) {
          set({ ordersLoading: false })
          console.error('خطأ في إنشاء الطلب:', error)
          toast.error('حدث خطأ في إنشاء الطلب')
          return { success: false, error: error.message }
        }
      },

      // تحديد طلب محدد
      selectOrder: async (orderId) => {
        try {
          // أولاً، تحقق من الطلبات المحملة محلياً
          const { orders } = get()
          const localOrder = orders.find(o => o.id === orderId)

          if (localOrder) {
            set({ selectedOrder: localOrder })
            return
          }

          // إذا لم يوجد محلياً، جلبه من قاعدة البيانات
          const { data, error } = await supabase
            .from('orders')
            .select(`
              *,
              users (
                id,
                name,
                organization_name,
                email
              )
            `)
            .eq('id', orderId)
            .single()

          if (error) {
            console.error('خطأ في جلب الطلب:', error)
            toast.error('الطلب غير موجود أو لا يمكن الوصول إليه')
            return
          }

          set({ selectedOrder: data })
        } catch (error) {
          console.error('خطأ في جلب تفاصيل الطلب:', error)
          toast.error('حدث خطأ في جلب تفاصيل الطلب')
        }
      },

      // ==================== دوال الإشعارات ====================
      
      // تحميل الإشعارات
      loadNotifications: async () => {
        const { user } = get()
        if (!user) return

        set({ notificationsLoading: true })
        
        try {
          const { data, error } = await supabase
            .from('notifications')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(20)

          if (error) {
            throw error
          }

          const unreadCount = data?.filter(n => n.status === 'unread').length || 0
          
          set({ 
            notifications: data || [],
            unreadCount: unreadCount,
            notificationsLoading: false 
          })
          
        } catch (error) {
          set({ notificationsLoading: false })
          console.error('حدث خطأ في تحميل الإشعارات:', error)
        }
      },

      // ==================== دوال المهام والاتصالات ====================

      // إضافة حدث جديد للجدول الزمني
      addTimelineEvent: async (orderId, eventData) => {
        const { user } = get()
        if (!user) {
          toast.error('يجب تسجيل الدخول أولاً')
          return { success: false }
        }

        try {
          // تحويل نوع الحدث للقيم المسموحة في قاعدة البيانات
          const typeMapping = {
            'comment': 'message',
            'status_change': 'notification',
            'file_upload': 'message',
            'payment': 'notification'
          }

          const newEvent = {
            order_id: orderId,
            user_id: user.id,
            type: typeMapping[eventData.type] || 'message',
            direction: 'outgoing',
            subject: eventData.title,
            content: eventData.description,
            sender_name: user.name || 'فريق نماء',
            recipient_name: 'العميل',
            status: 'sent',
            priority: 'normal',
            metadata: {
              original_type: eventData.type
            }
          }

          const { data, error } = await supabase
            .from('communications')
            .insert([newEvent])
            .select()
            .single()

          if (error) {
            console.error('Supabase error:', error)
            throw error
          }

          toast.success('تم إضافة الحدث بنجاح!')
          return { success: true, event: data }

        } catch (error) {
          console.error('خطأ في إضافة الحدث:', error)
          toast.error(`حدث خطأ في إضافة الحدث: ${error.message}`)
          return { success: false, error: error.message }
        }
      },

      // تحميل مهام الطلب
      loadOrderTasks: async (orderId) => {
        try {
          const { data, error } = await supabase
            .from('tasks')
            .select('*')
            .eq('order_id', orderId)
            .order('created_at', { ascending: false })

          if (error) {
            throw error
          }

          return data || []
        } catch (error) {
          console.error('خطأ في تحميل المهام:', error)
          return []
        }
      },

      // تحميل اتصالات الطلب
      loadOrderCommunications: async (orderId) => {
        try {
          const { data, error } = await supabase
            .from('communications')
            .select('*')
            .eq('order_id', orderId)
            .order('created_at', { ascending: false })

          if (error) {
            throw error
          }

          return data || []
        } catch (error) {
          console.error('خطأ في تحميل الاتصالات:', error)
          return []
        }
      },

      // ==================== دوال إدارة المستخدمين ====================

      // إنشاء مستخدم جديد
      createUser: async (userData) => {
        try {
          const userId = crypto.randomUUID()

          // إنشاء المستخدم في auth.users
          const { error: authError } = await supabase.rpc('create_auth_user', {
            user_id: userId,
            user_email: userData.email,
            user_password: userData.password
          })

          if (authError) throw authError

          // إنشاء بروفايل المستخدم
          const { error: profileError } = await supabase
            .from('users')
            .insert({
              id: userId,
              email: userData.email,
              name: userData.name,
              organization_name: userData.organization_name || userData.name,
              contact_person: userData.contact_person,
              phone: userData.phone,
              address: userData.address,
              membership_level: userData.membership_level || 'basic',
              website: userData.website,
              logo: userData.logo || '🏢',
              preferences: {
                user_type: 'customer',
                notifications: true,
                language: 'ar'
              }
            })

          if (profileError) throw profileError

          return { success: true, userId }
        } catch (error) {
          console.error('خطأ في إنشاء المستخدم:', error)
          return { success: false, error: error.message }
        }
      },

      // تغيير كلمة مرور المستخدم
      changeUserPassword: async (userId, newPassword) => {
        try {
          const { error } = await supabase.rpc('update_user_password', {
            user_id: userId,
            new_password: newPassword
          })

          if (error) throw error

          return { success: true }
        } catch (error) {
          console.error('خطأ في تغيير كلمة المرور:', error)
          return { success: false, error: error.message }
        }
      },

      // ==================== دوال الواجهة ====================

      // تبديل الشريط الجانبي
      toggleSidebar: () => {
        set(state => ({ sidebarOpen: !state.sidebarOpen }))
      },

      // تبديل المظهر
      toggleTheme: () => {
        set(state => ({
          theme: state.theme === 'light' ? 'dark' : 'light'
        }))
      },

      // إعادة تعيين الحالة
      reset: () => {
        set({
          user: null,
          session: null,
          isLoading: false,
          isAuthenticated: false,
          orders: [],
          selectedOrder: null,
          ordersLoading: false,
          notifications: [],
          unreadCount: 0,
          notificationsLoading: false
        })
      }
    }),
    {
      name: 'nama-store',
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        sidebarOpen: state.sidebarOpen
      })
    }
  )
)

export default useStore
