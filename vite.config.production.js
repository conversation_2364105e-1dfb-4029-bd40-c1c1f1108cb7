import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// إعدادات الإنتاج المحسنة
export default defineConfig({
  plugins: [react()],
  
  // إعدادات الخادم
  server: {
    port: 3000,
    host: true
  },
  
  // إعدادات البناء المحسنة
  build: {
    outDir: 'dist',
    sourcemap: false, // إيقاف source maps في الإنتاج
    minify: 'terser', // استخدام terser للضغط
    
    // تحسين حجم الحزم
    rollupOptions: {
      output: {
        manualChunks: {
          // فصل المكتبات الكبيرة
          vendor: ['react', 'react-dom'],
          supabase: ['@supabase/supabase-js'],
          ui: ['lucide-react', 'react-hot-toast'],
          store: ['zustand']
        },
        
        // تسمية الملفات
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          const ext = info[info.length - 1]
          
          if (/\.(css)$/.test(assetInfo.name)) {
            return 'assets/css/[name]-[hash].[ext]'
          }
          
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return 'assets/images/[name]-[hash].[ext]'
          }
          
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
            return 'assets/fonts/[name]-[hash].[ext]'
          }
          
          return 'assets/[name]-[hash].[ext]'
        }
      }
    },
    
    // إعدادات terser للضغط المتقدم
    terserOptions: {
      compress: {
        drop_console: true, // إزالة console.log في الإنتاج
        drop_debugger: true, // إزالة debugger
        pure_funcs: ['console.log', 'console.info', 'console.debug'], // إزالة دوال محددة
      },
      mangle: {
        safari10: true // دعم Safari 10
      },
      format: {
        comments: false // إزالة التعليقات
      }
    },
    
    // تحسين CSS
    cssCodeSplit: true,
    cssMinify: true,
    
    // حد أقصى لحجم الملفات
    chunkSizeWarningLimit: 1000,
    
    // تحسين الأصول
    assetsInlineLimit: 4096 // inline الملفات الصغيرة
  },
  
  // تحسين التبعيات
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@supabase/supabase-js',
      'zustand',
      'lucide-react',
      'react-hot-toast'
    ]
  },
  
  // إعدادات CSS
  css: {
    devSourcemap: false, // إيقاف source maps للـ CSS في الإنتاج
    postcss: {
      plugins: [
        // تحسين Tailwind CSS
        require('tailwindcss'),
        require('autoprefixer'),
        // ضغط CSS في الإنتاج
        require('cssnano')({
          preset: ['default', {
            discardComments: {
              removeAll: true,
            },
            normalizeWhitespace: true,
            minifySelectors: true,
            minifyParams: true
          }]
        })
      ]
    }
  },
  
  // إعدادات الأمان
  define: {
    // إزالة معلومات التطوير
    __DEV__: false,
    'process.env.NODE_ENV': '"production"'
  },
  
  // تحسين الشبكة
  experimental: {
    renderBuiltUrl(filename, { hostType }) {
      if (hostType === 'js') {
        return { js: `/${filename}` }
      } else {
        return { relative: true }
      }
    }
  }
})
