# إعدادات الأمان والأداء لخوادم Apache
# ملاحظة: هذا الملف احتياطي، Netlify يستخدم _headers و netlify.toml

# تفعيل إعادة الكتابة
RewriteEngine On

# إعادة توجيه للـ SPA
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # منع clickjacking
    Header always set X-Frame-Options "DENY"
    
    # حماية XSS
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Permissions Policy
    Header always set Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co wss://*.supabase.co; frame-ancestors 'none';"
</IfModule>

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # ملفات HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # ملفات CSS و JS
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # الصور
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    
    # ملفات أخرى
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/manifest+json "access plus 1 week"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# منع الوصول للملفات الحساسة
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files ".env.*">
    Order allow,deny
    Deny from all
</Files>

# إعدادات خاصة لـ manifest.json
<Files "manifest.json">
    <IfModule mod_headers.c>
        Header set Content-Type "application/manifest+json"
        Header set X-Content-Type-Options "nosniff"
    </IfModule>
</Files>

# إزالة إصدار الخادم من الهيدر
ServerTokens Prod
