# 🚀 نشر على GitHub Pages (بديل Netlify)

إذا واجهت مشاكل مع Netlify، يمكن نشر المشروع على GitHub Pages مجاناً!

## ⚡ **تفعيل GitHub Pages (خطوة واحدة)**

### 1. **تفعيل Pages في Repository:**
1. اذهب إلى: https://github.com/sh33hemam/nama-3meel
2. انقر **Settings** (في أعلى الصفحة)
3. انقر **Pages** (في القائمة الجانبية)
4. في **Source** اختر: **GitHub Actions**
5. انقر **Save**

### 2. **النشر التلقائي:**
- سيتم النشر تلقائياً خلال 2-3 دقائق
- الرابط سيكون: `https://sh33hemam.github.io/nama-3meel`

---

## 🔧 **إعدادات إضافية (اختيارية)**

### **تخصيص النطاق:**
1. في **Pages settings**
2. **Custom domain**: أدخل نطاقك
3. **Enforce HTTPS**: فعّل هذا الخيار

### **متغيرات البيئة:**
1. **Settings** > **Secrets and variables** > **Actions**
2. أضف متغيرات (إذا كنت تستخدم Supabase):
```
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

---

## 📊 **مقارنة المنصات**

| الميزة | GitHub Pages | Netlify | Vercel |
|--------|-------------|---------|--------|
| **التكلفة** | مجاني | مجاني (محدود) | مجاني (محدود) |
| **النطاق** | github.io | netlify.app | vercel.app |
| **النشر** | GitHub Actions | Git Integration | Git Integration |
| **CDN** | ✅ | ✅ | ✅ |
| **HTTPS** | ✅ | ✅ | ✅ |
| **سهولة الإعداد** | متوسط | سهل | سهل |

---

## 🎯 **الروابط المتوقعة**

### **GitHub Pages:**
```
https://sh33hemam.github.io/nama-3meel
```

### **Netlify (إذا نجح):**
```
https://nama-3meel.netlify.app
```

### **Vercel (بديل آخر):**
```
https://nama-3meel.vercel.app
```

---

## 🔍 **تحقق من حالة النشر**

### **GitHub Actions:**
1. اذهب إلى: https://github.com/sh33hemam/nama-3meel/actions
2. ستجد workflow يسمى "Build and Deploy"
3. انقر عليه لمتابعة التقدم

### **Pages Status:**
1. **Settings** > **Pages**
2. ستجد رسالة: "Your site is published at..."

---

## 🧪 **اختبار الموقع**

### **الحسابات التجريبية:**
```
البريد: <EMAIL>
كلمة المرور: 123456

البريد: <EMAIL>
كلمة المرور: 123456
```

### **قائمة الفحص:**
- [ ] الموقع يفتح بدون أخطاء
- [ ] تسجيل الدخول يعمل
- [ ] التصميم يظهر بشكل صحيح
- [ ] متجاوب على الجوال
- [ ] سرعة التحميل مقبولة

---

## 🛠️ **استكشاف الأخطاء**

### **المشكلة: Build يفشل**
1. تحقق من **Actions** tab
2. راجع error logs
3. تأكد من صحة package.json

### **المشكلة: الموقع لا يعمل**
1. تحقق من console errors
2. تأكد من إعدادات base URL
3. راجع _redirects settings

### **المشكلة: 404 على الصفحات**
- GitHub Pages يحتاج إعداد خاص للـ SPA
- تم إضافة الإعدادات المطلوبة تلقائياً

---

## 🚀 **بدائل أخرى للنشر**

### **1. Vercel (سهل جداً):**
```
1. اذهب إلى vercel.com
2. Import Git Repository
3. اختر nama-3meel
4. Deploy
```

### **2. Firebase Hosting:**
```bash
npm install -g firebase-tools
firebase login
firebase init hosting
firebase deploy
```

### **3. Surge.sh (سريع):**
```bash
npm install -g surge
npm run build
cd dist
surge
```

---

## 📞 **الدعم**

### **إذا استمرت المشاكل:**
1. **GitHub Issues**: https://github.com/sh33hemam/nama-3meel/issues
2. **البريد**: <EMAIL>
3. **GitHub Pages Docs**: https://docs.github.com/en/pages

---

## 🎉 **النتيجة المتوقعة**

بعد تفعيل GitHub Pages:
- ✅ موقع متاح على: `https://sh33hemam.github.io/nama-3meel`
- ✅ تحديثات تلقائية عند push
- ✅ HTTPS مفعل
- ✅ CDN عالمي
- ✅ مجاني 100%

---

**💡 نصيحة**: GitHub Pages أبسط وأكثر استقراراً من Netlify للمشاريع الصغيرة!
