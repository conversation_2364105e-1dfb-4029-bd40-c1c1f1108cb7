import React, { useState } from 'react'
import {
  X,
  Plus,
  Save,
  User,
  Mail,
  Phone,
  Building,
  Shield,
  Eye,
  EyeOff
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const AddCustomerModal = ({ isOpen, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    phone: '',
    contact_person: '',
    account_type: 'customer',
    is_active: true
  })

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    // التحقق من البيانات المطلوبة
    if (!formData.name.trim() || !formData.email.trim() || !formData.password.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    // التحقق من صحة الإيميل
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      toast.error('يرجى إدخال بريد إلكتروني صحيح')
      return
    }

    // التحقق من قوة كلمة المرور
    if (formData.password.length < 6) {
      toast.error('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      return
    }

    try {
      setLoading(true)

      // إنشاء حساب جديد في Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            name: formData.name,
            account_type: formData.account_type
          }
        }
      })

      if (authError) {
        if (authError.message.includes('already registered')) {
          toast.error('هذا البريد الإلكتروني مسجل مسبقاً')
        } else {
          throw authError
        }
        return
      }

      // إضافة بيانات إضافية في جدول users
      if (authData.user) {
        const { error: userError } = await supabase
          .from('users')
          .upsert({
            id: authData.user.id,
            email: formData.email,
            name: formData.name,
            phone: formData.phone || null,
            contact_person: formData.contact_person || null,
            account_type: formData.account_type,
            is_active: formData.is_active,
            created_at: new Date().toISOString()
          })

        if (userError) throw userError
      }

      toast.success('تم إضافة العميل بنجاح!')
      
      // إعادة تعيين النموذج
      setFormData({
        name: '',
        email: '',
        password: '',
        phone: '',
        contact_person: '',
        account_type: 'customer',
        is_active: true
      })

      // إغلاق النموذج وتحديث القائمة
      onSuccess && onSuccess()
      onClose()

    } catch (error) {
      console.error('خطأ في إضافة العميل:', error)
      toast.error('فشل في إضافة العميل')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* رأس النموذج */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
              <Plus className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">إضافة عميل جديد</h2>
              <p className="text-sm text-gray-500">إضافة عميل أو مدير جديد إلى النظام</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* محتوى النموذج */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* المعلومات الأساسية */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* الاسم */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم العميل *
                </label>
                <div className="relative">
                  <User className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: أحمد محمد أو شركة التقنية المتقدمة"
                    required
                  />
                </div>
              </div>

              {/* البريد الإلكتروني */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني *
                </label>
                <div className="relative">
                  <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              {/* كلمة المرور */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور *
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="w-full pr-4 pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="كلمة مرور قوية (6 أحرف على الأقل)"
                    required
                    minLength={6}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              {/* رقم الهاتف */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الهاتف
                </label>
                <div className="relative">
                  <Phone className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+966 50 123 4567"
                  />
                </div>
              </div>

              {/* جهة الاتصال */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم جهة الاتصال
                </label>
                <div className="relative">
                  <Building className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    name="contact_person"
                    value={formData.contact_person}
                    onChange={handleInputChange}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اسم الشخص المسؤول (للشركات)"
                  />
                </div>
              </div>
            </div>

            {/* نوع الحساب */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع الحساب
              </label>
              <div className="grid grid-cols-2 gap-4">
                <label className="relative">
                  <input
                    type="radio"
                    name="account_type"
                    value="customer"
                    checked={formData.account_type === 'customer'}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    formData.account_type === 'customer'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <User className={`w-5 h-5 ${
                        formData.account_type === 'customer' ? 'text-blue-600' : 'text-gray-400'
                      }`} />
                      <div>
                        <p className={`font-medium ${
                          formData.account_type === 'customer' ? 'text-blue-900' : 'text-gray-700'
                        }`}>
                          عميل
                        </p>
                        <p className="text-sm text-gray-500">حساب عميل عادي</p>
                      </div>
                    </div>
                  </div>
                </label>

                <label className="relative">
                  <input
                    type="radio"
                    name="account_type"
                    value="admin"
                    checked={formData.account_type === 'admin'}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    formData.account_type === 'admin'
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Shield className={`w-5 h-5 ${
                        formData.account_type === 'admin' ? 'text-purple-600' : 'text-gray-400'
                      }`} />
                      <div>
                        <p className={`font-medium ${
                          formData.account_type === 'admin' ? 'text-purple-900' : 'text-gray-700'
                        }`}>
                          مدير
                        </p>
                        <p className="text-sm text-gray-500">حساب إداري</p>
                      </div>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* حالة الحساب */}
            <div className="flex items-center space-x-3 space-x-reverse p-4 bg-gray-50 rounded-lg">
              <input
                type="checkbox"
                name="is_active"
                id="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                تفعيل الحساب (سيتمكن المستخدم من تسجيل الدخول)
              </label>
            </div>
          </form>
        </div>

        {/* أزرار التحكم */}
        <div className="flex items-center justify-end space-x-3 space-x-reverse p-6 border-t border-gray-200 bg-gray-50">
          <button
            type="button"
            onClick={onClose}
            className="px-6 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            إلغاء
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2 space-x-reverse"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>جاري الحفظ...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>حفظ العميل</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default AddCustomerModal
