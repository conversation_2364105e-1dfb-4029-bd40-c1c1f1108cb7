import React from 'react'
import useStore from '../../store/useStore'

const ForceSkip = ({ onSkip }) => {
  const handleForceSkip = () => {
    // إجبار النظام على الانتقال للوحة التحكم
    useStore.setState({
      user: { 
        email: '<EMAIL>', 
        id: '1',
        name: 'مؤسسة الأمل الخيرية',
        organization_name: 'مؤسسة الأمل الخيرية',
        membership_level: 'premium'
      },
      isAuthenticated: true,
      isLoading: false,
      session: { 
        user: { 
          email: '<EMAIL>',
          id: '1'
        } 
      }
    })
    
    // استدعاء دالة التجاوز
    if (onSkip) {
      onSkip()
    }
  }

  return (
    <div className="fixed top-4 right-4 z-50">
      <button
        onClick={handleForceSkip}
        className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg font-bold text-sm"
      >
        🚀 دخول مباشر
      </button>
    </div>
  )
}

export default ForceSkip
