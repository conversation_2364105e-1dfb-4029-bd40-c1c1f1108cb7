name: Build and Test

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  # Build and test job
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint
      continue-on-error: true

    - name: Build application
      run: npm run build
      env:
        VITE_APP_NAME: نماء الاحترافية
        VITE_APP_VERSION: 1.0.0
        VITE_APP_ENVIRONMENT: production

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dist-files
        path: dist/
        retention-days: 7

