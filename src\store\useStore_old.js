import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase } from '../lib/supabase'
import toast from 'react-hot-toast'

// متجر الحالة الرئيسي
const useStore = create(
  persist(
    (set, get) => ({
      // حالة المصادقة
      user: null,
      session: null,
      isLoading: false,
      isAuthenticated: false,

      // حالة الطلبات
      orders: [],
      selectedOrder: null,
      ordersLoading: false,
      selectedOrderLoading: false,

      // حالة الإشعارات
      notifications: [],
      unreadCount: 0,
      notificationsLoading: false,

      // حالة واجهة المستخدم
      sidebarOpen: true,
      theme: 'light',
      language: 'ar',

      // ==================== دوال واجهة المستخدم ====================

      // تبديل الثيم
      toggleTheme: () => {
        set((state) => ({
          theme: state.theme === 'light' ? 'dark' : 'light'
        }))
      },

      // تبديل الشريط الجانبي
      toggleSidebar: () => {
        set((state) => ({
          sidebarOpen: !state.sidebarOpen
        }))
      },

      // الاشتراكات المباشرة
      subscriptions: [],

      // ==================== دوال المصادقة ====================
      
      // تسجيل الدخول
      signIn: async (email, password) => {
        set({ isLoading: true })
        
        try {
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
          })

          if (error) {
            toast.error(error.message || 'فشل في تسجيل الدخول')
            return { success: false, error: error.message }
          }

          // الحصول على بيانات المستخدم الكاملة
          const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

          if (profileError) {
            console.error('Error loading profile:', profileError)
          }
          
          set({
            user: { ...data.user, ...profile },
            session: data.session,
            isAuthenticated: true,
            isLoading: false
          })

          // بدء الاشتراكات المباشرة
          get().startRealtimeSubscriptions()

          // تحميل البيانات الأولية
          get().loadUserData()

          toast.success(`مرحباً بك ${profile?.name || user.email}`)
          return { success: true }
          
        } catch (error) {
          set({ isLoading: false })
          toast.error('حدث خطأ في تسجيل الدخول')
          return { success: false, error: error.message }
        }
      },

      // تسجيل الخروج
      signOut: async () => {
        set({ isLoading: true })
        
        try {
          // إيقاف الاشتراكات المباشرة
          get().stopRealtimeSubscriptions()
          
          const { error } = await supabase.auth.signOut()
          
          if (error) {
            toast.error(error)
            return
          }

          // مسح الحالة
          set({
            user: null,
            session: null,
            isAuthenticated: false,
            requests: [],
            selectedRequest: null,
            notifications: [],
            unreadCount: 0,
            isLoading: false
          })

          toast.success('تم تسجيل الخروج بنجاح')
          
        } catch (error) {
          set({ isLoading: false })
          toast.error('حدث خطأ في تسجيل الخروج')
        }
      },

      // التحقق من حالة المصادقة
      checkAuth: async () => {
        try {
          console.log('checkAuth: Starting...')
          set({ isLoading: true })

          // التحقق من الجلسة الحالية
          const { data: { session }, error: sessionError } = await supabase.auth.getSession()

          if (sessionError || !session || !session.user) {
            console.log('checkAuth: No valid session found', sessionError)
            set({
              isAuthenticated: false,
              user: null,
              session: null,
              isLoading: false
            })
            return false
          }

          console.log('checkAuth: Valid session found for:', session.user.email)

          // استخدام بيانات المستخدم من الجلسة مباشرة
          set({
            user: session.user,
            session: session,
            isAuthenticated: true,
            isLoading: false
          })

          console.log('checkAuth: User state updated successfully')

          // تحميل بيانات إضافية بدون انتظار
          setTimeout(() => {
            console.log('checkAuth: Loading user data...')
            get().loadUserData()
          }, 100)

          console.log('checkAuth: Returning true')
          return true

        } catch (error) {
          console.error('checkAuth: Error occurred:', error)
          set({
            isAuthenticated: false,
            user: null,
            session: null,
            isLoading: false
          })
          return false
        }
      },

      // ==================== دوال الطلبات ====================
      
      // تحميل طلبات المستخدم
      loadOrders: async () => {
        const { user } = get()
        if (!user) return

        set({ ordersLoading: true })

        try {
          // استعلام مباشر من Supabase
          const { data, error } = await supabase
            .from('orders')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })

          if (error) {
            console.error('Error loading orders:', error)
            toast.error('فشل في تحميل الطلبات')
            return
          }

          set({ orders: data || [], ordersLoading: false })

        } catch (error) {
          console.error('Error in loadOrders:', error)
          set({ ordersLoading: false })
          toast.error('حدث خطأ في تحميل الطلبات')
        }
      },

      // تحديد طلب محدد
      selectOrder: async (orderId) => {
        set({ selectedOrderLoading: true })

        try {
          const { data, error } = await supabase
            .from('orders')
            .select('*')
            .eq('id', orderId)
            .single()

          if (error) {
            console.error('Error loading order:', error)
            toast.error('فشل في تحميل تفاصيل الطلب')
            set({ selectedOrderLoading: false })
            return
          }

          set({
            selectedOrder: data,
            selectedOrderLoading: false
          })

        } catch (error) {
          console.error('Error in selectOrder:', error)
          set({ selectedOrderLoading: false })
          toast.error('حدث خطأ في تحميل تفاصيل الطلب')
        }
      },

      // تحديث حالة الطلب
      updateOrderStatus: async (orderId, status) => {
        try {
          const { data, error } = await supabase
            .from('orders')
            .update({ status, updated_at: new Date().toISOString() })
            .eq('id', orderId)
            .select()
            .single()

          if (error) {
            toast.error('فشل في تحديث حالة الطلب')
            return
          }

          // تحديث الطلب في القائمة
          set(state => ({
            orders: state.orders.map(order =>
              order.id === orderId ? { ...order, ...data } : order
            ),
            selectedOrder: state.selectedOrder?.id === orderId
              ? { ...state.selectedOrder, ...data }
              : state.selectedOrder
          }))

          toast.success('تم تحديث حالة الطلب بنجاح')

        } catch (error) {
          toast.error('حدث خطأ في تحديث حالة الطلب')
        }
      },

      // ==================== دوال الإشعارات ====================
      
      // تحميل الإشعارات
      loadNotifications: async () => {
        const { user } = get()
        if (!user) return

        set({ notificationsLoading: true })

        try {
          const { data, error } = await supabase
            .from('notifications')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })

          if (error) {
            console.error('فشل في تحميل الإشعارات:', error)
            return
          }

          const unreadCount = data?.filter(n => n.status === 'unread').length || 0

          set({
            notifications: data || [],
            unreadCount,
            notificationsLoading: false
          })

        } catch (error) {
          set({ notificationsLoading: false })
          console.error('حدث خطأ في تحميل الإشعارات:', error)
        }
      },

      // إضافة إشعار جديد
      addNotification: async (notification) => {
        const { user } = get()
        if (!user) return

        try {
          const newNotification = {
            user_id: user.id,
            status: 'unread',
            created_at: new Date().toISOString(),
            ...notification
          }

          const { data, error } = await supabase
            .from('notifications')
            .insert(newNotification)
            .select()
            .single()

          if (error) {
            console.error('فشل في إضافة الإشعار:', error)
            return
          }

          // إضافة الإشعار للقائمة المحلية
          set(state => ({
            notifications: [data, ...state.notifications],
            unreadCount: state.unreadCount + 1
          }))

        } catch (error) {
          console.error('حدث خطأ في إضافة الإشعار:', error)
        }
      },

      // تحديد الإشعار كمقروء
      markNotificationAsRead: async (notificationId) => {
        try {
          const { error } = await supabase
            .from('notifications')
            .update({ status: 'read', read_at: new Date().toISOString() })
            .eq('id', notificationId)

          if (error) {
            console.error('فشل في تحديث الإشعار:', error)
            return
          }

          set(state => ({
            notifications: state.notifications.map(n =>
              n.id === notificationId ? { ...n, status: 'read' } : n
            ),
            unreadCount: Math.max(0, state.unreadCount - 1)
          }))

        } catch (error) {
          console.error('حدث خطأ في تحديث الإشعار:', error)
        }
      },

      // ==================== دوال واجهة المستخدم ====================
      
      // تبديل الشريط الجانبي
      toggleSidebar: () => {
        set(state => ({ sidebarOpen: !state.sidebarOpen }))
      },

      // تبديل المظهر
      toggleTheme: () => {
        set((state) => ({
          theme: state.theme === 'light' ? 'dark' : 'light'
        }))
      },

      // تغيير المظهر
      setTheme: (theme) => {
        set({ theme })
      },

      // تغيير اللغة
      setLanguage: (language) => {
        set({ language })
      },

      // ==================== الاشتراكات المباشرة ====================

      // بدء الاشتراكات المباشرة
      startRealtimeSubscriptions: () => {
        console.log('Starting realtime subscriptions...')
        // يمكن إضافة اشتراكات realtime هنا لاحقاً
      },

      // إيقاف الاشتراكات المباشرة
      stopRealtimeSubscriptions: () => {
        console.log('Stopping realtime subscriptions...')
        // يمكن إضافة إيقاف الاشتراكات هنا لاحقاً
      },

      // ==================== دوال مساعدة ====================
      
      // تحميل جميع بيانات المستخدم
      loadUserData: async () => {
        try {
          const { user } = get()
          if (!user) return

          console.log('Loading user data for:', user.email)

          // تحميل البيانات بدون انتظار
          get().loadOrders().catch(console.error)
          get().loadNotifications().catch(console.error)

        } catch (error) {
          console.error('Error in loadUserData:', error)
        }
      },



      // إعادة تعيين الحالة
      reset: () => {
        set({
          user: null,
          session: null,
          isLoading: false,
          isAuthenticated: false,
          orders: [],
          selectedOrder: null,
          ordersLoading: false,
          selectedOrderLoading: false,
          notifications: [],
          unreadCount: 0,
          notificationsLoading: false
        })
      }
    }),
    {
      name: 'nama-store',
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        sidebarOpen: state.sidebarOpen
      })
    }
  )
)

export default useStore
