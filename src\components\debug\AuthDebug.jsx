import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import useStore from '../../store/useStore'

const AuthDebug = () => {
  const [debugInfo, setDebugInfo] = useState({})
  const { user, isAuthenticated, session, isLoading } = useStore()

  useEffect(() => {
    const checkAuthState = async () => {
      try {
        // التحقق من الجلسة
        const { data: { session }, error } = await supabase.auth.getSession()

        // التحقق من المستخدم
        const { data: { user }, error: userError } = await supabase.auth.getUser()

        setDebugInfo({
          hasSession: !!session,
          sessionUser: session?.user?.email || 'لا يوجد',
          currentUser: user?.email || 'لا يوجد',
          storeUser: useStore.getState().user?.email || 'لا يوجد',
          isAuthenticated,
          isLoading,
          sessionError: error?.message || 'لا يوجد',
          userError: userError?.message || 'لا يوجد',
          timestamp: new Date().toLocaleTimeString('ar-SA'),
          storeState: {
            hasUser: !!useStore.getState().user,
            hasSession: !!useStore.getState().session,
            isAuth: useStore.getState().isAuthenticated,
            isLoad: useStore.getState().isLoading
          }
        })
      } catch (error) {
        setDebugInfo({
          error: error.message,
          timestamp: new Date().toLocaleTimeString('ar-SA')
        })
      }
    }

    checkAuthState()
    const interval = setInterval(checkAuthState, 1000) // تحديث كل ثانية

    return () => clearInterval(interval)
  }, [user, isAuthenticated, session, isLoading])

  const handleClearStorage = () => {
    localStorage.clear()
    sessionStorage.clear()
    window.location.reload()
  }

  const handleForceReauth = async () => {
    try {
      await supabase.auth.signOut()
      useStore.getState().reset()
      window.location.reload()
    } catch (error) {
      console.error('Error during force reauth:', error)
    }
  }

  const handleForceLogin = () => {
    useStore.setState({
      user: { email: '<EMAIL>', id: '123' },
      isAuthenticated: true,
      isLoading: false,
      session: { user: { email: '<EMAIL>' } }
    })
  }

  const handleSkipLoading = () => {
    useStore.setState({ isLoading: false })
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm text-sm" dir="rtl">
      <h3 className="font-bold text-gray-900 mb-2">🔍 تشخيص المصادقة</h3>
      
      <div className="space-y-1 text-xs">
        <div>
          <strong>الوقت:</strong> {debugInfo.timestamp}
        </div>
        <div>
          <strong>جلسة موجودة:</strong> 
          <span className={debugInfo.hasSession ? 'text-green-600' : 'text-red-600'}>
            {debugInfo.hasSession ? ' ✅' : ' ❌'}
          </span>
        </div>
        <div>
          <strong>مستخدم الجلسة:</strong> {debugInfo.sessionUser}
        </div>
        <div>
          <strong>المستخدم الحالي:</strong> {debugInfo.currentUser}
        </div>
        <div>
          <strong>مستخدم المتجر:</strong> {debugInfo.storeUser}
        </div>
        <div>
          <strong>مصادق عليه:</strong> 
          <span className={debugInfo.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
            {debugInfo.isAuthenticated ? ' ✅' : ' ❌'}
          </span>
        </div>
        <div>
          <strong>جاري التحميل:</strong> 
          <span className={debugInfo.isLoading ? 'text-yellow-600' : 'text-gray-600'}>
            {debugInfo.isLoading ? ' ⏳' : ' ⏹️'}
          </span>
        </div>
        
        {debugInfo.sessionError !== 'لا يوجد' && (
          <div className="text-red-600">
            <strong>خطأ الجلسة:</strong> {debugInfo.sessionError}
          </div>
        )}
        
        {debugInfo.userError !== 'لا يوجد' && (
          <div className="text-red-600">
            <strong>خطأ المستخدم:</strong> {debugInfo.userError}
          </div>
        )}
        
        {debugInfo.error && (
          <div className="text-red-600">
            <strong>خطأ عام:</strong> {debugInfo.error}
          </div>
        )}
      </div>

      <div className="mt-3 space-y-2">
        <button
          onClick={handleClearStorage}
          className="w-full bg-yellow-500 text-white px-2 py-1 rounded text-xs hover:bg-yellow-600"
        >
          مسح التخزين المحلي
        </button>
        <button
          onClick={handleForceReauth}
          className="w-full bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600"
        >
          إعادة تعيين المصادقة
        </button>
        <button
          onClick={handleSkipLoading}
          className="w-full bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600"
        >
          تخطي التحميل
        </button>
        <button
          onClick={handleForceLogin}
          className="w-full bg-green-500 text-white px-2 py-1 rounded text-xs hover:bg-green-600"
        >
          تسجيل دخول تجريبي
        </button>
      </div>
    </div>
  )
}

export default AuthDebug
