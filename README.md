# نماء الاحترافية - نظام إدارة العملاء

نظام متطور لإدارة طلبات العملاء وتتبع مراحل العمل، مصمم خصيصاً للجمعيات الخيرية والمؤسسات غير الربحية.

## 🌟 الميزات الرئيسية

### 🔐 نظام المصادقة المتقدم
- تسجيل دخول آمن مع Supabase Auth
- إدارة الجلسات والتذكر التلقائي
- حماية متقدمة للبيانات

### 📊 لوحة التحكم الذكية
- إحصائيات شاملة ومؤشرات الأداء
- تتبع مراحل العمل بصرياً
- تنبيهات ذكية للإجراءات المطلوبة

### 📋 إدارة الطلبات المتطورة
- تتبع دقيق لمراحل العمل
- نظام الوثائق والمرفقات
- التواصل المباشر مع الفريق
- تقارير مفصلة عن التقدم

### 🔔 نظام الإشعارات المباشرة
- إشعارات فورية للتحديثات
- تنبيهات للمواعيد النهائية
- تحديثات مباشرة عبر Realtime

### 📱 تصميم متجاوب
- واجهة عربية كاملة مع RTL
- تصميم متجاوب لجميع الأجهزة
- تجربة مستخدم سلسة ومتطورة

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** - مكتبة واجهة المستخدم
- **Vite** - أداة البناء السريعة
- **Tailwind CSS** - إطار عمل التصميم
- **Lucide React** - مكتبة الأيقونات
- **Zustand** - إدارة الحالة
- **React Hot Toast** - نظام الإشعارات

### Backend & Database
- **Supabase** - قاعدة البيانات والمصادقة
- **PostgreSQL** - قاعدة البيانات الرئيسية
- **Row Level Security** - حماية البيانات
- **Realtime Subscriptions** - التحديثات المباشرة

### Deployment
- **Netlify** - استضافة التطبيق
- **GitHub Actions** - التكامل المستمر
- **Environment Variables** - إدارة الإعدادات

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
- Node.js 18+ 
- npm أو yarn
- حساب Supabase
- حساب Netlify (للنشر)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/nama-client-system.git
cd nama-client-system
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
```

4. **تحديث ملف .env**
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

5. **تشغيل التطبيق**
```bash
npm run dev
```

## 🗄️ إعداد قاعدة البيانات

### إنشاء الجداول في Supabase

```sql
-- جدول المستخدمين
CREATE TABLE users (
  id UUID REFERENCES auth.users PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  phone TEXT,
  organization_name TEXT,
  logo TEXT,
  membership_level TEXT DEFAULT 'basic',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الطلبات
CREATE TABLE requests (
  id TEXT PRIMARY KEY,
  client_id UUID REFERENCES users(id),
  service TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  priority TEXT DEFAULT 'medium',
  progress INTEGER DEFAULT 0,
  current_step TEXT,
  submission_date DATE DEFAULT CURRENT_DATE,
  expected_completion DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الإشعارات
CREATE TABLE notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  title TEXT NOT NULL,
  message TEXT,
  type TEXT DEFAULT 'info',
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### إعداد Row Level Security

```sql
-- تفعيل RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can view own requests" ON requests
  FOR SELECT USING (auth.uid() = client_id);

CREATE POLICY "Users can view own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);
```

## 📦 البناء والنشر

### بناء التطبيق للإنتاج
```bash
npm run build
```

### النشر على Netlify

1. **ربط المشروع بـ GitHub**
2. **إعداد متغيرات البيئة في Netlify**
3. **تفعيل النشر التلقائي**

```bash
# أوامر البناء في Netlify
Build command: npm run build
Publish directory: dist
```

## 🔧 التخصيص والتطوير

### إضافة ميزات جديدة
1. إنشاء المكونات في `src/components/`
2. إضافة الحالة في `src/store/useStore.js`
3. تحديث قاعدة البيانات حسب الحاجة

### تخصيص التصميم
- تعديل ألوان العلامة التجارية في `tailwind.config.js`
- إضافة أنماط مخصصة في `src/index.css`
- تحديث الشعار والأيقونات

## 🧪 الاختبار

### حسابات تجريبية
```
البريد: <EMAIL>
كلمة المرور: 123456

البريد: <EMAIL>  
كلمة المرور: 123456
```

### تشغيل الاختبارات
```bash
npm run test
```

## 📚 الوثائق

### هيكل المشروع
```
src/
├── components/          # مكونات واجهة المستخدم
│   ├── auth/           # مكونات المصادقة
│   ├── ui/             # مكونات واجهة المستخدم الأساسية
│   └── dashboard/      # مكونات لوحة التحكم
├── lib/                # المكتبات والأدوات
├── store/              # إدارة الحالة
├── styles/             # ملفات التصميم
└── utils/              # دوال مساعدة
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **الموقع الإلكتروني**: [nama-professional.com](https://nama-professional.com)
- **البريد الإلكتروني**: <EMAIL>
- **تويتر**: [@nama_professional](https://twitter.com/nama_professional)

---

**تم التطوير بـ ❤️ بواسطة فريق نماء الاحترافية**
