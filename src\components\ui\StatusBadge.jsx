import React from 'react'
import { 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  CheckSquare,
  XCircle,
  Pause
} from 'lucide-react'

const StatusBadge = ({ status, size = 'default', showIcon = true }) => {
  const statusConfig = {
    'action-required': {
      color: 'bg-orange-100 text-orange-800 border-orange-200',
      icon: AlertCircle,
      label: 'يتطلب إجراء'
    },
    'on-track': {
      color: 'bg-green-100 text-green-800 border-green-200',
      icon: CheckCircle,
      label: 'سير طبيعي'
    },
    'delayed': {
      color: 'bg-red-100 text-red-800 border-red-200',
      icon: Clock,
      label: 'متأخر'
    },
    'completed': {
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      icon: CheckSquare,
      label: 'مكتمل'
    },
    'cancelled': {
      color: 'bg-gray-100 text-gray-800 border-gray-200',
      icon: XCircle,
      label: 'ملغي'
    },
    'paused': {
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      icon: Pause,
      label: 'متوقف مؤقتاً'
    },
    'in-progress': {
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      icon: Clock,
      label: 'قيد التنفيذ'
    },
    'pending': {
      color: 'bg-gray-100 text-gray-800 border-gray-200',
      icon: Clock,
      label: 'في الانتظار'
    }
  }

  const sizeClasses = {
    small: 'px-2 py-0.5 text-xs',
    default: 'px-3 py-1 text-sm',
    large: 'px-4 py-2 text-base'
  }

  const iconSizes = {
    small: 'w-3 h-3',
    default: 'w-4 h-4',
    large: 'w-5 h-5'
  }

  const config = statusConfig[status] || statusConfig['pending']
  const Icon = config.icon

  return (
    <span className={`inline-flex items-center rounded-full font-medium border ${config.color} ${sizeClasses[size]} transition-all duration-200`}>
      {showIcon && <Icon className={`${iconSizes[size]} ml-1`} />}
      {config.label}
    </span>
  )
}

export default StatusBadge
