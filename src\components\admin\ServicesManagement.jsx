import React, { useState, useEffect } from 'react'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  Eye,
  Settings,
  Globe,
  Palette,
  Smartphone,
  ShoppingCart,
  BarChart3,
  Shield,
  FileText
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import ServiceItemsManagement from './ServiceItemsManagement'
import toast from 'react-hot-toast'

const ServicesManagement = () => {
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedService, setSelectedService] = useState(null)
  const [showItemsManagement, setShowItemsManagement] = useState(false)
  const [selectedServiceForItems, setSelectedServiceForItems] = useState(null)

  // بيانات الخدمة الجديدة
  const [newService, setNewService] = useState({
    title: '',
    description: '',
    category: 'development',
    icon: 'Globe',
    color: 'blue',
    base_price: '',
    duration_weeks: ''
  })

  // الأيقونات المتاحة
  const availableIcons = [
    { name: 'Globe', component: Globe },
    { name: 'Palette', component: Palette },
    { name: 'Smartphone', component: Smartphone },
    { name: 'ShoppingCart', component: ShoppingCart },
    { name: 'BarChart3', component: BarChart3 },
    { name: 'Shield', component: Shield },
    { name: 'FileText', component: FileText }
  ]

  // الألوان المتاحة
  const availableColors = [
    { name: 'blue', class: 'bg-blue-500' },
    { name: 'purple', class: 'bg-purple-500' },
    { name: 'green', class: 'bg-green-500' },
    { name: 'orange', class: 'bg-orange-500' },
    { name: 'pink', class: 'bg-pink-500' },
    { name: 'red', class: 'bg-red-500' }
  ]

  // الفئات المتاحة
  const categories = [
    { id: 'all', label: 'جميع الفئات' },
    { id: 'development', label: 'التطوير' },
    { id: 'design', label: 'التصميم' },
    { id: 'marketing', label: 'التسويق' },
    { id: 'security', label: 'الأمان' }
  ]

  // تحميل الخدمات
  const loadServices = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setServices(data || [])
    } catch (error) {
      console.error('خطأ في تحميل الخدمات:', error)
      toast.error('فشل في تحميل الخدمات')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadServices()
  }, [])

  // إضافة خدمة جديدة
  const handleAddService = async () => {
    try {
      if (!newService.title || !newService.description) {
        toast.error('يرجى ملء جميع الحقول المطلوبة')
        return
      }

      const { error } = await supabase
        .from('services')
        .insert([{
          title: newService.title,
          description: newService.description,
          category: newService.category,
          icon: newService.icon,
          color: newService.color,
          base_price: parseFloat(newService.base_price) || null,
          duration_weeks: parseInt(newService.duration_weeks) || null
        }])

      if (error) throw error

      toast.success('تم إضافة الخدمة بنجاح')
      setShowAddModal(false)
      setNewService({
        title: '',
        description: '',
        category: 'development',
        icon: 'Globe',
        color: 'blue',
        base_price: '',
        duration_weeks: ''
      })
      loadServices()
    } catch (error) {
      console.error('خطأ في إضافة الخدمة:', error)
      toast.error('فشل في إضافة الخدمة')
    }
  }

  // تحديث خدمة
  const handleUpdateService = async () => {
    try {
      if (!selectedService.title || !selectedService.description) {
        toast.error('يرجى ملء جميع الحقول المطلوبة')
        return
      }

      const { error } = await supabase
        .from('services')
        .update({
          title: selectedService.title,
          description: selectedService.description,
          category: selectedService.category,
          icon: selectedService.icon,
          color: selectedService.color,
          base_price: parseFloat(selectedService.base_price) || null,
          duration_weeks: parseInt(selectedService.duration_weeks) || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedService.id)

      if (error) throw error

      toast.success('تم تحديث الخدمة بنجاح')
      setShowEditModal(false)
      setSelectedService(null)
      loadServices()
    } catch (error) {
      console.error('خطأ في تحديث الخدمة:', error)
      toast.error('فشل في تحديث الخدمة')
    }
  }

  // حذف خدمة
  const handleDeleteService = async (serviceId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الخدمة؟ سيتم حذف جميع عناصرها أيضاً.')) {
      return
    }

    try {
      const { error } = await supabase
        .from('services')
        .delete()
        .eq('id', serviceId)

      if (error) throw error

      toast.success('تم حذف الخدمة بنجاح')
      loadServices()
    } catch (error) {
      console.error('خطأ في حذف الخدمة:', error)
      toast.error('فشل في حذف الخدمة')
    }
  }

  // تبديل حالة الخدمة
  const toggleServiceStatus = async (serviceId, currentStatus) => {
    try {
      const { error } = await supabase
        .from('services')
        .update({ 
          is_active: !currentStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', serviceId)

      if (error) throw error

      toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} الخدمة`)
      loadServices()
    } catch (error) {
      console.error('خطأ في تغيير حالة الخدمة:', error)
      toast.error('فشل في تغيير حالة الخدمة')
    }
  }

  // فلترة الخدمات
  const filteredServices = services.filter(service => {
    const matchesSearch = service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = filterCategory === 'all' || service.category === filterCategory
    return matchesSearch && matchesCategory
  })

  // الحصول على مكون الأيقونة
  const getIconComponent = (iconName) => {
    const icon = availableIcons.find(i => i.name === iconName)
    return icon ? icon.component : Globe
  }

  // عرض إدارة عناصر الخدمة
  if (showItemsManagement && selectedServiceForItems) {
    return (
      <ServiceItemsManagement
        service={selectedServiceForItems}
        onBack={() => {
          setShowItemsManagement(false)
          setSelectedServiceForItems(null)
        }}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* العنوان والإجراءات */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الخدمات</h1>
          <p className="text-gray-600 mt-1">إضافة وتعديل وحذف الخدمات المتاحة</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>إضافة خدمة جديدة</span>
        </button>
      </div>

      {/* شريط البحث والفلاتر */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* البحث */}
          <div className="flex-1 relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="البحث في الخدمات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* فلتر الفئة */}
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* قائمة الخدمات */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-4">جاري تحميل الخدمات...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredServices.map((service) => {
            const IconComponent = getIconComponent(service.icon)
            return (
              <div key={service.id} className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300">
                {/* رأس البطاقة */}
                <div className={`bg-gradient-to-r from-${service.color}-500 to-${service.color}-600 p-6 text-white rounded-t-xl`}>
                  <div className="flex items-center justify-between mb-4">
                    <IconComponent className="w-8 h-8" />
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        service.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {service.is_active ? 'نشط' : 'غير نشط'}
                      </span>
                    </div>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                  <p className="text-sm opacity-90 line-clamp-2">{service.description}</p>
                </div>

                {/* محتوى البطاقة */}
                <div className="p-6">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>الفئة: {categories.find(c => c.id === service.category)?.label}</span>
                      {service.base_price && (
                        <span>من {service.base_price.toLocaleString()} ريال</span>
                      )}
                    </div>
                    
                    {service.duration_weeks && (
                      <div className="text-sm text-gray-600">
                        المدة: {service.duration_weeks} أسبوع
                      </div>
                    )}

                    {/* أزرار الإجراءات */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => {
                            setSelectedServiceForItems(service)
                            setShowItemsManagement(true)
                          }}
                          className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                          title="إدارة العناصر"
                        >
                          <Settings className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedService(service)
                            setShowEditModal(true)
                          }}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          title="تعديل"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteService(service.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                      
                      <button
                        onClick={() => toggleServiceStatus(service.id, service.is_active)}
                        className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                          service.is_active 
                            ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                            : 'bg-green-100 text-green-700 hover:bg-green-200'
                        }`}
                      >
                        {service.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* رسالة عدم وجود نتائج */}
      {!loading && filteredServices.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد خدمات</h3>
          <p className="text-gray-500">لا توجد خدمات تطابق معايير البحث</p>
        </div>
      )}

      {/* نموذج إضافة خدمة جديدة */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-6">إضافة خدمة جديدة</h2>

            <div className="space-y-4">
              {/* عنوان الخدمة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">عنوان الخدمة *</label>
                <input
                  type="text"
                  value={newService.title}
                  onChange={(e) => setNewService({...newService, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="مثال: تطوير المواقع الإلكترونية"
                />
              </div>

              {/* وصف الخدمة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">وصف الخدمة *</label>
                <textarea
                  value={newService.description}
                  onChange={(e) => setNewService({...newService, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="وصف مفصل للخدمة..."
                />
              </div>

              {/* الفئة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                <select
                  value={newService.category}
                  onChange={(e) => setNewService({...newService, category: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="development">التطوير</option>
                  <option value="design">التصميم</option>
                  <option value="marketing">التسويق</option>
                  <option value="security">الأمان</option>
                </select>
              </div>

              {/* الأيقونة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الأيقونة</label>
                <div className="grid grid-cols-4 gap-2">
                  {availableIcons.map((icon) => {
                    const IconComponent = icon.component
                    return (
                      <button
                        key={icon.name}
                        type="button"
                        onClick={() => setNewService({...newService, icon: icon.name})}
                        className={`p-3 rounded-lg border-2 transition-colors ${
                          newService.icon === icon.name
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <IconComponent className="w-6 h-6 mx-auto" />
                      </button>
                    )
                  })}
                </div>
              </div>

              {/* اللون */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                <div className="flex space-x-2 space-x-reverse">
                  {availableColors.map((color) => (
                    <button
                      key={color.name}
                      type="button"
                      onClick={() => setNewService({...newService, color: color.name})}
                      className={`w-8 h-8 rounded-full ${color.class} border-2 ${
                        newService.color === color.name ? 'border-gray-800' : 'border-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>

              {/* السعر والمدة */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السعر الأساسي (ريال)</label>
                  <input
                    type="number"
                    value={newService.base_price}
                    onChange={(e) => setNewService({...newService, base_price: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="5000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المدة (أسابيع)</label>
                  <input
                    type="number"
                    value={newService.duration_weeks}
                    onChange={(e) => setNewService({...newService, duration_weeks: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="4"
                  />
                </div>
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex items-center justify-end space-x-3 space-x-reverse mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleAddService}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                إضافة الخدمة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تعديل الخدمة */}
      {showEditModal && selectedService && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-6">تعديل الخدمة</h2>

            <div className="space-y-4">
              {/* عنوان الخدمة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">عنوان الخدمة *</label>
                <input
                  type="text"
                  value={selectedService.title}
                  onChange={(e) => setSelectedService({...selectedService, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* وصف الخدمة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">وصف الخدمة *</label>
                <textarea
                  value={selectedService.description}
                  onChange={(e) => setSelectedService({...selectedService, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* الفئة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                <select
                  value={selectedService.category}
                  onChange={(e) => setSelectedService({...selectedService, category: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="development">التطوير</option>
                  <option value="design">التصميم</option>
                  <option value="marketing">التسويق</option>
                  <option value="security">الأمان</option>
                </select>
              </div>

              {/* الأيقونة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الأيقونة</label>
                <div className="grid grid-cols-4 gap-2">
                  {availableIcons.map((icon) => {
                    const IconComponent = icon.component
                    return (
                      <button
                        key={icon.name}
                        type="button"
                        onClick={() => setSelectedService({...selectedService, icon: icon.name})}
                        className={`p-3 rounded-lg border-2 transition-colors ${
                          selectedService.icon === icon.name
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <IconComponent className="w-6 h-6 mx-auto" />
                      </button>
                    )
                  })}
                </div>
              </div>

              {/* اللون */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                <div className="flex space-x-2 space-x-reverse">
                  {availableColors.map((color) => (
                    <button
                      key={color.name}
                      type="button"
                      onClick={() => setSelectedService({...selectedService, color: color.name})}
                      className={`w-8 h-8 rounded-full ${color.class} border-2 ${
                        selectedService.color === color.name ? 'border-gray-800' : 'border-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>

              {/* السعر والمدة */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السعر الأساسي (ريال)</label>
                  <input
                    type="number"
                    value={selectedService.base_price || ''}
                    onChange={(e) => setSelectedService({...selectedService, base_price: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المدة (أسابيع)</label>
                  <input
                    type="number"
                    value={selectedService.duration_weeks || ''}
                    onChange={(e) => setSelectedService({...selectedService, duration_weeks: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex items-center justify-end space-x-3 space-x-reverse mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setSelectedService(null)
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleUpdateService}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                حفظ التغييرات
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ServicesManagement
