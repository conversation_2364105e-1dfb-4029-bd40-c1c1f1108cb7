# 🚀 نشر فوري على Netlify

## زر النشر السريع

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/sh33hemam/nama-3meel)

---

## 📋 خطوات النشر السريع

### 1. انقر على الزر أعلاه
سيأخذك مباشرة إلى Netlify مع المشروع جاهز للنشر

### 2. اتبع الخطوات
1. **Connect to GitHub** - اربط حسابك
2. **Configure** - اختر اسم للموقع
3. **Deploy** - انقر نشر

### 3. أضف متغيرات البيئة
بعد النشر، اذهب إلى **Site settings** > **Environment variables**:

```env
VITE_APP_NAME=نماء الاحترافية
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production
```

### 4. أعد النشر
انقر **Trigger deploy** لتطبيق المتغيرات

---

## 🎯 النتيجة المتوقعة

- ✅ موقع جاهز خلال 3-5 دقائق
- ✅ رابط مثل: `https://nama-3meel.netlify.app`
- ✅ تحديثات تلقائية من GitHub
- ✅ HTTPS مفعل تلقائياً

---

## 🧪 اختبار سريع

### الحسابات التجريبية:
```
البريد: <EMAIL>
كلمة المرور: 123456

البريد: <EMAIL>
كلمة المرور: 123456
```

---

## 📞 مساعدة

**مشاكل؟** راجع [NETLIFY_DEPLOY.md](NETLIFY_DEPLOY.md) للتفاصيل الكاملة

**دعم فني:** <EMAIL>
