import React, { useState, useEffect } from 'react'
import {
  Users,
  Settings,
  Bar<PERSON>hart3,
  FileText,
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  ChevronRight,
  ChevronDown,
  Calendar,
  Activity,
  AlertCircle,
  Shield,
  Link,
  Unlink
} from 'lucide-react'
import PageHeader from '../layout/PageHeader'
import CustomerManagement from './CustomerManagement'
import CustomerServiceManagement from './CustomerServiceManagement'
import CustomerServiceItemsManagement from './CustomerServiceItemsManagement'
import ServiceDataManagement from './ServiceDataManagement'
import { supabase } from '../../lib/supabase'
import useStore from '../../store/useStore'
import useSidebar from '../../hooks/useSidebar'
import toast from 'react-hot-toast'

const AdminDashboard = () => {
  const { user } = useStore()
  const { isCollapsed, gridCols } = useSidebar()
  const [selectedCustomer, setSelectedCustomer] = useState(null)
  const [selectedService, setSelectedService] = useState(null)
  const [selectedServiceItem, setSelectedServiceItem] = useState(null)

  // بيانات القوائم المنسدلة
  const [customers, setCustomers] = useState([])
  const [allServices, setAllServices] = useState([])
  const [customerServices, setCustomerServices] = useState([])
  const [serviceItems, setServiceItems] = useState([])

  // التحقق من الصلاحيات الإدارية
  const isAdmin = user?.account_type === 'admin' || user?.email === '<EMAIL>'

  // تحميل العملاء
  const loadCustomers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('name')

      if (error) throw error
      setCustomers(data || [])
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error)
      toast.error('فشل في تحميل العملاء')
    }
  }

  // تحميل جميع الخدمات
  const loadAllServices = async () => {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('is_active', true)
        .order('title')

      if (error) throw error
      setAllServices(data || [])
    } catch (error) {
      console.error('خطأ في تحميل الخدمات:', error)
      toast.error('فشل في تحميل الخدمات')
    }
  }

  // تحميل خدمات العميل المحدد
  const loadCustomerServices = async (customerId) => {
    if (!customerId) {
      setCustomerServices([])
      return
    }

    try {
      const { data, error } = await supabase
        .from('user_services')
        .select(`
          *,
          services (*)
        `)
        .eq('user_id', customerId)
        .eq('is_active', true)

      if (error) throw error

      // استخراج الخدمات من النتيجة
      const services = (data || []).map(us => us.services).filter(Boolean)
      setCustomerServices(services)
    } catch (error) {
      console.error('خطأ في تحميل خدمات العميل:', error)
      toast.error('فشل في تحميل خدمات العميل')
    }
  }

  // تحميل عناصر الخدمة
  const loadServiceItems = async (serviceId) => {
    if (!serviceId) {
      setServiceItems([])
      return
    }

    try {
      const { data, error } = await supabase
        .from('service_items')
        .select('*')
        .eq('service_id', serviceId)
        .eq('is_active', true)
        .order('title')

      if (error) throw error
      setServiceItems(data || [])
    } catch (error) {
      console.error('خطأ في تحميل عناصر الخدمة:', error)
      toast.error('فشل في تحميل عناصر الخدمة')
    }
  }



  useEffect(() => {
    if (isAdmin) {
      loadCustomers()
      loadAllServices()
    }
  }, [isAdmin])

  useEffect(() => {
    if (selectedCustomer) {
      loadCustomerServices(selectedCustomer.id)
    } else {
      setCustomerServices([])
    }
  }, [selectedCustomer])



  useEffect(() => {
    if (selectedService) {
      loadServiceItems(selectedService.id)
    }
  }, [selectedService])





  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Shield className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">غير مصرح</h2>
          <p className="text-gray-600">ليس لديك صلاحية للوصول لهذه الصفحة</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <PageHeader
        title="إدارة الإجراءات"
        subtitle="إدارة الإجراءات والمتطلبات والجدول الزمني للعناصر"
      />

      {/* المحتوى الرئيسي */}
      <div className="px-6 py-6 space-y-6">

        {/* معلومات الصفحة */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">إدارة الإجراءات</h3>
            <p className="text-gray-600 mb-4">
              اختر عميل وخدمة وعنصر من القوائم أدناه لإدارة الإجراءات والمتطلبات والجدول الزمني
            </p>
            <div className="flex items-center justify-center space-x-4 space-x-reverse text-sm text-gray-500">
              <div className="flex items-center space-x-1 space-x-reverse">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>اختر العميل</span>
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>اختر الخدمة</span>
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>اختر العنصر</span>
              </div>
            </div>
          </div>
        </div>

        {/* القوائم المنسدلة للاختيار */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* اختيار العميل */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اختر العميل
              </label>
              <div className="relative">
                <select
                  value={selectedCustomer?.id || ''}
                  onChange={(e) => {
                    const customer = customers.find(c => c.id === e.target.value)
                    setSelectedCustomer(customer || null)
                    setSelectedService(null)
                    setSelectedServiceItem(null)
                  }}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
                >
                  <option value="">-- اختر عميل --</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name} ({customer.email})
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
              </div>
            </div>

            {/* اختيار الخدمة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اختر الخدمة
              </label>
              <div className="relative">
                <select
                  value={selectedService?.id || ''}
                  onChange={(e) => {
                    const service = customerServices.find(s => s.id === e.target.value)
                    setSelectedService(service || null)
                    setSelectedServiceItem(null)
                  }}
                  disabled={!selectedCustomer}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white disabled:bg-gray-50 disabled:text-gray-400"
                >
                  <option value="">
                    {selectedCustomer
                      ? "-- اختر خدمة --"
                      : "-- اختر عميل أولاً --"
                    }
                  </option>
                  {customerServices.map(service => (
                    <option key={service.id} value={service.id}>
                      {service.title}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
              </div>
            </div>

            {/* اختيار عنصر الخدمة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اختر عنصر الخدمة
              </label>
              <div className="relative">
                <select
                  value={selectedServiceItem?.id || ''}
                  onChange={(e) => {
                    const item = serviceItems.find(i => i.id === e.target.value)
                    setSelectedServiceItem(item || null)
                  }}
                  disabled={!selectedService}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white disabled:bg-gray-50 disabled:text-gray-400"
                >
                  <option value="">-- اختر عنصر --</option>
                  {serviceItems.map(item => (
                    <option key={item.id} value={item.id}>
                      {item.title}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
              </div>
            </div>
          </div>
        </div>

        {/* قسم إدارة البيانات - يمتد على كامل المساحة */}
        <div className="min-h-[calc(100vh-20rem)]">
          <ServiceDataManagement
            customer={selectedCustomer}
            serviceItem={selectedServiceItem}
          />
        </div>
      </div>
    </div>
  )

}

export default AdminDashboard
