import React, { useState, useEffect, createContext, useContext, useCallback, useMemo } from 'react';
import { 
  ChevronRight, Calendar, FileText, MessageCircle, Upload, Download, 
  CheckCircle, Clock, AlertCircle, User, LogOut, Send, Plus, Bell,
  Shield, Activity, Award, TrendingUp, Zap, Info, X, ChevronLeft,
  Settings, HelpCircle, Filter, Search, BarChart, PieChart, 
  FolderOpen, Link2, AlertTriangle, CheckSquare, MessageSquare,
  Mail, Phone, MapPin, Globe, Users, Briefcase, Star, Eye, EyeOff,
  Menu, Home, Archive, CreditCard, HeadphonesIcon, LogIn
} from 'lucide-react';

// ==================== نظام عملاء نماء الاحترافي ====================
// مطور بواسطة خبير في تطوير الأنظمة المؤسسية

// ==================== إدارة السياق المركزية ====================
const AppContext = createContext();

const AppProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [theme, setTheme] = useState('light');
  const [language, setLanguage] = useState('ar');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const addNotification = useCallback((notification) => {
    const newNotif = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      read: false,
      ...notification
    };
    setNotifications(prev => [newNotif, ...prev]);
  }, []);

  const markNotificationAsRead = useCallback((id) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  }, []);

  const value = useMemo(() => ({
    currentUser,
    setCurrentUser,
    notifications,
    addNotification,
    markNotificationAsRead,
    theme,
    setTheme,
    language,
    setLanguage,
    sidebarOpen,
    setSidebarOpen
  }), [currentUser, notifications, theme, language, sidebarOpen, addNotification, markNotificationAsRead]);

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};

// ==================== قاعدة البيانات المحسنة ====================
const mockDatabase = {
  users: {
    '<EMAIL>': {
      id: 'USR-001',
      password: '123456',
      name: 'جمعية الأمل الخيرية',
      logo: '🏢',
      contact: 'أحمد محمد السالم',
      phone: '**********',
      email: '<EMAIL>',
      establishedDate: '2015-03-15',
      membershipLevel: 'premium',
      membershipExpiry: '2025-03-15',
      address: 'الرياض - حي العليا - شارع الملك فهد',
      registrationNumber: 'REG-2015-001',
      vatNumber: '************',
      bankAccount: '************************',
      website: 'www.alamal.org.sa',
      socialMedia: {
        twitter: '@alamal_org',
        linkedin: 'alamal-charity'
      },
      stats: {
        totalProjects: 12,
        completedProjects: 8,
        activeProjects: 3,
        pendingProjects: 1,
        satisfaction: 4.8,
        totalSpent: 450000,
        totalSaved: 85000,
        avgProjectDuration: 45
      },
      preferences: {
        emailNotifications: true,
        smsNotifications: true,
        autoRenew: true,
        preferredLanguage: 'ar'
      }
    },
    '<EMAIL>': {
      id: 'USR-002',
      password: '123456',
      name: 'جمعية روافد التنموية',
      logo: '🌟',
      contact: 'فاطمة أحمد العلي',
      phone: '**********',
      email: '<EMAIL>',
      establishedDate: '2018-07-20',
      membershipLevel: 'gold',
      membershipExpiry: '2024-07-20',
      address: 'جدة - حي الروضة - طريق الملك عبدالله',
      registrationNumber: 'REG-2018-045',
      vatNumber: '************',
      bankAccount: '************************',
      website: 'www.rawafed.org.sa',
      socialMedia: {
        twitter: '@rawafed_dev',
        instagram: 'rawafed.development'
      },
      stats: {
        totalProjects: 6,
        completedProjects: 4,
        activeProjects: 2,
        pendingProjects: 0,
        satisfaction: 4.9,
        totalSpent: 280000,
        totalSaved: 45000,
        avgProjectDuration: 38
      },
      preferences: {
        emailNotifications: true,
        smsNotifications: false,
        autoRenew: false,
        preferredLanguage: 'ar'
      }
    }
  },

  requests: [
    {
      id: 'REQ-001',
      clientId: 'USR-001',
      clientEmail: '<EMAIL>',
      service: 'خدمات التأسيس',
      serviceIcon: '🏛️',
      category: 'foundation',
      priority: 'high',
      submissionDate: '2024-05-15',
      expectedCompletion: '2024-07-15',
      actualCompletion: null,
      currentStep: 'إعداد النظام الأساسي',
      currentStepStartDate: '2024-06-01',
      progress: 65,
      status: 'action-required',
      health: 'at-risk',
      assignedTeam: [
        { id: 'EMP-001', name: 'محمد السعيد', role: 'مدير المشروع', avatar: '👨‍💼' },
        { id: 'EMP-002', name: 'نورا الخالد', role: 'مستشار قانوني', avatar: '👩‍⚖️' }
      ],
      budget: {
        total: 25000,
        paid: 15000,
        remaining: 10000,
        currency: 'SAR',
        paymentSchedule: [
          { amount: 10000, date: '2024-05-20', status: 'paid' },
          { amount: 5000, date: '2024-06-01', status: 'paid' },
          { amount: 5000, date: '2024-06-20', status: 'pending' },
          { amount: 5000, date: '2024-07-15', status: 'scheduled' }
        ]
      },
      contract: {
        number: 'CNT-2024-001',
        signedDate: '2024-05-16',
        value: 25000,
        duration: '3 أشهر'
      },
      risks: [
        {
          id: 'RSK-001',
          description: 'تأخر في الحصول على موافقة الجهات الرسمية',
          impact: 'high',
          probability: 'medium',
          mitigation: 'التواصل المبكر مع الجهات المعنية'
        }
      ],
      nextAction: {
        id: 'ACT-001',
        title: 'مراجعة وتوقيع النظام الأساسي',
        description: 'تم إرسال النظام الأساسي المحدث لمراجعتكم. يرجى مراجعته بعناية والتأكد من جميع البنود ثم توقيعه إلكترونياً أو يدوياً وإرساله خلال 3 أيام عمل.',
        detailedInstructions: `
          1. قم بتحميل ملف النظام الأساسي المرفق
          2. اقرأ جميع البنود بعناية وتأكد من صحة المعلومات
          3. في حال وجود أي ملاحظات، يرجى التواصل معنا فوراً
          4. قم بطباعة النموذج وتوقيعه من قبل المفوضين الثلاثة
          5. قم بمسح النسخة الموقعة ورفعها عبر النظام
        `,
        dueDate: '2024-06-20',
        urgency: 'urgent',
        estimatedTime: '30 دقيقة',
        requiredSignatures: 3,
        signatoriesStatus: [
          { name: 'أحمد محمد السالم', role: 'رئيس مجلس الإدارة', signed: false },
          { name: 'خالد عبدالله المنيف', role: 'نائب الرئيس', signed: false },
          { name: 'سارة أحمد الغامدي', role: 'أمين الصندوق', signed: false }
        ],
        documents: [
          { 
            id: 'DOC-ACT-001',
            name: 'النظام_الأساسي_النهائي.pdf',
            size: '2.3 MB',
            type: 'pdf',
            uploadDate: '2024-06-12',
            version: 'v3.0',
            checksum: 'abc123def456'
          },
          { 
            id: 'DOC-ACT-002',
            name: 'نموذج_التوقيع.pdf',
            size: '450 KB',
            type: 'pdf',
            uploadDate: '2024-06-12',
            version: 'v1.0'
          },
          { 
            id: 'DOC-ACT-003',
            name: 'دليل_المراجعة.docx',
            size: '1.1 MB',
            type: 'doc',
            uploadDate: '2024-06-12',
            version: 'v2.1'
          }
        ],
        dependencies: ['تأكيد صحة البيانات', 'موافقة مجلس الإدارة'],
        completionCriteria: [
          'استلام النظام الأساسي موقع من جميع المفوضين',
          'التحقق من صحة التواقيع',
          'أرشفة النسخة الموقعة'
        ]
      },
      timeline: [
        { 
          step: 'تقديم الطلب',
          stepNumber: 1,
          date: '2024-05-15',
          status: 'completed',
          description: 'تم استلام طلب التأسيس وجميع المرفقات المطلوبة',
          completedBy: 'النظام الآلي',
          duration: '1 يوم',
          deliverables: ['نموذج الطلب', 'المستندات الأساسية']
        },
        { 
          step: 'مراجعة المستندات',
          stepNumber: 2,
          date: '2024-05-18',
          status: 'completed',
          description: 'تمت مراجعة جميع المستندات والتحقق من اكتمالها وصحتها',
          completedBy: 'فريق المراجعة القانونية',
          duration: '3 أيام',
          deliverables: ['تقرير المراجعة', 'قائمة الملاحظات']
        },
        { 
          step: 'إعداد النظام الأساسي',
          stepNumber: 3,
          date: '2024-06-01',
          status: 'current',
          description: 'جاري العمل على صياغة النظام الأساسي وفقاً للمعايير والأنظمة',
          assignedTo: 'المستشار القانوني',
          expectedDuration: '14 يوم',
          progress: 85,
          subtasks: [
            { name: 'صياغة المسودة الأولى', status: 'completed' },
            { name: 'المراجعة القانونية', status: 'completed' },
            { name: 'إدخال التعديلات', status: 'completed' },
            { name: 'المراجعة النهائية', status: 'in-progress' },
            { name: 'اعتماد النسخة النهائية', status: 'pending' }
          ]
        },
        { 
          step: 'التسجيل الرسمي',
          stepNumber: 4,
          date: null,
          status: 'upcoming',
          description: 'تقديم الطلب للجهات الرسمية والحصول على الموافقات اللازمة',
          expectedDuration: '21 يوم',
          prerequisites: ['النظام الأساسي الموقع', 'المستندات المكتملة']
        },
        { 
          step: 'التسليم النهائي',
          stepNumber: 5,
          date: null,
          status: 'upcoming',
          description: 'تسليم جميع المستندات والشهادات الرسمية وإغلاق المشروع',
          expectedDuration: '3 أيام',
          deliverables: ['شهادة التسجيل', 'النظام الأساسي المعتمد', 'دليل الإجراءات']
        }
      ],
      communications: [
        {
          id: 'MSG-001',
          date: '2024-06-12T14:30:00',
          from: { type: 'support', name: 'محمد السعيد', avatar: '👨‍💼' },
          to: { type: 'client', name: 'أحمد محمد السالم' },
          subject: 'النظام الأساسي جاهز للمراجعة',
          message: 'السلام عليكم ورحمة الله وبركاته،\n\nيسعدني إبلاغكم بأننا أنهينا إعداد النسخة النهائية من النظام الأساسي لجمعيتكم. تم مراعاة جميع الملاحظات التي ناقشناها في الاجتماع السابق.\n\nيرجى مراجعة النظام المرفق وإبداء أي ملاحظات خلال 3 أيام عمل.\n\nتحياتي،\nمحمد السعيد',
          attachments: ['النظام_الأساسي_النهائي.pdf'],
          read: true,
          important: true
        },
        {
          id: 'MSG-002',
          date: '2024-06-11T09:15:00',
          from: { type: 'client', name: 'أحمد محمد السالم' },
          to: { type: 'support', name: 'محمد السعيد' },
          subject: 'استفسار حول البنود',
          message: 'صباح الخير،\n\nشكراً على المسودة الأولية. لدينا استفسار حول البند الخاص بالعضوية، هل يمكن إضافة تفاصيل أكثر حول شروط العضوية التطوعية؟\n\nكما نود إضافة بند خاص بآلية التصويت الإلكتروني.\n\nشاكرين تعاونكم',
          read: true
        },
        {
          id: 'MSG-003',
          date: '2024-06-10T16:45:00',
          from: { type: 'support', name: 'نورا الخالد', avatar: '👩‍⚖️' },
          to: { type: 'client', name: 'أحمد محمد السالم' },
          subject: 'تحديث على سير العمل',
          message: 'مساء الخير،\n\nأود إطلاعكم على آخر التطورات في مشروع التأسيس:\n\n1. تم الانتهاء من 85% من صياغة النظام الأساسي\n2. تمت مراجعة جميع البنود القانونية\n3. نتوقع إرسال النسخة النهائية يوم الاثنين القادم\n\nفي حال وجود أي استفسارات، نحن في الخدمة.\n\nأطيب التحيات',
          read: true
        }
      ],
      documents: [
        {
          id: 'DOC-001',
          name: 'طلب_التأسيس_الأصلي.pdf',
          type: 'application',
          uploadDate: '2024-05-15',
          uploadedBy: 'أحمد محمد السالم',
          status: 'approved',
          size: '1.5 MB',
          category: 'foundation'
        },
        {
          id: 'DOC-002',
          name: 'صورة_السجل_التجاري.jpg',
          type: 'identity',
          uploadDate: '2024-05-15',
          uploadedBy: 'أحمد محمد السالم',
          status: 'approved',
          size: '850 KB',
          category: 'official'
        },
        {
          id: 'DOC-003',
          name: 'محضر_اجتماع_المؤسسين.pdf',
          type: 'meeting',
          uploadDate: '2024-05-16',
          uploadedBy: 'أحمد محمد السالم',
          status: 'approved',
          size: '2.1 MB',
          category: 'foundation'
        }
      ],
      recentUpdates: [
        { 
          id: 'UPD-001',
          date: '2024-06-12T14:30:00',
          message: 'تم إرسال النظام الأساسي للمراجعة النهائية',
          type: 'info',
          author: 'محمد السعيد',
          category: 'document',
          attachments: 1,
          details: 'النسخة 3.0 من النظام الأساسي جاهزة للمراجعة والتوقيع'
        },
        { 
          id: 'UPD-002',
          date: '2024-06-08T11:00:00',
          message: 'انتهاء صياغة النظام الأساسي بنجاح',
          type: 'success',
          author: 'نورا الخالد',
          category: 'milestone',
          details: 'تم الانتهاء من جميع البنود القانونية المطلوبة'
        },
        { 
          id: 'UPD-003',
          date: '2024-06-05T09:30:00',
          message: 'بدء العمل على النظام الأساسي',
          type: 'info',
          author: 'محمد السعيد',
          category: 'start',
          details: 'بدأ الفريق القانوني في صياغة النظام وفقاً للمعايير'
        }
      ],
      satisfaction: {
        overall: 4.5,
        communication: 4.8,
        quality: 4.6,
        timeliness: 4.2,
        value: 4.5
      }
    },
    // يمكن إضافة المزيد من الطلبات هنا...
  ],

  services: [
    {
      id: 'SRV-001',
      name: 'خدمات التأسيس',
      icon: '🏛️',
      category: 'foundation',
      description: 'خدمات شاملة لتأسيس الجمعيات والمؤسسات غير الربحية من الألف إلى الياء',
      detailedDescription: 'نقدم خدمات متكاملة لتأسيس الجمعيات الخيرية والمؤسسات غير الربحية، بدءاً من الاستشارة الأولية وحتى الحصول على جميع التراخيص الرسمية.',
      features: [
        'دراسة الجدوى والاستشارة الأولية',
        'إعداد النظام الأساسي واللوائح الداخلية',
        'التسجيل في وزارة الموارد البشرية والتنمية الاجتماعية',
        'استخراج التراخيص والتصاريح اللازمة',
        'إعداد الهيكل التنظيمي والوظيفي',
        'تدريب الكادر الإداري'
      ],
      duration: '2-3 أشهر',
      startingPrice: 20000,
      includes: [
        'استشارات غير محدودة',
        'مراجعات قانونية',
        'متابعة مع الجهات الرسمية',
        'دعم فني لمدة 6 أشهر'
      ]
    },
    {
      id: 'SRV-002',
      name: 'الخدمات المالية والإدارية',
      icon: '💰',
      category: 'financial',
      description: 'حلول متكاملة للإدارة المالية والمحاسبية وفق أفضل الممارسات',
      detailedDescription: 'نساعدك في بناء نظام مالي وإداري قوي يضمن الشفافية والكفاءة في إدارة موارد مؤسستك.',
      features: [
        'تركيب وتخصيص الأنظمة المحاسبية',
        'إعداد اللوائح المالية والإدارية',
        'تصميم دورة مستندية متكاملة',
        'التدريب على الأنظمة المالية',
        'إعداد الموازنات والتقارير المالية',
        'المراجعة الدورية والتدقيق الداخلي'
      ],
      duration: '1-2 شهر',
      startingPrice: 30000,
      includes: [
        'نظام محاسبي متكامل',
        'تدريب 20 ساعة',
        'دعم فني لمدة سنة',
        'تحديثات مجانية'
      ]
    },
    {
      id: 'SRV-003',
      name: 'خدمة حوكمة الجمعيات',
      icon: '⚖️',
      category: 'governance',
      description: 'تطبيق أفضل معايير الحوكمة المؤسسية لضمان الاستدامة والشفافية',
      detailedDescription: 'نعمل معك على بناء نظام حوكمة قوي يضمن الشفافية والمساءلة ويعزز ثقة المانحين والمستفيدين.',
      features: [
        'تقييم شامل للوضع الحالي للحوكمة',
        'وضع لوائح ومواثيق الحوكمة',
        'تطوير آليات الرقابة والمساءلة',
        'تدريب مجلس الإدارة والإدارة التنفيذية',
        'تصميم نظام إدارة المخاطر',
        'المتابعة الدورية والتقييم المستمر'
      ],
      duration: '3-4 أشهر',
      startingPrice: 40000,
      includes: [
        'تقييم شامل',
        'خطة حوكمة متكاملة',
        'تدريب 30 ساعة',
        'متابعة ربع سنوية'
      ]
    }
  ],

  notifications: [
    {
      id: 'NOTIF-001',
      userId: 'USR-001',
      type: 'action-required',
      title: 'مطلوب: توقيع النظام الأساسي',
      message: 'يرجى مراجعة وتوقيع النظام الأساسي قبل 2024-06-20',
      date: '2024-06-12T14:30:00',
      read: false,
      priority: 'high',
      relatedRequest: 'REQ-001'
    }
  ]
};

// ==================== مكونات واجهة المستخدم الأساسية ====================
const LoadingSpinner = ({ size = 'default', fullScreen = false }) => {
  const sizeClasses = {
    small: 'h-4 w-4',
    default: 'h-8 w-8',
    large: 'h-12 w-12'
  };

  const spinner = (
    <div className="flex items-center justify-center">
      <div className={`animate-spin rounded-full border-b-2 border-blue-600 ${sizeClasses[size]}`}></div>
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
        {spinner}
      </div>
    );
  }

  return spinner;
};

const EmptyState = ({ icon: Icon, title, description, action, className = '' }) => (
  <div className={`text-center py-12 ${className}`}>
    <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
      <Icon className="w-8 h-8 text-gray-400" />
    </div>
    <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-500 mb-4 max-w-md mx-auto">{description}</p>
    {action && action}
  </div>
);

const ProgressBar = ({ progress, showLabel = true, height = 'h-2', animated = true, color = 'blue' }) => {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-green-600',
    orange: 'from-orange-500 to-orange-600',
    red: 'from-red-500 to-red-600'
  };

  return (
    <div className="w-full">
      {showLabel && (
        <div className="flex justify-between mb-1">
          <span className="text-sm font-medium text-gray-700">التقدم</span>
          <span className={`text-sm font-medium text-${color}-600`}>{progress}%</span>
        </div>
      )}
      <div className={`w-full bg-gray-200 rounded-full ${height} overflow-hidden`}>
        <div 
          className={`bg-gradient-to-r ${colorClasses[color]} ${height} rounded-full transition-all duration-500 relative ${animated ? 'animate-pulse' : ''}`}
          style={{ width: `${progress}%` }}
        >
          {animated && (
            <div className="absolute inset-0 bg-white opacity-20 animate-slide"></div>
          )}
        </div>
      </div>
    </div>
  );
};

const StatusBadge = ({ status, size = 'default' }) => {
  const statusConfig = {
    'action-required': {
      color: 'bg-orange-100 text-orange-800 border-orange-200',
      icon: AlertCircle,
      label: 'يتطلب إجراء'
    },
    'on-track': {
      color: 'bg-green-100 text-green-800 border-green-200',
      icon: CheckCircle,
      label: 'سير طبيعي'
    },
    'delayed': {
      color: 'bg-red-100 text-red-800 border-red-200',
      icon: Clock,
      label: 'متأخر'
    },
    'completed': {
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      icon: CheckSquare,
      label: 'مكتمل'
    }
  };

  const sizeClasses = {
    small: 'px-2 py-0.5 text-xs',
    default: 'px-3 py-1 text-sm',
    large: 'px-4 py-2 text-base'
  };

  const config = statusConfig[status] || statusConfig['on-track'];
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center rounded-full font-medium border ${config.color} ${sizeClasses[size]}`}>
      <Icon className={`${size === 'small' ? 'w-3 h-3' : 'w-4 h-4'} ml-1`} />
      {config.label}
    </span>
  );
};

const PriorityBadge = ({ priority }) => {
  const priorityConfig = {
    high: { color: 'bg-red-100 text-red-800', icon: '🔴', label: 'عالية' },
    medium: { color: 'bg-yellow-100 text-yellow-800', icon: '🟡', label: 'متوسطة' },
    low: { color: 'bg-gray-100 text-gray-800', icon: '⚪', label: 'منخفضة' }
  };

  const config = priorityConfig[priority] || priorityConfig.medium;

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      <span className="ml-1">{config.icon}</span>
      {config.label}
    </span>
  );
};

// ==================== مكون تسجيل الدخول المحسّن ====================
const LoginPage = () => {
  const { setCurrentUser, addNotification } = useApp();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(true);

  const handleLogin = async () => {
    if (!email || !password) {
      setError('يرجى إدخال البريد الإلكتروني وكلمة المرور');
      return;
    }

    setLoading(true);
    setError('');

    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 1500));

    const user = mockDatabase.users[email];
    if (user && user.password === password) {
      setCurrentUser(user);
      if (rememberMe) {
        localStorage.setItem('namaUser', JSON.stringify({ email, rememberMe: true }));
      }
      addNotification({
        type: 'success',
        title: 'تم تسجيل الدخول بنجاح',
        message: `مرحباً بك ${user.name}`
      });
    } else {
      setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-teal-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        {/* Logo and Header */}
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-900 to-teal-500 rounded-3xl shadow-2xl mb-6 transform hover:scale-105 transition-transform">
            <span className="text-white text-4xl font-bold">ن</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">نماء الاحترافية</h1>
          <p className="text-lg text-gray-600">لحلول الأعمال المتكاملة</p>
        </div>

        {/* Login Form Card */}
        <div className="bg-white rounded-2xl shadow-xl p-8 backdrop-filter backdrop-blur-lg bg-opacity-95">
          <div className="space-y-6">
            {/* Email Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البريد الإلكتروني
              </label>
              <div className="relative">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
                  className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-left"
                  placeholder="<EMAIL>"
                  dir="ltr"
                />
                <Mail className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>
            </div>

            {/* Password Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                كلمة المرور
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
                  className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-3.5 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            {/* Remember Me */}
            <div className="flex items-center justify-between">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="ml-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">تذكرني</span>
              </label>
              <button className="text-sm text-blue-600 hover:text-blue-800">
                نسيت كلمة المرور؟
              </button>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center animate-shake">
                <AlertCircle className="w-5 h-5 ml-2" />
                {error}
              </div>
            )}

            {/* Submit Button */}
            <button
              onClick={handleLogin}
              disabled={loading}
              className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 px-4 rounded-xl hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed font-medium flex items-center justify-center"
            >
              {loading ? (
                <>
                  <LoadingSpinner size="small" />
                  <span className="mr-2">جاري تسجيل الدخول...</span>
                </>
              ) : (
                <>
                  <LogIn className="w-5 h-5 ml-2" />
                  تسجيل الدخول
                </>
              )}
            </button>
          </div>

          {/* Demo Credentials */}
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-teal-50 rounded-xl">
            <p className="text-sm font-medium text-blue-900 mb-3 text-center">حسابات تجريبية للاختبار:</p>
            <div className="space-y-2">
              <button 
                onClick={() => {
                  setEmail('<EMAIL>');
                  setPassword('123456');
                }}
                className="w-full flex justify-between items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center">
                  <span className="text-2xl ml-2">🏢</span>
                  <span className="text-sm text-gray-700">جمعية الأمل الخيرية</span>
                </div>
                <span className="text-xs font-mono text-gray-500">123456</span>
              </button>
              
              <button 
                onClick={() => {
                  setEmail('<EMAIL>');
                  setPassword('123456');
                }}
                className="w-full flex justify-between items-center p-3 bg-white rounded-lg hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center">
                  <span className="text-2xl ml-2">🌟</span>
                  <span className="text-sm text-gray-700">جمعية روافد التنموية</span>
                </div>
                <span className="text-xs font-mono text-gray-500">123456</span>
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center space-y-2">
          <p className="text-sm text-gray-500">
            جميع الحقوق محفوظة © 2024 نماء الاحترافية
          </p>
          <div className="flex items-center justify-center space-x-4 space-x-reverse">
            <a href="#" className="text-xs text-gray-400 hover:text-gray-600">سياسة الخصوصية</a>
            <span className="text-gray-300">•</span>
            <a href="#" className="text-xs text-gray-400 hover:text-gray-600">الشروط والأحكام</a>
            <span className="text-gray-300">•</span>
            <a href="#" className="text-xs text-gray-400 hover:text-gray-600">اتصل بنا</a>
          </div>
        </div>
      </div>
    </div>
  );
};

// ==================== مكون الإحصائيات المحسّن ====================
const StatsCard = ({ icon: Icon, label, value, trend, color = 'blue', onClick }) => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600 border-blue-200',
    green: 'bg-green-100 text-green-600 border-green-200',
    orange: 'bg-orange-100 text-orange-600 border-orange-200',
    purple: 'bg-purple-100 text-purple-600 border-purple-200',
    red: 'bg-red-100 text-red-600 border-red-200'
  };

  return (
    <div 
      className="bg-white rounded-xl shadow-sm border p-6 hover:shadow-lg transition-all cursor-pointer transform hover:-translate-y-1"
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-4">
        <div className={`inline-flex items-center justify-center w-14 h-14 rounded-xl ${colorClasses[color]} border`}>
          <Icon className="w-7 h-7" />
        </div>
        {trend !== undefined && (
          <div className={`flex items-center text-sm font-medium ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {trend > 0 ? <TrendingUp className="w-4 h-4 ml-1" /> : '📉'}
            <span>{Math.abs(trend)}%</span>
          </div>
        )}
      </div>
      <p className="text-sm text-gray-600 mb-1">{label}</p>
      <p className="text-2xl font-bold text-gray-900">{value}</p>
    </div>
  );
};

// ==================== مكونات التفاصيل ====================
const RequestDetails = ({ request, onBack }) => {
  const { currentUser, addNotification } = useApp();
  const [activeTab, setActiveTab] = useState('current');
  const [uploadedLinks, setUploadedLinks] = useState([]);
  const [linkInput, setLinkInput] = useState('');
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [messageContent, setMessageContent] = useState('');

  const handleLinkSubmit = () => {
    if (linkInput.trim()) {
      const newLink = {
        id: Date.now(),
        url: linkInput,
        uploadDate: new Date().toISOString(),
        status: 'pending'
      };
      setUploadedLinks([...uploadedLinks, newLink]);
      setLinkInput('');
      addNotification({
        type: 'success',
        title: 'تم رفع الرابط بنجاح',
        message: 'سيتم مراجعة الملف من قبل الفريق المختص'
      });
    }
  };

  const handleSendMessage = () => {
    if (messageContent.trim()) {
      addNotification({
        type: 'success',
        title: 'تم إرسال الرسالة',
        message: 'سيتم الرد عليك في أقرب وقت ممكن'
      });
      setMessageContent('');
      setShowMessageModal(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <button 
                onClick={onBack}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{request.service}</h1>
                <p className="text-sm text-gray-600">#{request.id} • {request.currentStep}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <StatusBadge status={request.status} size="large" />
              <PriorityBadge priority={request.priority} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-12 gap-6">
          {/* الشريط الجانبي - معلومات المشروع */}
          <div className="col-span-4">
            <div className="bg-white rounded-xl shadow-sm sticky top-24">
              {/* التقدم العام */}
              <div className="p-6 border-b">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">التقدم العام</h3>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="relative inline-flex items-center justify-center w-32 h-32">
                      <svg className="w-32 h-32 transform -rotate-90">
                        <circle
                          cx="64"
                          cy="64"
                          r="56"
                          stroke="currentColor"
                          strokeWidth="12"
                          fill="none"
                          className="text-gray-200"
                        />
                        <circle
                          cx="64"
                          cy="64"
                          r="56"
                          stroke="currentColor"
                          strokeWidth="12"
                          fill="none"
                          strokeDasharray={351.86}
                          strokeDashoffset={351.86 * (1 - request.progress / 100)}
                          className="text-blue-600 transition-all duration-500"
                        />
                      </svg>
                      <span className="absolute text-3xl font-bold text-gray-900">{request.progress}%</span>
                    </div>
                  </div>
                  <ProgressBar progress={request.progress} showLabel={false} height="h-3" />
                </div>
              </div>

              {/* معلومات المشروع */}
              <div className="p-6 border-b">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات المشروع</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">تاريخ البداية</span>
                    <span className="text-sm font-medium text-gray-900">{request.submissionDate}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">التاريخ المتوقع</span>
                    <span className="text-sm font-medium text-gray-900">{request.expectedCompletion}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">رقم العقد</span>
                    <span className="text-sm font-medium text-gray-900">{request.contract.number}</span>
                  </div>
                </div>
              </div>

              {/* الفريق المكلف */}
              <div className="p-6 border-b">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">الفريق المكلف</h3>
                <div className="space-y-3">
                  {request.assignedTeam.map((member) => (
                    <div key={member.id} className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-lg">
                        {member.avatar}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{member.name}</p>
                        <p className="text-xs text-gray-500">{member.role}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* المعلومات المالية */}
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات المالية</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">إجمالي القيمة</span>
                    <span className="text-sm font-medium text-gray-900">{request.budget.total.toLocaleString()} ريال</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">المدفوع</span>
                    <span className="text-sm font-medium text-green-600">{request.budget.paid.toLocaleString()} ريال</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">المتبقي</span>
                    <span className="text-sm font-medium text-orange-600">{request.budget.remaining.toLocaleString()} ريال</span>
                  </div>
                </div>
                <div className="mt-4">
                  <ProgressBar 
                    progress={Math.round((request.budget.paid / request.budget.total) * 100)} 
                    showLabel={false} 
                    color="green"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="col-span-8">
            {/* التبويبات */}
            <div className="bg-white rounded-xl shadow-sm mb-6">
              <nav className="flex border-b">
                {[
                  { id: 'current', label: 'الإجراء الحالي', icon: Zap },
                  { id: 'timeline', label: 'الجدول الزمني', icon: Calendar },
                  { id: 'communications', label: 'المراسلات', icon: MessageCircle },
                  { id: 'documents', label: 'المستندات', icon: FolderOpen }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 flex items-center justify-center px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 bg-blue-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <tab.icon className="w-5 h-5 ml-2" />
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* محتوى التبويبات */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              {/* تبويب الإجراء الحالي */}
              {activeTab === 'current' && request.nextAction && (
                <div className="space-y-6">
                  {/* رأس الإجراء */}
                  <div className={`p-6 rounded-xl border-2 ${
                    request.nextAction.urgency === 'urgent' 
                      ? 'bg-red-50 border-red-200' 
                      : 'bg-blue-50 border-blue-200'
                  }`}>
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className={`w-16 h-16 rounded-xl flex items-center justify-center ${
                        request.nextAction.urgency === 'urgent' 
                          ? 'bg-red-100' 
                          : 'bg-blue-100'
                      }`}>
                        <AlertCircle className={`w-8 h-8 ${
                          request.nextAction.urgency === 'urgent' 
                            ? 'text-red-600' 
                            : 'text-blue-600'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <h3 className={`text-2xl font-bold mb-2 ${
                          request.nextAction.urgency === 'urgent' 
                            ? 'text-red-900' 
                            : 'text-blue-900'
                        }`}>
                          {request.nextAction.title}
                        </h3>
                        <p className={`text-lg ${
                          request.nextAction.urgency === 'urgent' 
                            ? 'text-red-700' 
                            : 'text-blue-700'
                        }`}>
                          {request.nextAction.description}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* معلومات الإجراء */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <Clock className="w-6 h-6 text-gray-600 mx-auto mb-2" />
                      <p className="text-xs text-gray-600 mb-1">الموعد النهائي</p>
                      <p className="font-semibold text-gray-900">{request.nextAction.dueDate}</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <Activity className="w-6 h-6 text-gray-600 mx-auto mb-2" />
                      <p className="text-xs text-gray-600 mb-1">الوقت المقدر</p>
                      <p className="font-semibold text-gray-900">{request.nextAction.estimatedTime}</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <Users className="w-6 h-6 text-gray-600 mx-auto mb-2" />
                      <p className="text-xs text-gray-600 mb-1">التواقيع المطلوبة</p>
                      <p className="font-semibold text-gray-900">{request.nextAction.requiredSignatures}</p>
                    </div>
                  </div>

                  {/* التعليمات التفصيلية */}
                  {request.nextAction.detailedInstructions && (
                    <div className="bg-blue-50 rounded-lg p-6">
                      <h4 className="font-semibold text-blue-900 mb-3">التعليمات التفصيلية:</h4>
                      <div className="text-blue-800 whitespace-pre-line">
                        {request.nextAction.detailedInstructions}
                      </div>
                    </div>
                  )}

                  {/* المستندات المطلوبة */}
                  {request.nextAction.documents.length > 0 && (
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">المستندات المطلوبة للتحميل</h4>
                      <div className="grid gap-3">
                        {request.nextAction.documents.map((doc) => (
                          <div key={doc.id} className="flex items-center justify-between p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                            <div className="flex items-center space-x-3 space-x-reverse">
                              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <FileText className="w-6 h-6 text-blue-600" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{doc.name}</p>
                                <p className="text-sm text-gray-500">{doc.size} • {doc.version}</p>
                              </div>
                            </div>
                            <button className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                              <Download className="w-4 h-4" />
                              <span>تحميل</span>
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* رفع المستندات */}
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 text-center">رفع المستندات المطلوبة</h4>
                    
                    <div className="space-y-4">
                      <div className="flex space-x-3 space-x-reverse">
                        <input
                          type="url"
                          value={linkInput}
                          onChange={(e) => setLinkInput(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleLinkSubmit()}
                          placeholder="الصق رابط Google Drive أو Dropbox هنا..."
                          className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          dir="ltr"
                        />
                        <button
                          onClick={handleLinkSubmit}
                          disabled={!linkInput.trim()}
                          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
                        >
                          <Upload className="w-4 h-4 ml-2" />
                          رفع
                        </button>
                      </div>

                      {/* الروابط المرفوعة */}
                      {uploadedLinks.length > 0 && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-700">الملفات المرفوعة:</p>
                          {uploadedLinks.map((link) => (
                            <div key={link.id} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <CheckCircle className="w-5 h-5 text-green-600" />
                                <span className="text-sm text-gray-700">{link.url}</span>
                              </div>
                              <span className="text-xs text-gray-500">
                                {new Date(link.uploadDate).toLocaleTimeString('ar-SA')}
                              </span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    <div className="mt-4 text-center">
                      <p className="text-sm text-gray-500 mb-2">أو</p>
                      <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <Upload className="w-4 h-4 ml-2" />
                        رفع من الجهاز
                      </button>
                    </div>
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="flex justify-center space-x-4 space-x-reverse">
                    <button 
                      onClick={() => setShowMessageModal(true)}
                      className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center"
                    >
                      <MessageCircle className="w-4 h-4 ml-2" />
                      إرسال رسالة
                    </button>
                    <button className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center">
                      <CheckCircle className="w-4 h-4 ml-2" />
                      تأكيد إتمام الإجراء
                    </button>
                  </div>
                </div>
              )}

              {/* تبويب الجدول الزمني */}
              {activeTab === 'timeline' && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-gray-900 text-center mb-6">الجدول الزمني للمشروع</h3>
                  
                  <div className="relative">
                    {request.timeline.map((step, index) => (
                      <div key={index} className="relative flex items-start mb-8 last:mb-0">
                        {/* الخط الواصل */}
                        {index < request.timeline.length - 1 && (
                          <div className="absolute top-12 right-6 w-0.5 h-full bg-gray-200"></div>
                        )}
                        
                        {/* الدائرة */}
                        <div className={`relative z-10 flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center text-white font-bold ${
                          step.status === 'completed' 
                            ? 'bg-green-500' 
                            : step.status === 'current' 
                            ? 'bg-blue-500 animate-pulse' 
                            : 'bg-gray-300'
                        }`}>
                          {step.status === 'completed' ? <CheckCircle className="w-6 h-6" /> : step.stepNumber}
                        </div>
                        
                        {/* محتوى المرحلة */}
                        <div className="flex-1 mr-4">
                          <div className={`p-6 rounded-lg border-2 ${
                            step.status === 'completed' 
                              ? 'bg-green-50 border-green-200' 
                              : step.status === 'current' 
                              ? 'bg-blue-50 border-blue-200' 
                              : 'bg-gray-50 border-gray-200'
                          }`}>
                            <div className="flex items-center justify-between mb-2">
                              <h4 className={`text-lg font-semibold ${
                                step.status === 'current' ? 'text-blue-900' : 'text-gray-900'
                              }`}>
                                {step.step}
                              </h4>
                              {step.status === 'current' && (
                                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                                  جاري العمل
                                </span>
                              )}
                            </div>
                            
                            <p className="text-gray-700 mb-3">{step.description}</p>
                            
                            <div className="flex flex-wrap gap-4 text-sm">
                              {step.date && (
                                <div className="flex items-center text-gray-600">
                                  <Calendar className="w-4 h-4 ml-1" />
                                  {step.date}
                                </div>
                              )}
                              {step.duration && (
                                <div className="flex items-center text-gray-600">
                                  <Clock className="w-4 h-4 ml-1" />
                                  {step.duration}
                                </div>
                              )}
                              {step.completedBy && (
                                <div className="flex items-center text-gray-600">
                                  <User className="w-4 h-4 ml-1" />
                                  {step.completedBy}
                                </div>
                              )}
                            </div>
                            
                            {/* المهام الفرعية */}
                            {step.subtasks && (
                              <div className="mt-4 space-y-2">
                                <p className="text-sm font-medium text-gray-700">المهام الفرعية:</p>
                                {step.subtasks.map((subtask, subIndex) => (
                                  <div key={subIndex} className="flex items-center space-x-2 space-x-reverse">
                                    <div className={`w-5 h-5 rounded-full border-2 ${
                                      subtask.status === 'completed' 
                                        ? 'bg-green-500 border-green-500' 
                                        : subtask.status === 'in-progress' 
                                        ? 'bg-blue-500 border-blue-500' 
                                        : 'bg-white border-gray-300'
                                    }`}>
                                      {subtask.status === 'completed' && (
                                        <CheckCircle className="w-3 h-3 text-white" />
                                      )}
                                    </div>
                                    <span className={`text-sm ${
                                      subtask.status === 'completed' ? 'text-gray-500 line-through' : 'text-gray-700'
                                    }`}>
                                      {subtask.name}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* تبويب المراسلات */}
              {activeTab === 'communications' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold text-gray-900">المراسلات</h3>
                    <button 
                      onClick={() => setShowMessageModal(true)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                    >
                      <Plus className="w-4 h-4 ml-2" />
                      رسالة جديدة
                    </button>
                  </div>
                  
                  <div className="space-y-4">
                    {request.communications.map((message) => (
                      <div key={message.id} className={`p-6 rounded-lg border ${
                        message.from.type === 'support' 
                          ? 'bg-blue-50 border-blue-200 mr-12' 
                          : 'bg-gray-50 border-gray-200 ml-12'
                      }`}>
                        <div className="flex items-start space-x-3 space-x-reverse">
                          <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center text-lg">
                            {message.from.avatar || '👤'}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <div>
                                <span className="font-medium text-gray-900">{message.from.name}</span>
                                <span className="text-sm text-gray-500 mr-2">
                                  {new Date(message.date).toLocaleString('ar-SA')}
                                </span>
                              </div>
                              {message.important && (
                                <Star className="w-5 h-5 text-yellow-500" />
                              )}
                            </div>
                            {message.subject && (
                              <p className="font-medium text-gray-900 mb-2">{message.subject}</p>
                            )}
                            <p className="text-gray-700 whitespace-pre-line">{message.message}</p>
                            {message.attachments && message.attachments.length > 0 && (
                              <div className="mt-3 flex items-center text-sm text-gray-500">
                                <FileText className="w-4 h-4 ml-1" />
                                {message.attachments.length} مرفقات
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* تبويب المستندات */}
              {activeTab === 'documents' && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-gray-900 text-center mb-6">المستندات</h3>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {request.documents.map((doc) => (
                      <div key={doc.id} className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                        <div className="flex items-start space-x-3 space-x-reverse">
                          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            <FileText className="w-6 h-6 text-gray-600" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{doc.name}</h4>
                            <p className="text-sm text-gray-500 mt-1">
                              {doc.size} • {doc.uploadDate}
                            </p>
                            <div className="flex items-center justify-between mt-2">
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                doc.status === 'approved' 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {doc.status === 'approved' ? 'معتمد' : 'قيد المراجعة'}
                              </span>
                              <button className="text-blue-600 hover:text-blue-800 text-sm">
                                تحميل
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* نافذة إرسال رسالة */}
      {showMessageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">إرسال رسالة جديدة</h3>
              <button 
                onClick={() => setShowMessageModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الرسالة
                </label>
                <textarea
                  value={messageContent}
                  onChange={(e) => setMessageContent(e.target.value)}
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  placeholder="اكتب رسالتك هنا..."
                />
              </div>
              
              <div className="flex justify-end space-x-3 space-x-reverse">
                <button 
                  onClick={() => setShowMessageModal(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  إلغاء
                </button>
                <button 
                  onClick={handleSendMessage}
                  disabled={!messageContent.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  <Send className="w-4 h-4 ml-2" />
                  إرسال
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// ==================== مكون لوحة التحكم الرئيسية ====================
const Dashboard = () => {
  const { currentUser, notifications, markNotificationAsRead, setSidebarOpen, sidebarOpen } = useApp();
  const [selectedView, setSelectedView] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [showNotifications, setShowNotifications] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);

  const userRequests = mockDatabase.requests.filter(req => req.clientEmail === currentUser.email);
  const urgentActions = userRequests.filter(req => req.nextAction?.urgency === 'urgent');
  const unreadNotifications = notifications.filter(n => !n.read && n.userId === currentUser.id);

  // تصفية الطلبات
  const filteredRequests = userRequests.filter(req => {
    const matchesSearch = req.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         req.id.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || req.status === filterStatus;
    const matchesPriority = filterPriority === 'all' || req.priority === filterPriority;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  // حساب الإحصائيات
  const stats = {
    totalRequests: userRequests.length,
    activeRequests: userRequests.filter(req => req.status !== 'completed').length,
    actionsRequired: userRequests.filter(req => req.status === 'action-required').length,
    averageProgress: Math.round(userRequests.reduce((sum, req) => sum + req.progress, 0) / userRequests.length) || 0,
    totalBudget: userRequests.reduce((sum, req) => sum + req.budget.total, 0),
    paidAmount: userRequests.reduce((sum, req) => sum + req.budget.paid, 0),
    satisfaction: currentUser.stats.satisfaction
  };

  if (selectedRequest) {
    return <RequestDetails request={selectedRequest} onBack={() => setSelectedRequest(null)} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors lg:hidden"
              >
                <Menu className="w-5 h-5" />
              </button>
              
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-gradient-to-br from-blue-900 to-teal-500 w-12 h-12 rounded-xl flex items-center justify-center shadow">
                  <span className="text-white text-xl font-bold">ن</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">نماء الاحترافية</h1>
                  <p className="text-sm text-gray-600">لوحة التحكم</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              {/* Search */}
              <div className="hidden md:block relative">
                <input
                  type="text"
                  placeholder="البحث..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <Search className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>

              {/* Notifications */}
              <div className="relative">
                <button 
                  onClick={() => setShowNotifications(!showNotifications)}
                  className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <Bell className="w-5 h-5" />
                  {unreadNotifications.length > 0 && (
                    <span className="absolute top-0 right-0 w-2.5 h-2.5 bg-red-500 rounded-full animate-pulse"></span>
                  )}
                </button>
                
                {showNotifications && (
                  <div className="absolute left-0 mt-2 w-96 bg-white rounded-xl shadow-xl border border-gray-200 z-50">
                    <div className="p-4 border-b flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">الإشعارات</h3>
                      <span className="text-sm text-gray-500">{unreadNotifications.length} جديد</span>
                    </div>
                    <div className="max-h-96 overflow-y-auto">
                      {urgentActions.length > 0 ? (
                        urgentActions.map((action, index) => (
                          <div 
                            key={index} 
                            className="p-4 border-b hover:bg-gray-50 cursor-pointer transition-colors"
                            onClick={() => {
                              setSelectedRequest(action);
                              setShowNotifications(false);
                            }}
                          >
                            <div className="flex items-start space-x-3 space-x-reverse">
                              <div className="flex-shrink-0">
                                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                                  action.nextAction.urgency === 'urgent' 
                                    ? 'bg-orange-100' 
                                    : 'bg-blue-100'
                                }`}>
                                  <AlertCircle className={`w-5 h-5 ${
                                    action.nextAction.urgency === 'urgent' 
                                      ? 'text-orange-600' 
                                      : 'text-blue-600'
                                  }`} />
                                </div>
                              </div>
                              <div className="flex-1">
                                <p className="text-sm font-medium text-gray-900">{action.nextAction.title}</p>
                                <p className="text-xs text-gray-500 mt-1">
                                  {action.service} • موعد التسليم: {action.nextAction.dueDate}
                                </p>
                              </div>
                              <button 
                                className="text-blue-600 hover:text-blue-800 text-sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  markNotificationAsRead(index);
                                }}
                              >
                                تم
                              </button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <EmptyState
                          icon={Bell}
                          title="لا توجد إشعارات جديدة"
                          description="ستظهر هنا جميع الإشعارات والتنبيهات"
                          className="py-8"
                        />
                      )}
                    </div>
                    <div className="p-3 border-t text-center">
                      <button className="text-sm text-blue-600 hover:text-blue-800">
                        عرض جميع الإشعارات
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Help */}
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <HelpCircle className="w-5 h-5" />
              </button>

              {/* User Profile */}
              <div className="flex items-center space-x-3 space-x-reverse border-r pr-4">
                <div className="text-left">
                  <p className="text-sm font-medium text-gray-900">{currentUser.name}</p>
                  <p className="text-xs text-gray-500">{currentUser.membershipLevel === 'premium' ? 'عضوية مميزة' : 'عضوية ذهبية'}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-teal-500 rounded-full flex items-center justify-center text-white font-bold text-lg shadow">
                  {currentUser.logo}
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className={`${sidebarOpen ? 'w-64' : 'w-0'} bg-white shadow-sm border-l transition-all duration-300 overflow-hidden lg:w-64`}>
          <nav className="p-4 space-y-2">
            {[
              { id: 'overview', label: 'نظرة عامة', icon: Home },
              { id: 'requests', label: 'الطلبات', icon: FileText, badge: stats.activeRequests },
              { id: 'financial', label: 'المالية', icon: CreditCard },
              { id: 'messages', label: 'الرسائل', icon: MessageCircle },
              { id: 'documents', label: 'المستندات', icon: FolderOpen },
              { id: 'archive', label: 'الأرشيف', icon: Archive }
            ].map((item) => (
              <button
                key={item.id}
                onClick={() => setSelectedView(item.id)}
                className={`w-full flex items-center justify-between px-4 py-3 rounded-lg transition-colors ${
                  selectedView === item.id
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center">
                  <item.icon className="w-5 h-5 ml-3" />
                  <span className="font-medium">{item.label}</span>
                </div>
                {item.badge && (
                  <span className="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs font-medium">
                    {item.badge}
                  </span>
                )}
              </button>
            ))}
          </nav>

          <div className="p-4 mt-auto border-t">
            <button 
              onClick={() => {
                localStorage.removeItem('namaUser');
                window.location.reload();
              }}
              className="w-full flex items-center px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <LogOut className="w-5 h-5 ml-3" />
              <span className="font-medium">تسجيل الخروج</span>
            </button>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {/* Content based on selected view */}
          {selectedView === 'overview' && (
            <>
              {/* Welcome Section */}
              <div className="bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 mb-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-3xl font-bold mb-2">مرحباً، {currentUser.name} 👋</h2>
                    <p className="text-blue-100">
                      لديك {stats.actionsRequired} إجراء يتطلب اهتمامك اليوم
                    </p>
                  </div>
                  <div className="text-left">
                    <p className="text-sm text-blue-100">مستوى العضوية</p>
                    <p className="text-2xl font-bold flex items-center">
                      {currentUser.membershipLevel === 'premium' ? '💎' : '⭐'} 
                      {currentUser.membershipLevel === 'premium' ? 'مميزة' : 'ذهبية'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <StatsCard 
                  icon={FileText} 
                  label="إجمالي الطلبات" 
                  value={stats.totalRequests}
                  trend={12}
                  color="blue"
                  onClick={() => setSelectedView('requests')}
                />
                <StatsCard 
                  icon={Activity} 
                  label="طلبات نشطة" 
                  value={stats.activeRequests}
                  color="green"
                  onClick={() => setSelectedView('requests')}
                />
                <StatsCard 
                  icon={AlertCircle} 
                  label="تتطلب إجراء" 
                  value={stats.actionsRequired}
                  trend={-25}
                  color="orange"
                  onClick={() => setSelectedView('requests')}
                />
                <StatsCard 
                  icon={Star} 
                  label="تقييم الرضا" 
                  value={`${stats.satisfaction}/5`}
                  color="purple"
                />
              </div>

              {/* Financial Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">النظرة المالية</h3>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-sm text-gray-600">إجمالي القيمة</span>
                        <span className="text-sm font-medium text-gray-900">
                          {stats.totalBudget.toLocaleString()} ريال
                        </span>
                      </div>
                      <ProgressBar 
                        progress={Math.round((stats.paidAmount / stats.totalBudget) * 100)} 
                        showLabel={false}
                        color="green"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                      <div className="text-center">
                        <p className="text-sm text-gray-600 mb-1">المدفوع</p>
                        <p className="text-xl font-bold text-green-600">
                          {stats.paidAmount.toLocaleString()}
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600 mb-1">المتبقي</p>
                        <p className="text-xl font-bold text-orange-600">
                          {(stats.totalBudget - stats.paidAmount).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات المؤسسة</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <MapPin className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-700">{currentUser.address}</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Phone className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-700" dir="ltr">{currentUser.phone}</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Mail className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-700">{currentUser.email}</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Globe className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-700">{currentUser.website}</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Calendar className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-700">
                        تأسست في {new Date(currentUser.establishedDate).getFullYear()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Active Requests Preview */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">الطلبات النشطة</h3>
                  <button 
                    onClick={() => setSelectedView('requests')}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    عرض الكل
                  </button>
                </div>
                
                {userRequests.length > 0 ? (
                  <div className="grid gap-4">
                    {userRequests.slice(0, 3).map((request) => (
                      <div 
                        key={request.id} 
                        className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors cursor-pointer"
                        onClick={() => setSelectedRequest(request)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3 space-x-reverse">
                            <div className="text-2xl">{request.serviceIcon}</div>
                            <div>
                              <h4 className="font-medium text-gray-900">{request.service}</h4>
                              <p className="text-sm text-gray-500">#{request.id} • {request.currentStep}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4 space-x-reverse">
                            <div className="text-left">
                              <p className="text-2xl font-bold text-blue-600">{request.progress}%</p>
                              <p className="text-xs text-gray-500">مكتمل</p>
                            </div>
                            <StatusBadge status={request.status} />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <EmptyState
                    icon={FileText}
                    title="لا توجد طلبات نشطة"
                    description="ابدأ بإنشاء طلب جديد للاستفادة من خدماتنا"
                    action={
                      <button className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        طلب خدمة جديدة
                      </button>
                    }
                  />
                )}
              </div>
            </>
          )}

          {selectedView === 'requests' && (
            <div>
              {/* Page Header */}
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">الطلبات</h2>
                <p className="text-gray-600">إدارة ومتابعة جميع طلباتك</p>
              </div>

              {/* Filters */}
              <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      placeholder="البحث في الطلبات..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <Search className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
                  </div>
                  
                  <div className="flex gap-2">
                    <select
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value)}
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">جميع الحالات</option>
                      <option value="action-required">يتطلب إجراء</option>
                      <option value="on-track">سير طبيعي</option>
                      <option value="delayed">متأخر</option>
                      <option value="completed">مكتمل</option>
                    </select>
                    
                    <select
                      value={filterPriority}
                      onChange={(e) => setFilterPriority(e.target.value)}
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">جميع الأولويات</option>
                      <option value="high">عالية</option>
                      <option value="medium">متوسطة</option>
                      <option value="low">منخفضة</option>
                    </select>
                    
                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center">
                      <Filter className="w-4 h-4 ml-2" />
                      المزيد
                    </button>
                  </div>
                </div>
              </div>

              {/* Requests Grid */}
              {filteredRequests.length > 0 ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {filteredRequests.map((request) => (
                    <div
                      key={request.id}
                      className="bg-white rounded-xl shadow-sm border hover:shadow-lg transition-all cursor-pointer"
                      onClick={() => setSelectedRequest(request)}
                    >
                      <div className="p-6">
                        {/* Header */}
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center">
                            <div className="text-3xl ml-3">{request.serviceIcon}</div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900">{request.service}</h3>
                              <p className="text-sm text-gray-500">#{request.id}</p>
                            </div>
                          </div>
                          <div className="flex flex-col items-end space-y-2">
                            <StatusBadge status={request.status} />
                            <PriorityBadge priority={request.priority} />
                          </div>
                        </div>

                        {/* Progress */}
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-600">المرحلة الحالية</span>
                            <span className="text-sm font-medium text-gray-900">{request.currentStep}</span>
                          </div>
                          <ProgressBar progress={request.progress} showLabel={false} />
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-500">
                              بدأ في {request.submissionDate}
                            </span>
                            <span className="text-xs text-gray-500">
                              متوقع في {request.expectedCompletion}
                            </span>
                          </div>
                        </div>

                        {/* Next Action */}
                        {request.nextAction && (
                          <div className={`p-4 rounded-lg mb-4 ${
                            request.nextAction.urgency === 'urgent' 
                              ? 'bg-orange-50 border border-orange-200' 
                              : 'bg-blue-50 border border-blue-200'
                          }`}>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm font-medium text-gray-900">
                                  {request.nextAction.title}
                                </p>
                                <p className="text-xs text-gray-600 mt-1 flex items-center">
                                  <Clock className="w-3 h-3 ml-1" />
                                  {request.nextAction.dueDate} • {request.nextAction.estimatedTime}
                                </p>
                              </div>
                              <ChevronLeft className="w-5 h-5 text-gray-400" />
                            </div>
                          </div>
                        )}

                        {/* Footer */}
                        <div className="flex items-center justify-between pt-4 border-t">
                          <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                            <div className="flex items-center">
                              <Users className="w-4 h-4 ml-1" />
                              {request.assignedTeam.length} أعضاء
                            </div>
                            <div className="flex items-center">
                              <CreditCard className="w-4 h-4 ml-1" />
                              {request.budget.total.toLocaleString()} ريال
                            </div>
                          </div>
                          <button 
                            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedRequest(request);
                            }}
                          >
                            عرض التفاصيل
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <EmptyState
                  icon={FileText}
                  title="لا توجد طلبات"
                  description="لم يتم العثور على طلبات تطابق معايير البحث"
                  action={
                    <button 
                      onClick={() => {
                        setSearchQuery('');
                        setFilterStatus('all');
                        setFilterPriority('all');
                      }}
                      className="mt-4 px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      إعادة تعيين الفلاتر
                    </button>
                  }
                />
              )}
            </div>
          )}

          {selectedView === 'financial' && (
            <div>
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">المعلومات المالية</h2>
                <p className="text-gray-600">نظرة شاملة على الجانب المالي لطلباتك</p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <CreditCard className="w-6 h-6 text-blue-600" />
                    </div>
                    <span className="text-sm text-gray-500">إجمالي</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalBudget.toLocaleString()} ريال</p>
                  <p className="text-sm text-gray-500 mt-1">إجمالي قيمة المشاريع</p>
                </div>
                
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <CheckCircle className="w-6 h-6 text-green-600" />
                    </div>
                    <span className="text-sm text-gray-500">مدفوع</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600">{stats.paidAmount.toLocaleString()} ريال</p>
                  <p className="text-sm text-gray-500 mt-1">المبلغ المدفوع</p>
                </div>
                
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                      <Clock className="w-6 h-6 text-orange-600" />
                    </div>
                    <span className="text-sm text-gray-500">متبقي</span>
                  </div>
                  <p className="text-2xl font-bold text-orange-600">
                    {(stats.totalBudget - stats.paidAmount).toLocaleString()} ريال
                  </p>
                  <p className="text-sm text-gray-500 mt-1">المبلغ المتبقي</p>
                </div>
              </div>
              
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل المدفوعات حسب المشروع</h3>
                <div className="space-y-4">
                  {userRequests.map((request) => (
                    <div key={request.id} className="border-b pb-4 last:border-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <span className="text-2xl">{request.serviceIcon}</span>
                          <div>
                            <h4 className="font-medium text-gray-900">{request.service}</h4>
                            <p className="text-sm text-gray-500">#{request.id}</p>
                          </div>
                        </div>
                        <div className="text-left">
                          <p className="font-semibold text-gray-900">
                            {request.budget.total.toLocaleString()} ريال
                          </p>
                          <p className="text-xs text-gray-500">
                            مدفوع: {Math.round((request.budget.paid / request.budget.total) * 100)}%
                          </p>
                        </div>
                      </div>
                      <ProgressBar 
                        progress={Math.round((request.budget.paid / request.budget.total) * 100)} 
                        showLabel={false}
                        height="h-2"
                        color="green"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {selectedView === 'messages' && (
            <div>
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">المراسلات</h2>
                <p className="text-gray-600">جميع مراسلاتك مع فريق نماء</p>
              </div>
              
              <div className="bg-white rounded-xl shadow-sm">
                <EmptyState
                  icon={MessageCircle}
                  title="لا توجد رسائل"
                  description="ستظهر هنا جميع مراسلاتك مع فريق نماء"
                  action={
                    <button className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                      <Plus className="w-4 h-4 ml-2" />
                      بدء محادثة جديدة
                    </button>
                  }
                  className="py-12"
                />
              </div>
            </div>
          )}

          {selectedView === 'documents' && (
            <div>
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">المستندات</h2>
                <p className="text-gray-600">جميع المستندات المتعلقة بطلباتك</p>
              </div>
              
              <div className="bg-white rounded-xl shadow-sm">
                <EmptyState
                  icon={FolderOpen}
                  title="لا توجد مستندات"
                  description="ستظهر هنا جميع المستندات المتعلقة بطلباتك"
                  className="py-12"
                />
              </div>
            </div>
          )}

          {selectedView === 'archive' && (
            <div>
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">الأرشيف</h2>
                <p className="text-gray-600">الطلبات المكتملة والمؤرشفة</p>
              </div>
              
              <div className="bg-white rounded-xl shadow-sm">
                <EmptyState
                  icon={Archive}
                  title="لا توجد طلبات مؤرشفة"
                  description="ستظهر هنا الطلبات المكتملة والمؤرشفة"
                  className="py-12"
                />
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

// ==================== المكون الرئيسي للتطبيق ====================
const NamaClientSystemPro = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // إعداد البيئة
    document.documentElement.dir = 'rtl';
    document.documentElement.lang = 'ar';
    document.body.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
    
    // إضافة بعض الأنماط العامة
    const style = document.createElement('style');
    style.textContent = `
      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
        20%, 40%, 60%, 80% { transform: translateX(2px); }
      }
      
      @keyframes slide {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }
      
      .animate-shake {
        animation: shake 0.5s ease-in-out;
      }
      
      .animate-slide {
        animation: slide 2s linear infinite;
      }
      
      /* تحسين الخطوط العربية */
      * {
        font-feature-settings: "liga" 1, "calt" 1;
      }
      
      /* تحسين التمرير */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }
    `;
    document.head.appendChild(style);
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => {
      document.documentElement.dir = 'ltr';
      document.documentElement.lang = 'en';
      document.head.removeChild(style);
    };
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-teal-50">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-900 to-teal-500 rounded-3xl shadow-2xl mb-6 animate-pulse">
            <span className="text-white text-4xl font-bold">ن</span>
          </div>
          <LoadingSpinner size="large" />
          <p className="mt-4 text-gray-600">جاري تحميل النظام...</p>
        </div>
      </div>
    );
  }

  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
};

const AppContent = () => {
  const { currentUser } = useApp();
  
  if (!currentUser) {
    return <LoginPage />;
  }
  
  return <Dashboard />;
};

export default NamaClientSystemPro;