import React, { useState } from 'react';
import { ChevronRight, Calendar, FileText, MessageCircle, Upload, Download, CheckCircle, Clock, AlertCircle, User, LogOut, Send, Plus } from 'lucide-react';

const NamaClientSystem = () => {
  const [currentUser, setCurrentUser] = useState(null);
  const [currentView, setCurrentView] = useState('login');
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [linkInput, setLinkInput] = useState('');
  const [submittedLinks, setSubmittedLinks] = useState([
    'https://drive.google.com/file/d/1234567890/view?usp=sharing',
    'https://www.dropbox.com/s/abc123/document.pdf?dl=0'
  ]);
  const [activeTab, setActiveTab] = useState('current'); // current, plan, previous

  // إعداد اتجاه RTL للصفحة
  React.useEffect(() => {
    document.documentElement.dir = 'rtl';
    document.documentElement.lang = 'ar';
    return () => {
      document.documentElement.dir = 'ltr';
      document.documentElement.lang = 'en';
    };
  }, []);

  // بيانات العملاء
  const users = {
    '<EMAIL>': {
      password: '123456',
      name: 'جمعية الأمل الخيرية',
      contact: 'أحمد محمد السالم',
      phone: '0501234567',
      email: '<EMAIL>'
    },
    '<EMAIL>': {
      password: '123456', 
      name: 'جمعية روافد التنموية',
      contact: 'فاطمة أحمد العلي',
      phone: '0509876543',
      email: '<EMAIL>'
    }
  };

  // طلبات العملاء
  const requests = [
    {
      id: 'REQ-001',
      clientEmail: '<EMAIL>',
      service: 'خدمات التأسيس',
      submissionDate: '2024-05-15',
      currentStep: 'إعداد النظام الأساسي',
      progress: 65,
      status: 'يتطلب إجراء منك',
      nextAction: {
        title: 'مراجعة وتوقيع النظام الأساسي',
        description: 'تم إرسال النظام الأساسي المحدث لمراجعتكم. يرجى مراجعته وتوقيعه وإرساله خلال 3 أيام.',
        dueDate: '2024-06-20',
        urgency: 'urgent',
        documents: ['النظام_الأساسي_النهائي.pdf', 'نموذج_التوقيع.pdf'],
        estimatedTime: '30 دقيقة'
      },
      timeline: [
        { step: 'تقديم الطلب', date: '2024-05-15', status: 'completed' },
        { step: 'مراجعة المستندات', date: '2024-05-18', status: 'completed' },
        { step: 'إعداد النظام الأساسي', date: '2024-06-01', status: 'current' },
        { step: 'التسجيل الرسمي', date: null, status: 'upcoming' },
        { step: 'التسليم النهائي', date: null, status: 'upcoming' }
      ],
      recentUpdates: [
        { date: '2024-06-12', message: 'تم إرسال النظام الأساسي للمراجعة', type: 'info' },
        { date: '2024-06-08', message: 'انتهاء صياغة النظام الأساسي', type: 'success' }
      ]
    },
    {
      id: 'REQ-002',
      clientEmail: '<EMAIL>',
      service: 'الخدمات المالية والإدارية',
      submissionDate: '2024-04-10',
      currentStep: 'تطبيق النظام المحاسبي',
      progress: 90,
      status: 'سير طبيعي',
      nextAction: {
        title: 'جلسة تدريب نهائية',
        description: 'جلسة تدريب أخيرة لفريق المحاسبة على النظام الجديد وآليات إعداد التقارير.',
        dueDate: '2024-06-18',
        urgency: 'normal',
        documents: [],
        estimatedTime: 'ساعتين'
      },
      timeline: [
        { step: 'تقديم الطلب', date: '2024-04-10', status: 'completed' },
        { step: 'تحليل الاحتياجات', date: '2024-04-15', status: 'completed' },
        { step: 'تركيب النظام', date: '2024-05-01', status: 'completed' },
        { step: 'تطبيق النظام المحاسبي', date: '2024-05-15', status: 'current' },
        { step: 'التسليم النهائي', date: null, status: 'upcoming' }
      ],
      recentUpdates: [
        { date: '2024-06-10', message: 'تم إنجاز 90% من العمل', type: 'success' },
        { date: '2024-06-05', message: 'بدء تدريب الموظفين', type: 'info' }
      ]
    },
    {
      id: 'REQ-003',
      clientEmail: '<EMAIL>',
      service: 'خدمة حوكمة الجمعيات',
      submissionDate: '2024-06-01',
      currentStep: 'تحليل الوضع الحالي',
      progress: 25,
      status: 'يتطلب إجراء منك',
      nextAction: {
        title: 'تعبئة استبيان تقييم الحوكمة',
        description: 'استبيان شامل لتقييم الوضع الحالي للحوكمة في جمعيتكم. سيساعدنا في وضع خطة العمل المناسبة.',
        dueDate: '2024-06-17',
        urgency: 'urgent',
        documents: ['استبيان_تقييم_الحوكمة.pdf'],
        estimatedTime: '45 دقيقة'
      },
      timeline: [
        { step: 'تقديم الطلب', date: '2024-06-01', status: 'completed' },
        { step: 'تحليل الوضع الحالي', date: '2024-06-05', status: 'current' },
        { step: 'وضع خطة العمل', date: null, status: 'upcoming' },
        { step: 'تطبيق اللوائح', date: null, status: 'upcoming' },
        { step: 'التسليم النهائي', date: null, status: 'upcoming' }
      ],
      recentUpdates: [
        { date: '2024-06-12', message: 'تم إرسال استبيان التقييم', type: 'info' },
        { date: '2024-06-05', message: 'بدء العمل على المشروع', type: 'success' }
      ]
    }
  ];

  // وظائف النظام
  const handleLogin = (email, password) => {
    if (users[email] && users[email].password === password) {
      setCurrentUser(users[email]);
      setCurrentView('dashboard');
      return true;
    }
    return false;
  };

  const handleLogout = () => {
    setCurrentUser(null);
    setCurrentView('login');
    setSelectedRequest(null);
  };

  const getUserRequests = () => {
    return requests.filter(req => req.clientEmail === currentUser?.email);
  };

  const handleLinkSubmit = () => {
    if (linkInput.trim() && isValidLink(linkInput)) {
      setSubmittedLinks([...submittedLinks, linkInput.trim()]);
      setLinkInput('');
      alert('تم إرسال الرابط بنجاح! ✅');
    }
  };

  const isValidLink = (url) => {
    return url.includes('drive.google.com') || 
           url.includes('dropbox.com') || 
           url.includes('onedrive') ||
           url.startsWith('http');
  };

  // صفحة تسجيل الدخول
  const LoginPage = () => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');

    const handleSubmit = () => {
      if (email && password && handleLogin(email, password)) {
        setError('');
      } else {
        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
      }
    };

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-teal-50 flex items-center justify-center p-4" 
           dir="rtl" style={{ fontFamily: 'Segoe UI, Tahoma, Arial, Helvetica, sans-serif' }}>
        <div className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-md">
          <div className="text-center mb-8">
            <div className="bg-gradient-to-r from-blue-900 to-teal-500 w-16 h-16 rounded-lg mx-auto mb-4 flex items-center justify-center">
              <span className="text-white text-2xl font-bold">ن</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">نماء الاحترافية</h1>
            <p className="text-gray-600">لحلول الأعمال</p>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 text-right">
                البريد الإلكتروني
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-right"
                placeholder="أدخل بريدك الإلكتروني"
                onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 text-right">
                كلمة المرور
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-right"
                placeholder="أدخل كلمة المرور"
                onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
              />
            </div>

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg text-right">
                {error}
              </div>
            )}

            <button
              onClick={handleSubmit}
              className="w-full bg-gradient-to-r from-blue-900 to-teal-500 text-white py-3 px-4 rounded-lg hover:from-blue-800 hover:to-teal-600 transition duration-200 font-medium"
            >
              دخول
            </button>
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-lg text-center">
            <p className="text-sm text-gray-600 mb-2">للتجربة:</p>
            <div className="text-xs text-gray-500 space-y-1">
              <div><EMAIL> : 123456</div>
              <div><EMAIL> : 123456</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // لوحة التحكم الرئيسية
  const Dashboard = () => {
    const userRequests = getUserRequests();
    const urgentActions = userRequests.filter(req => req.nextAction?.urgency === 'urgent');

    return (
      <div className="min-h-screen bg-gray-50" dir="rtl" style={{ fontFamily: 'Segoe UI, Tahoma, Arial, Helvetica, sans-serif' }}>
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-blue-900 to-teal-500 w-10 h-10 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg font-bold">ن</span>
              </div>
              <div className="text-right">
                <h1 className="text-xl font-bold text-gray-800">نماء الاحترافية</h1>
                <p className="text-sm text-gray-600">متابعة طلباتك</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <button 
                onClick={handleLogout}
                className="text-gray-600 hover:text-gray-800 p-2"
              >
                <LogOut className="w-5 h-5" />
              </button>
              <User className="w-8 h-8 text-gray-600 bg-gray-100 rounded-full p-1" />
              <div className="text-right">
                <p className="text-sm font-medium text-gray-800">{currentUser?.name}</p>
                <p className="text-xs text-gray-600">{currentUser?.contact}</p>
              </div>
            </div>
          </div>
        </header>

        {/* المحتوى الرئيسي */}
        <main className="max-w-7xl mx-auto px-4 py-6">
          
          {/* تنبيهات عاجلة */}
          {urgentActions.length > 0 && (
            <div className="mb-6">
              <div className="bg-red-50 border-2 border-red-200 rounded-lg p-6">
                <div className="flex items-center space-x-3 space-x-reverse mb-4">
                  <AlertCircle className="w-6 h-6 text-red-600" />
                  <h3 className="text-xl font-bold text-red-800">⚠️ مطلوب إجراء عاجل</h3>
                </div>
                {urgentActions.map((request) => (
                  <div key={request.id} className="bg-white rounded-lg p-4 mb-3 last:mb-0 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="text-right">
                        <p className="font-bold text-gray-800">{request.service}</p>
                        <p className="text-sm text-gray-600">{request.nextAction.title}</p>
                        <p className="text-xs text-red-600 mt-1">⏰ الموعد النهائي: {request.nextAction.dueDate}</p>
                      </div>
                      <button 
                        onClick={() => {
                          setSelectedRequest(request);
                          setCurrentView('request');
                        }}
                        className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 font-medium"
                      >
                        اتخذ إجراء فوراً
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* ملخص سريع */}
          <div className="grid grid-cols-3 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm p-6 text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <p className="text-sm text-gray-600 mb-1">طلباتك النشطة</p>
              <p className="text-3xl font-bold text-blue-600">{userRequests.length}</p>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mb-3">
                <Clock className="w-6 h-6 text-orange-600" />
              </div>
              <p className="text-sm text-gray-600 mb-1">إجراءات مطلوبة</p>
              <p className="text-3xl font-bold text-orange-600">
                {userRequests.filter(req => req.status === 'يتطلب إجراء منك').length}
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <p className="text-sm text-gray-600 mb-1">متوسط التقدم</p>
              <p className="text-3xl font-bold text-green-600">
                {Math.round(userRequests.reduce((sum, req) => sum + req.progress, 0) / userRequests.length)}%
              </p>
            </div>
          </div>

          {/* قائمة الطلبات */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800 text-center">طلباتك</h2>
            
            <div className="grid grid-cols-2 gap-6">
              {userRequests.map((request) => (
                <div key={request.id} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
                     onClick={() => {
                       setSelectedRequest(request);
                       setCurrentView('request');
                     }}>
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1 text-right">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm text-gray-500">#{request.id}</span>
                          <h3 className="text-lg font-bold text-gray-800">{request.service}</h3>
                        </div>
                        
                        <div className="flex items-center justify-between mb-4">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                            request.status === 'يتطلب إجراء منك' ? 'bg-orange-100 text-orange-800' :
                            request.status === 'سير طبيعي' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {request.status}
                          </span>
                          <span className="text-sm text-gray-600">المرحلة: {request.currentStep}</span>
                        </div>

                        {/* شريط التقدم */}
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-lg font-bold text-blue-600">{request.progress}%</span>
                            <span className="text-sm text-gray-600">التقدم</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div 
                              className="bg-gradient-to-l from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300"
                              style={{ width: `${request.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* المطلوب منك */}
                    {request.nextAction && (
                      <div className={`p-4 rounded-lg border-2 mb-4 ${
                        request.nextAction.urgency === 'urgent' 
                          ? 'bg-red-50 border-red-200' 
                          : 'bg-blue-50 border-blue-200'
                      }`}>
                        <h4 className={`font-bold mb-2 text-center ${
                          request.nextAction.urgency === 'urgent' 
                            ? 'text-red-900' 
                            : 'text-blue-900'
                        }`}>
                          المطلوب منك:
                        </h4>
                        <p className={`text-sm text-center ${
                          request.nextAction.urgency === 'urgent' 
                            ? 'text-red-700' 
                            : 'text-blue-700'
                        }`}>
                          {request.nextAction.title}
                        </p>
                        <p className="text-xs text-center text-gray-600 mt-2">
                          ⏰ {request.nextAction.dueDate} • ⏱️ {request.nextAction.estimatedTime}
                        </p>
                      </div>
                    )}

                    {/* Footer */}
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span className="text-blue-600 hover:text-blue-800 font-medium">
                        👈 انقر للدخول
                      </span>
                      <span>بدأ في: {request.submissionDate}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </main>
      </div>
    );
  };

  // صفحة تفاصيل الطلب - مع التبويبات
  const RequestDetails = () => {
    return (
      <div className="min-h-screen bg-gray-50" dir="rtl" style={{ fontFamily: 'Segoe UI, Tahoma, Arial, Helvetica, sans-serif' }}>
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
            <div className="flex items-center space-x-4 space-x-reverse">
              <button 
                onClick={() => setCurrentView('dashboard')}
                className="text-gray-600 hover:text-gray-800"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
              <div className="text-right">
                <h1 className="text-xl font-bold text-gray-800">{selectedRequest.service}</h1>
                <p className="text-sm text-gray-600">#{selectedRequest.id}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <button 
                onClick={handleLogout}
                className="text-gray-600 hover:text-gray-800 p-2"
              >
                <LogOut className="w-5 h-5" />
              </button>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                selectedRequest.status === 'يتطلب إجراء منك' ? 'bg-orange-100 text-orange-800' :
                selectedRequest.status === 'سير طبيعي' ? 'bg-green-100 text-green-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {selectedRequest.status}
              </span>
            </div>
          </div>
        </header>

        {/* المحتوى الرئيسي - القسمين */}
        <main className="max-w-7xl mx-auto px-4 py-6">
          <div className="grid grid-cols-12 gap-8">
            
            {/* القسم الأيسر: مراحل العمل */}
            <div className="col-span-5 order-1">
              <div className="bg-white rounded-lg shadow-sm sticky top-6">
                <div className="p-6 border-b">
                  <h2 className="text-2xl font-bold text-gray-800 text-center">
                    🔄 مراحل العمل
                  </h2>
                </div>
                
                <div className="p-6">
                  {/* التقدم العام */}
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-2xl font-bold text-blue-600">{selectedRequest.progress}%</span>
                      <span className="text-lg text-gray-600">مكتمل</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-6 mb-4">
                      <div 
                        className="bg-gradient-to-l from-blue-500 to-green-500 h-6 rounded-full transition-all duration-500 relative"
                        style={{ width: `${selectedRequest.progress}%` }}
                      >
                        <div className="absolute left-2 top-1 h-4 w-1 bg-white rounded-full"></div>
                      </div>
                    </div>
                    <div className="bg-blue-50 rounded-lg p-4 text-center">
                      <p className="text-sm text-gray-600 mb-1">المرحلة الحالية</p>
                      <p className="font-bold text-blue-900">{selectedRequest.currentStep}</p>
                    </div>
                  </div>

                  {/* مراحل العمل */}
                  <div className="space-y-5">
                    {selectedRequest.timeline.map((step, index) => (
                      <div key={index} className="flex items-start space-x-4 space-x-reverse relative">
                        {/* خط التوصيل */}
                        {index < selectedRequest.timeline.length - 1 && (
                          <div className="absolute top-8 right-4 w-0.5 h-12 bg-gray-200"></div>
                        )}
                        
                        {/* النقطة والرقم */}
                        <div className={`relative z-10 flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold border-2 ${
                          step.status === 'completed' 
                            ? 'bg-green-500 border-green-500 text-white' :
                          step.status === 'current' 
                            ? 'bg-blue-500 border-blue-500 text-white animate-pulse' :
                            'bg-gray-100 border-gray-300 text-gray-500'
                        }`}>
                          {step.status === 'completed' ? '✓' : index + 1}
                        </div>
                        
                        {/* تفاصيل المرحلة */}
                        <div className="flex-1 text-right pb-6">
                          <h4 className={`font-medium text-sm mb-1 ${
                            step.status === 'current' ? 'text-blue-600 font-bold' : 
                            step.status === 'completed' ? 'text-green-600' : 'text-gray-500'
                          }`}>
                            {step.step}
                          </h4>
                          {step.date && (
                            <p className="text-xs text-gray-500">
                              {step.status === 'completed' ? '✅ تم في: ' : '📅 مقرر في: '}{step.date}
                            </p>
                          )}
                          {step.status === 'current' && (
                            <div className="mt-2">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                🔥 قيد العمل
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* آخر التحديثات */}
                  <div className="mt-8 pt-6 border-t">
                    <h4 className="text-lg font-bold text-gray-800 mb-4 text-center">📢 آخر التحديثات</h4>
                    <div className="space-y-3">
                      {selectedRequest.recentUpdates.map((update, index) => (
                        <div key={index} className={`p-4 rounded-lg border-l-4 ${
                          update.type === 'success' ? 'bg-green-50 border-green-400' :
                          update.type === 'info' ? 'bg-blue-50 border-blue-400' :
                          'bg-gray-50 border-gray-400'
                        }`}>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-500">{update.date}</span>
                            <p className={`font-medium text-right flex-1 mr-3 ${
                              update.type === 'success' ? 'text-green-800' :
                              update.type === 'info' ? 'text-blue-800' :
                              'text-gray-800'
                            }`}>
                              {update.message}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* معلومات الطلب */}
                  <div className="mt-8 pt-6 border-t bg-gray-50 rounded-lg p-4">
                    <h4 className="text-lg font-bold text-gray-800 mb-4 text-center">📋 معلومات الطلب</h4>
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div className="bg-white rounded-lg p-3">
                        <p className="text-sm text-gray-500 mb-1">رقم الطلب</p>
                        <p className="font-bold text-gray-800">{selectedRequest.id}</p>
                      </div>
                      <div className="bg-white rounded-lg p-3">
                        <p className="text-sm text-gray-500 mb-1">تاريخ البداية</p>
                        <p className="font-bold text-gray-800">{selectedRequest.submissionDate}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* القسم الأيمن: التبويبات */}
            <div className="col-span-7 order-2">
              <div className="bg-white rounded-lg shadow-sm">
                {/* التبويبات */}
                <div className="border-b">
                  <nav className="flex">
                    <button
                      onClick={() => setActiveTab('current')}
                      className={`flex-1 px-6 py-4 text-center font-medium border-b-2 transition-colors ${
                        activeTab === 'current'
                          ? 'border-blue-500 text-blue-600 bg-blue-50'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      ⚡ الإجراء الحالي
                    </button>
                    <button
                      onClick={() => setActiveTab('plan')}
                      className={`flex-1 px-6 py-4 text-center font-medium border-b-2 transition-colors ${
                        activeTab === 'plan'
                          ? 'border-blue-500 text-blue-600 bg-blue-50'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      📋 تفاصيل الخطة
                    </button>
                    <button
                      onClick={() => setActiveTab('previous')}
                      className={`flex-1 px-6 py-4 text-center font-medium border-b-2 transition-colors ${
                        activeTab === 'previous'
                          ? 'border-blue-500 text-blue-600 bg-blue-50'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      ✅ الإجراءات السابقة
                    </button>
                  </nav>
                </div>

                {/* محتوى التبويبات */}
                <div className="p-6">
                  
                  {/* تبويب الإجراء الحالي */}
                  {activeTab === 'current' && (
                    <div>
                      {selectedRequest.nextAction ? (
                        <div className="space-y-6">
                          {/* عنوان المطلوب */}
                          <div className={`p-6 rounded-xl border-2 text-center ${
                            selectedRequest.nextAction.urgency === 'urgent' 
                              ? 'bg-red-50 border-red-200' 
                              : 'bg-blue-50 border-blue-200'
                          }`}>
                            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${
                              selectedRequest.nextAction.urgency === 'urgent' 
                                ? 'bg-red-100' 
                                : 'bg-blue-100'
                            }`}>
                              <AlertCircle className={`w-8 h-8 ${
                                selectedRequest.nextAction.urgency === 'urgent' 
                                  ? 'text-red-600' 
                                  : 'text-blue-600'
                              }`} />
                            </div>
                            
                            <h3 className={`text-2xl font-bold mb-3 ${
                              selectedRequest.nextAction.urgency === 'urgent' 
                                ? 'text-red-900' 
                                : 'text-blue-900'
                            }`}>
                              {selectedRequest.nextAction.title}
                            </h3>
                            
                            <p className={`text-lg leading-relaxed mb-4 ${
                              selectedRequest.nextAction.urgency === 'urgent' 
                                ? 'text-red-700' 
                                : 'text-blue-700'
                            }`}>
                              {selectedRequest.nextAction.description}
                            </p>
                            
                            {/* معلومات مهمة */}
                            <div className="grid grid-cols-3 gap-4">
                              <div className={`p-4 rounded-lg ${
                                selectedRequest.nextAction.urgency === 'urgent' 
                                  ? 'bg-red-100' 
                                  : 'bg-blue-100'
                              }`}>
                                <p className="text-xs text-gray-600 mb-1">⏰ الموعد النهائي</p>
                                <p className={`font-bold text-lg ${
                                  selectedRequest.nextAction.urgency === 'urgent' 
                                    ? 'text-red-800' 
                                    : 'text-blue-800'
                                }`}>
                                  {selectedRequest.nextAction.dueDate}
                                </p>
                              </div>
                              <div className={`p-4 rounded-lg ${
                                selectedRequest.nextAction.urgency === 'urgent' 
                                  ? 'bg-red-100' 
                                  : 'bg-blue-100'
                              }`}>
                                <p className="text-xs text-gray-600 mb-1">⏱️ الوقت المطلوب</p>
                                <p className={`font-bold text-lg ${
                                  selectedRequest.nextAction.urgency === 'urgent' 
                                    ? 'text-red-800' 
                                    : 'text-blue-800'
                                }`}>
                                  {selectedRequest.nextAction.estimatedTime}
                                </p>
                              </div>
                              <div className={`p-4 rounded-lg ${
                                selectedRequest.nextAction.urgency === 'urgent' 
                                  ? 'bg-red-100' 
                                  : 'bg-blue-100'
                              }`}>
                                <p className="text-xs text-gray-600 mb-1">📄 المستندات</p>
                                <p className={`font-bold text-lg ${
                                  selectedRequest.nextAction.urgency === 'urgent' 
                                    ? 'text-red-800' 
                                    : 'text-blue-800'
                                }`}>
                                  {selectedRequest.nextAction.documents.length} ملف
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* المستندات المطلوبة */}
                          {selectedRequest.nextAction.documents.length > 0 && (
                            <div>
                              <h4 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                                📋 المستندات المطلوبة
                              </h4>
                              <div className="grid grid-cols-1 gap-3">
                                {selectedRequest.nextAction.documents.map((doc, index) => (
                                  <div key={index} className="flex items-center justify-between p-4 border-2 border-blue-200 rounded-lg bg-blue-50">
                                    <button className="text-blue-600 hover:text-blue-800 p-2 bg-blue-100 rounded-lg">
                                      <Download className="w-5 h-5" />
                                    </button>
                                    <div className="flex items-center space-x-3 space-x-reverse flex-1">
                                      <span className="font-medium text-gray-800 text-center flex-1">{doc}</span>
                                      <FileText className="w-6 h-6 text-blue-600" />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* إرسال المستندات */}
                          <div>
                            <h4 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                              📤 إرسال المستندات
                            </h4>
                            
                            <div className="space-y-4">
                              {/* نموذج الرابط */}
                              <div className="p-6 border-2 border-green-200 rounded-lg bg-green-50">
                                <div className="text-center mb-4">
                                  <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-2">
                                    <span className="text-2xl">🔗</span>
                                  </div>
                                  <p className="text-sm text-green-700 font-medium">
                                    شارك رابط ملفاتك من Google Drive أو Dropbox
                                  </p>
                                </div>
                                
                                <div className="flex space-x-3 space-x-reverse">
                                  <button
                                    onClick={handleLinkSubmit}
                                    disabled={!linkInput.trim() || !isValidLink(linkInput)}
                                    className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium"
                                  >
                                    📤 إرسال
                                  </button>
                                  <input
                                    type="url"
                                    value={linkInput}
                                    onChange={(e) => setLinkInput(e.target.value)}
                                    placeholder="https://drive.google.com/... أو https://dropbox.com/..."
                                    className="flex-1 px-4 py-3 border-2 border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-center"
                                    onKeyPress={(e) => e.key === 'Enter' && handleLinkSubmit()}
                                    dir="ltr"
                                  />
                                </div>
                              </div>

                              {/* الروابط المرسلة */}
                              {submittedLinks.length > 0 && (
                                <div>
                                  <h5 className="text-md font-medium text-gray-700 mb-3 text-center">✅ الروابط المرسلة</h5>
                                  <div className="space-y-2">
                                    {submittedLinks.map((link, index) => (
                                      <div key={index} className="flex items-center justify-between p-4 bg-green-100 border border-green-300 rounded-lg">
                                        <span className="text-sm text-green-700 font-medium">تم الإرسال ✓</span>
                                        <span className="text-sm text-gray-700 truncate max-w-md text-center flex-1">{link}</span>
                                        <CheckCircle className="w-5 h-5 text-green-600" />
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* أزرار إضافية */}
                              <div className="flex justify-center space-x-4 space-x-reverse pt-6 border-t">
                                <button className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 font-medium">
                                  💬 تواصل معنا
                                </button>
                                <button className="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium">
                                  ✏️ إضافة تعليق
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                            <CheckCircle className="w-8 h-8 text-green-600" />
                          </div>
                          <h3 className="text-xl font-bold text-green-800 mb-2">
                            🎉 لا يوجد إجراءات مطلوبة حالياً
                          </h3>
                          <p className="text-green-600">
                            فريق نماء يعمل على طلبك. سنتواصل معك عند الحاجة لأي إجراء.
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* تبويب تفاصيل الخطة */}
                  {activeTab === 'plan' && (
                    <div className="space-y-6">
                      <div className="text-center mb-6">
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                          <FileText className="w-8 h-8 text-blue-600" />
                        </div>
                        <h3 className="text-2xl font-bold text-blue-900 mb-2">خطة العمل الشاملة</h3>
                        <p className="text-blue-700">نظرة عامة على جميع مراحل مشروعك</p>
                      </div>

                      {/* معلومات المشروع */}
                      <div className="grid grid-cols-2 gap-6">
                        <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-6 text-center">
                          <h4 className="text-lg font-bold text-blue-900 mb-4">📊 معلومات المشروع</h4>
                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between">
                              <span className="font-medium text-blue-800">نوع الخدمة:</span>
                              <span className="text-gray-700">{selectedRequest.service}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="font-medium text-blue-800">تاريخ البداية:</span>
                              <span className="text-gray-700">{selectedRequest.submissionDate}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="font-medium text-blue-800">المرحلة الحالية:</span>
                              <span className="text-gray-700">{selectedRequest.currentStep}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="font-medium text-blue-800">نسبة الإنجاز:</span>
                              <span className="text-blue-600 font-bold">{selectedRequest.progress}%</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-green-50 border-2 border-green-200 rounded-lg p-6 text-center">
                          <h4 className="text-lg font-bold text-green-900 mb-4">⏱️ الجدول الزمني</h4>
                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between">
                              <span className="font-medium text-green-800">المراحل المكتملة:</span>
                              <span className="text-gray-700">
                                {selectedRequest.timeline.filter(step => step.status === 'completed').length}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="font-medium text-green-800">المراحل المتبقية:</span>
                              <span className="text-gray-700">
                                {selectedRequest.timeline.filter(step => step.status === 'upcoming').length}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="font-medium text-green-800">إجمالي المراحل:</span>
                              <span className="text-gray-700">{selectedRequest.timeline.length}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* تفاصيل المراحل */}
                      <div>
                        <h4 className="text-lg font-bold text-gray-800 mb-4 text-center">📋 تفاصيل جميع المراحل</h4>
                        <div className="space-y-4">
                          {selectedRequest.timeline.map((step, index) => (
                            <div key={index} className={`p-6 rounded-lg border-2 ${
                              step.status === 'completed' ? 'bg-green-50 border-green-200' :
                              step.status === 'current' ? 'bg-blue-50 border-blue-200' :
                              'bg-gray-50 border-gray-200'
                            }`}>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4 space-x-reverse">
                                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
                                    step.status === 'completed' ? 'bg-green-500' :
                                    step.status === 'current' ? 'bg-blue-500' :
                                    'bg-gray-400'
                                  }`}>
                                    {step.status === 'completed' ? '✓' : index + 1}
                                  </div>
                                  <div className="text-right">
                                    <h5 className="font-bold text-gray-800">{step.step}</h5>
                                    {step.date && (
                                      <p className="text-sm text-gray-600">
                                        {step.status === 'completed' ? 'تم في: ' : 'مخطط له في: '}{step.date}
                                      </p>
                                    )}
                                  </div>
                                </div>
                                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                                  step.status === 'completed' ? 'bg-green-100 text-green-800' :
                                  step.status === 'current' ? 'bg-blue-100 text-blue-800' :
                                  'bg-gray-100 text-gray-600'
                                }`}>
                                  {step.status === 'completed' ? 'مكتمل' :
                                   step.status === 'current' ? 'جاري العمل' : 'قادم'}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* ملاحظات إضافية */}
                      <div className="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-6">
                        <h4 className="text-lg font-bold text-yellow-900 mb-3 text-center">📝 ملاحظات مهمة</h4>
                        <ul className="text-sm text-yellow-800 space-y-2 text-center">
                          <li>• قد تختلف المواعيد حسب سرعة استجابتكم للمطلوبات</li>
                          <li>• يمكنكم التواصل معنا في أي وقت لأي استفسارات</li>
                          <li>• سيتم إشعاركم عند كل تحديث أو انتقال لمرحلة جديدة</li>
                        </ul>
                      </div>
                    </div>
                  )}

                  {/* تبويب الإجراءات السابقة */}
                  {activeTab === 'previous' && (
                    <div className="space-y-4">
                      {selectedRequest.timeline
                        .filter(step => step.status === 'completed')
                        .map((step, index) => (
                        <div key={index} className="bg-green-50 border-2 border-green-200 rounded-lg p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4 space-x-reverse">
                              <div className="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center text-white font-bold">
                                ✓
                              </div>
                              <div className="text-right">
                                <h5 className="font-bold text-green-900">{step.step}</h5>
                                <p className="text-sm text-green-700">اكتمل في: {step.date}</p>
                              </div>
                            </div>
                            <span className="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              مكتمل ✓
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  };

  // العرض الرئيسي
  if (currentView === 'login') {
    return <LoginPage />;
  } else if (currentView === 'dashboard') {
    return <Dashboard />;
  } else if (currentView === 'request') {
    return <RequestDetails />;
  }
};

export default NamaClientSystem;