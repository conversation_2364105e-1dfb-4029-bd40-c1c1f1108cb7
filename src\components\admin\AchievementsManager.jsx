import React, { useState, useEffect } from 'react'
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  CheckCircle,
  Users,
  DollarSign,
  Activity,
  TrendingUp,
  Award,
  Target,
  Star,
  Zap,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'
import useStore from '../../store/useStore'

const AchievementsManager = ({ onClose }) => {
  const { user } = useStore()
  const [achievements, setAchievements] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedAchievement, setSelectedAchievement] = useState(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    value: '',
    improvement: '',
    icon: 'CheckCircle',
    color: 'green',
    sort_order: 0,
    is_active: true
  })

  // الأيقونات المتاحة
  const availableIcons = [
    { name: 'CheckCircle', icon: CheckCircle, label: 'علامة صح' },
    { name: 'Users', icon: Users, label: 'مستخدمون' },
    { name: 'DollarSign', icon: DollarSign, label: 'دولار' },
    { name: 'Activity', icon: Activity, label: 'نشاط' },
    { name: 'TrendingUp', icon: TrendingUp, label: 'اتجاه صاعد' },
    { name: 'Award', icon: Award, label: 'جائزة' },
    { name: 'Target', icon: Target, label: 'هدف' },
    { name: 'Star', icon: Star, label: 'نجمة' },
    { name: 'Zap', icon: Zap, label: 'برق' }
  ]

  // الألوان المتاحة
  const availableColors = [
    { name: 'green', label: 'أخضر', bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-600' },
    { name: 'blue', label: 'أزرق', bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-600' },
    { name: 'purple', label: 'بنفسجي', bg: 'bg-purple-50', border: 'border-purple-200', text: 'text-purple-600' },
    { name: 'yellow', label: 'أصفر', bg: 'bg-yellow-50', border: 'border-yellow-200', text: 'text-yellow-600' },
    { name: 'red', label: 'أحمر', bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-600' },
    { name: 'teal', label: 'تركوازي', bg: 'bg-teal-50', border: 'border-teal-200', text: 'text-teal-600' }
  ]

  // تحميل الإنجازات
  const loadAchievements = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('achievements')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true })

      if (error) throw error
      setAchievements(data || [])
    } catch (error) {
      console.error('خطأ في تحميل الإنجازات:', error)
      toast.error('فشل في تحميل الإنجازات')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadAchievements()
  }, [])

  // إضافة إنجاز جديد
  const handleAddAchievement = async () => {
    try {
      const { data, error } = await supabase
        .from('achievements')
        .insert([{
          ...formData,
          sort_order: achievements.length + 1,
          created_by: user.id
        }])
        .select()

      if (error) throw error

      setAchievements([...achievements, data[0]])
      setShowAddModal(false)
      resetForm()
      toast.success('تم إضافة الإنجاز بنجاح')
    } catch (error) {
      console.error('خطأ في إضافة الإنجاز:', error)
      toast.error('فشل في إضافة الإنجاز')
    }
  }

  // تعديل إنجاز
  const handleEditAchievement = async () => {
    try {
      const { data, error } = await supabase
        .from('achievements')
        .update({
          ...formData,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedAchievement.id)
        .select()

      if (error) throw error

      setAchievements(achievements.map(achievement => 
        achievement.id === selectedAchievement.id ? data[0] : achievement
      ))
      setShowEditModal(false)
      setSelectedAchievement(null)
      resetForm()
      toast.success('تم تحديث الإنجاز بنجاح')
    } catch (error) {
      console.error('خطأ في تحديث الإنجاز:', error)
      toast.error('فشل في تحديث الإنجاز')
    }
  }

  // حذف إنجاز
  const handleDeleteAchievement = async (achievementId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الإنجاز؟')) return

    try {
      const { error } = await supabase
        .from('achievements')
        .delete()
        .eq('id', achievementId)

      if (error) throw error

      setAchievements(achievements.filter(achievement => achievement.id !== achievementId))
      toast.success('تم حذف الإنجاز بنجاح')
    } catch (error) {
      console.error('خطأ في حذف الإنجاز:', error)
      toast.error('فشل في حذف الإنجاز')
    }
  }

  // تحريك الإنجاز لأعلى أو أسفل
  const moveAchievement = async (achievementId, direction) => {
    console.log('🔄 moveAchievement called:', { achievementId, direction, achievementsLength: achievements.length })

    const currentIndex = achievements.findIndex(a => a.id === achievementId)
    console.log('📍 Current index:', currentIndex)

    if (currentIndex === -1) {
      console.error('❌ Achievement not found')
      return
    }

    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === achievements.length - 1)
    ) {
      console.log('⚠️ Cannot move - at boundary')
      return
    }

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    console.log('📍 New index:', newIndex)

    const newAchievements = [...achievements]

    // تبديل المواضع
    [newAchievements[currentIndex], newAchievements[newIndex]] =
    [newAchievements[newIndex], newAchievements[currentIndex]]

    try {
      console.log('💾 Updating database...')

      // تحديث sort_order للعنصرين المتبادلين فقط
      const currentAchievement = newAchievements[currentIndex]
      const swappedAchievement = newAchievements[newIndex]

      // تحديث العنصر الأول
      const { error: error1 } = await supabase
        .from('achievements')
        .update({ sort_order: currentIndex + 1 })
        .eq('id', currentAchievement.id)

      if (error1) throw error1

      // تحديث العنصر الثاني
      const { error: error2 } = await supabase
        .from('achievements')
        .update({ sort_order: newIndex + 1 })
        .eq('id', swappedAchievement.id)

      if (error2) throw error2

      // تحديث الحالة المحلية
      currentAchievement.sort_order = currentIndex + 1
      swappedAchievement.sort_order = newIndex + 1

      setAchievements(newAchievements)
      console.log('✅ Achievements reordered successfully')
      toast.success('تم تحديث ترتيب الإنجازات')
    } catch (error) {
      console.error('❌ Error updating order:', error)
      toast.error('فشل في تحديث الترتيب')
      // إعادة تحميل الإنجازات في حالة الخطأ
      loadAchievements()
    }
  }

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      value: '',
      improvement: '',
      icon: 'CheckCircle',
      color: 'green',
      sort_order: 0,
      is_active: true
    })
  }

  // فتح نموذج التعديل
  const openEditModal = (achievement) => {
    setSelectedAchievement(achievement)
    setFormData({
      title: achievement.title || '',
      description: achievement.description || '',
      value: achievement.value || '',
      improvement: achievement.improvement || '',
      icon: achievement.icon || 'CheckCircle',
      color: achievement.color || 'green',
      sort_order: achievement.sort_order || 0,
      is_active: achievement.is_active
    })
    setShowEditModal(true)
  }

  // الحصول على الأيقونة
  const getIcon = (iconName) => {
    const iconData = availableIcons.find(i => i.name === iconName)
    return iconData ? iconData.icon : CheckCircle
  }

  // الحصول على اللون
  const getColorClasses = (colorName) => {
    const colorData = availableColors.find(c => c.name === colorName)
    return colorData || availableColors[0]
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* رأس النافذة */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">إدارة أهم الإنجازات</h2>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => setShowAddModal(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <Plus className="w-4 h-4" />
                <span>إضافة إنجاز</span>
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* قائمة الإنجازات */}
        <div className="p-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-4">جاري تحميل الإنجازات...</p>
            </div>
          ) : achievements.length > 0 ? (
            <div className="space-y-4">
              {achievements.map((achievement, index) => {
                const Icon = getIcon(achievement.icon)
                const colorClasses = getColorClasses(achievement.color)
                
                return (
                  <div
                    key={achievement.id}
                    className={`flex items-start space-x-4 space-x-reverse p-4 ${colorClasses.bg} rounded-lg border ${colorClasses.border}`}
                  >
                    <div className="flex-shrink-0">
                      <Icon className={`w-6 h-6 ${colorClasses.text}`} />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{achievement.title}</h3>
                      <p className="text-sm text-gray-600 mb-1">{achievement.description}</p>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className={`text-sm font-medium ${colorClasses.text}`}>
                          {achievement.value}
                        </span>
                        {achievement.improvement && (
                          <span className="text-xs text-gray-500">
                            {achievement.improvement}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          console.log('⬆️ Up button clicked for achievement:', achievement.id, 'index:', index)
                          moveAchievement(achievement.id, 'up')
                        }}
                        disabled={index === 0}
                        className={`p-1 transition-colors ${
                          index === 0
                            ? 'text-gray-300 cursor-not-allowed'
                            : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded'
                        }`}
                        title="تحريك لأعلى"
                      >
                        <ArrowUp className="w-4 h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          console.log('⬇️ Down button clicked for achievement:', achievement.id, 'index:', index)
                          moveAchievement(achievement.id, 'down')
                        }}
                        disabled={index === achievements.length - 1}
                        className={`p-1 transition-colors ${
                          index === achievements.length - 1
                            ? 'text-gray-300 cursor-not-allowed'
                            : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded'
                        }`}
                        title="تحريك لأسفل"
                      >
                        <ArrowDown className="w-4 h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          openEditModal(achievement)
                        }}
                        className="p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
                        title="تعديل"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteAchievement(achievement.id)
                        }}
                        className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                        title="حذف"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <Award className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد إنجازات</h3>
              <p className="text-gray-500 mb-4">لم يتم إضافة أي إنجازات بعد</p>
              <button
                onClick={() => setShowAddModal(true)}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                إضافة أول إنجاز
              </button>
            </div>
          )}
        </div>

        {/* نموذج إضافة إنجاز */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">إضافة إنجاز جديد</h3>
                  <button
                    onClick={() => {
                      setShowAddModal(false)
                      resetForm()
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">عنوان الإنجاز *</label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: إكمال 15 مشروع"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الوصف *</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="وصف تفصيلي للإنجاز"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">القيمة</label>
                    <input
                      type="text"
                      value={formData.value}
                      onChange={(e) => setFormData({...formData, value: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثال: 15 مشروع"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">التحسن</label>
                    <input
                      type="text"
                      value={formData.improvement}
                      onChange={(e) => setFormData({...formData, improvement: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثال: +25% عن الشهر الماضي"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الأيقونة</label>
                  <div className="grid grid-cols-3 gap-2">
                    {availableIcons.map((iconData) => {
                      const Icon = iconData.icon
                      return (
                        <button
                          key={iconData.name}
                          type="button"
                          onClick={() => setFormData({...formData, icon: iconData.name})}
                          className={`p-3 border rounded-lg flex flex-col items-center space-y-1 transition-colors ${
                            formData.icon === iconData.name
                              ? 'border-blue-500 bg-blue-50 text-blue-600'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <Icon className="w-5 h-5" />
                          <span className="text-xs">{iconData.label}</span>
                        </button>
                      )
                    })}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                  <div className="grid grid-cols-3 gap-2">
                    {availableColors.map((colorData) => (
                      <button
                        key={colorData.name}
                        type="button"
                        onClick={() => setFormData({...formData, color: colorData.name})}
                        className={`p-3 border rounded-lg flex items-center space-x-2 space-x-reverse transition-colors ${
                          formData.color === colorData.name
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <div className={`w-4 h-4 rounded-full ${colorData.bg} border ${colorData.border}`}></div>
                        <span className="text-sm">{colorData.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-3 space-x-reverse">
                <button
                  onClick={() => {
                    setShowAddModal(false)
                    resetForm()
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleAddAchievement}
                  disabled={!formData.title.trim() || !formData.description.trim()}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse"
                >
                  <Save className="w-4 h-4" />
                  <span>حفظ</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* نموذج تعديل إنجاز */}
        {showEditModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">تعديل الإنجاز</h3>
                  <button
                    onClick={() => {
                      setShowEditModal(false)
                      setSelectedAchievement(null)
                      resetForm()
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">عنوان الإنجاز *</label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: إكمال 15 مشروع"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الوصف *</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="وصف تفصيلي للإنجاز"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">القيمة</label>
                    <input
                      type="text"
                      value={formData.value}
                      onChange={(e) => setFormData({...formData, value: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثال: 15 مشروع"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">التحسن</label>
                    <input
                      type="text"
                      value={formData.improvement}
                      onChange={(e) => setFormData({...formData, improvement: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثال: +25% عن الشهر الماضي"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الأيقونة</label>
                  <div className="grid grid-cols-3 gap-2">
                    {availableIcons.map((iconData) => {
                      const Icon = iconData.icon
                      return (
                        <button
                          key={iconData.name}
                          type="button"
                          onClick={() => setFormData({...formData, icon: iconData.name})}
                          className={`p-3 border rounded-lg flex flex-col items-center space-y-1 transition-colors ${
                            formData.icon === iconData.name
                              ? 'border-blue-500 bg-blue-50 text-blue-600'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <Icon className="w-5 h-5" />
                          <span className="text-xs">{iconData.label}</span>
                        </button>
                      )
                    })}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                  <div className="grid grid-cols-3 gap-2">
                    {availableColors.map((colorData) => (
                      <button
                        key={colorData.name}
                        type="button"
                        onClick={() => setFormData({...formData, color: colorData.name})}
                        className={`p-3 border rounded-lg flex items-center space-x-2 space-x-reverse transition-colors ${
                          formData.color === colorData.name
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <div className={`w-4 h-4 rounded-full ${colorData.bg} border ${colorData.border}`}></div>
                        <span className="text-sm">{colorData.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-3 space-x-reverse">
                <button
                  onClick={() => {
                    setShowEditModal(false)
                    setSelectedAchievement(null)
                    resetForm()
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleEditAchievement}
                  disabled={!formData.title.trim() || !formData.description.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse"
                >
                  <Save className="w-4 h-4" />
                  <span>حفظ التغييرات</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default AchievementsManager
