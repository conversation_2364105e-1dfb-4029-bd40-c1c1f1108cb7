import React, { useState } from 'react'
import { X, Send, FileText, DollarSign, Clock, AlertCircle } from 'lucide-react'
import { supabase } from '../../lib/supabase'
import useStore from '../../store/useStore'
import toast from 'react-hot-toast'

const ServiceRequestModal = ({ service, serviceItem, isOpen, onClose, onSuccess }) => {
  const { user } = useStore()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    requirements: '',
    budget: '',
    timeline: '',
    priority: 'normal'
  })

  // إرسال طلب الخدمة
  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!formData.title || !formData.description) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    try {
      setLoading(true)

      const requestData = {
        user_id: user.id,
        service_id: service.id,
        service_item_id: serviceItem?.id || null,
        title: formData.title,
        description: formData.description,
        requirements: formData.requirements,
        budget: parseFloat(formData.budget) || null,
        timeline: formData.timeline,
        priority: formData.priority,
        status: 'pending'
      }

      const { error } = await supabase
        .from('service_requests')
        .insert([requestData])

      if (error) throw error

      toast.success('تم إرسال طلب الخدمة بنجاح')
      
      // إعادة تعيين النموذج
      setFormData({
        title: '',
        description: '',
        requirements: '',
        budget: '',
        timeline: '',
        priority: 'normal'
      })
      
      onSuccess && onSuccess()
      onClose()
    } catch (error) {
      console.error('خطأ في إرسال طلب الخدمة:', error)
      toast.error('فشل في إرسال طلب الخدمة')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* رأس النموذج */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900">طلب خدمة جديدة</h2>
            <p className="text-gray-600 mt-1">
              {service.title} {serviceItem && `- ${serviceItem.title}`}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* معلومات الخدمة */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3 space-x-reverse">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-blue-900">{service.title}</h3>
              <p className="text-sm text-blue-700 mt-1">{service.description}</p>
              <div className="flex items-center space-x-4 space-x-reverse mt-2 text-sm text-blue-600">
                {service.base_price && (
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <DollarSign className="w-4 h-4" />
                    <span>من {service.base_price.toLocaleString()} ريال</span>
                  </div>
                )}
                {service.duration_weeks && (
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <Clock className="w-4 h-4" />
                    <span>{service.duration_weeks} أسابيع</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* نموذج الطلب */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* عنوان الطلب */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              عنوان الطلب *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({...formData, title: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="مثال: تطوير موقع إلكتروني لشركتي"
              required
            />
          </div>

          {/* وصف الطلب */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              وصف تفصيلي للطلب *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="اشرح بالتفصيل ما تحتاجه من هذه الخدمة..."
              required
            />
          </div>

          {/* المتطلبات الخاصة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              المتطلبات الخاصة
            </label>
            <textarea
              value={formData.requirements}
              onChange={(e) => setFormData({...formData, requirements: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="أي متطلبات خاصة أو تفاصيل إضافية..."
            />
          </div>

          {/* الميزانية والجدول الزمني */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الميزانية المتوقعة (ريال)
              </label>
              <input
                type="number"
                value={formData.budget}
                onChange={(e) => setFormData({...formData, budget: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="10000"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الجدول الزمني المطلوب
              </label>
              <select
                value={formData.timeline}
                onChange={(e) => setFormData({...formData, timeline: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">اختر الجدول الزمني</option>
                <option value="أسبوع واحد">أسبوع واحد</option>
                <option value="أسبوعين">أسبوعين</option>
                <option value="شهر واحد">شهر واحد</option>
                <option value="شهرين">شهرين</option>
                <option value="3 أشهر">3 أشهر</option>
                <option value="أكثر من 3 أشهر">أكثر من 3 أشهر</option>
              </select>
            </div>
          </div>

          {/* الأولوية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              أولوية الطلب
            </label>
            <div className="flex space-x-4 space-x-reverse">
              {[
                { value: 'low', label: 'منخفضة', color: 'green' },
                { value: 'normal', label: 'عادية', color: 'blue' },
                { value: 'urgent', label: 'عاجلة', color: 'red' }
              ].map((priority) => (
                <label key={priority.value} className="flex items-center">
                  <input
                    type="radio"
                    name="priority"
                    value={priority.value}
                    checked={formData.priority === priority.value}
                    onChange={(e) => setFormData({...formData, priority: e.target.value})}
                    className={`mr-2 text-${priority.color}-600 focus:ring-${priority.color}-500`}
                  />
                  <span className="text-sm text-gray-700">{priority.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* تنبيه */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3 space-x-reverse">
              <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">ملاحظة مهمة:</p>
                <p>
                  سيتم مراجعة طلبك من قبل فريقنا وسنتواصل معك خلال 24 ساعة لتأكيد التفاصيل والتكلفة النهائية.
                </p>
              </div>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex items-center justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Send className="w-4 h-4" />
              )}
              <span>{loading ? 'جاري الإرسال...' : 'إرسال الطلب'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ServiceRequestModal
