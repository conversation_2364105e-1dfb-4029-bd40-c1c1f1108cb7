import { expect, afterEach, vi } from 'vitest'
import { cleanup } from '@testing-library/react'
import * as matchers from '@testing-library/jest-dom/matchers'

// إضافة matchers مخصصة
expect.extend(matchers)

// تنظيف بعد كل اختبار
afterEach(() => {
  cleanup()
})

// Mock للـ localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
vi.stubGlobal('localStorage', localStorageMock)

// Mock للـ sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
vi.stubGlobal('sessionStorage', sessionStorageMock)

// Mock للـ window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock للـ IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock للـ ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock للـ fetch
global.fetch = vi.fn()

// إعداد متغيرات البيئة للاختبارات
vi.stubEnv('VITE_SUPABASE_URL', 'https://test.supabase.co')
vi.stubEnv('VITE_SUPABASE_ANON_KEY', 'test-anon-key')
vi.stubEnv('VITE_APP_NAME', 'نماء الاحترافية - اختبار')
vi.stubEnv('VITE_APP_ENVIRONMENT', 'test')

// Mock لـ Supabase
vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(() => ({
    auth: {
      signInWithPassword: vi.fn(),
      signOut: vi.fn(),
      getUser: vi.fn(),
      onAuthStateChange: vi.fn(() => ({ data: { subscription: { unsubscribe: vi.fn() } } })),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
    })),
    storage: {
      from: vi.fn(() => ({
        upload: vi.fn(),
        createSignedUrl: vi.fn(),
      })),
    },
    channel: vi.fn(() => ({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
    })),
    removeChannel: vi.fn(),
  })),
}))

// Mock لـ React Hot Toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
  },
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
  },
  Toaster: vi.fn(() => null),
}))

// دوال مساعدة للاختبارات
export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'مستخدم تجريبي',
  organization_name: 'منظمة تجريبية',
  membership_level: 'basic',
  ...overrides,
})

export const createMockRequest = (overrides = {}) => ({
  id: 'REQ-001',
  client_id: 'test-user-id',
  service_name: 'خدمة تجريبية',
  status: 'pending',
  priority: 'medium',
  progress: 50,
  current_step: 'مرحلة تجريبية',
  created_at: new Date().toISOString(),
  ...overrides,
})

export const createMockNotification = (overrides = {}) => ({
  id: 'notif-001',
  user_id: 'test-user-id',
  title: 'إشعار تجريبي',
  message: 'هذا إشعار للاختبار',
  type: 'info',
  is_read: false,
  created_at: new Date().toISOString(),
  ...overrides,
})
