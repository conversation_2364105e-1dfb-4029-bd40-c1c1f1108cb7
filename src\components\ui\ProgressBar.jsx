import React from 'react'

const ProgressBar = ({
  progress,
  showLabel = true,
  height = 'h-2',
  animated = true,
  color = 'blue',
  className = '',
  label = null
}) => {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-green-600',
    orange: 'from-orange-500 to-orange-600',
    red: 'from-red-500 to-red-600',
    purple: 'from-purple-500 to-purple-600',
    teal: 'from-teal-500 to-teal-600'
  }

  const textColorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    orange: 'text-orange-600',
    red: 'text-red-600',
    purple: 'text-purple-600',
    teal: 'text-teal-600'
  }

  // تحديد اللون بناءً على النسبة
  const getColorByProgress = (progress) => {
    if (progress >= 80) return 'green'
    if (progress >= 60) return 'blue'
    if (progress >= 40) return 'orange'
    return 'red'
  }

  const finalColor = color === 'auto' ? getColorByProgress(progress) : color

  return (
    <div className={`w-full ${className}`}>
      {showLabel && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">
            {label || 'التقدم'}
          </span>
          <span className={`text-sm font-bold ${textColorClasses[finalColor]}`}>
            {progress}%
          </span>
        </div>
      )}

      <div className={`w-full bg-gray-200 rounded-full ${height} overflow-hidden shadow-inner`}>
        <div
          className={`bg-gradient-to-r ${colorClasses[finalColor]} ${height} rounded-full transition-all duration-700 ease-out relative ${
            animated ? 'animate-pulse' : ''
          }`}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        >
          {animated && progress > 0 && (
            <div className="absolute inset-0 bg-white opacity-20 animate-slide rounded-full"></div>
          )}

          {/* نقطة متحركة في نهاية الشريط */}
          {progress > 0 && progress < 100 && (
            <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2">
              <div className={`w-2 h-2 bg-white rounded-full shadow-lg ${animated ? 'animate-pulse' : ''}`}></div>
            </div>
          )}
        </div>
      </div>

      {/* مؤشرات المراحل */}
      {progress > 0 && (
        <div className="flex justify-between mt-1 text-xs text-gray-500">
          <span className={progress >= 25 ? textColorClasses[finalColor] : ''}>25%</span>
          <span className={progress >= 50 ? textColorClasses[finalColor] : ''}>50%</span>
          <span className={progress >= 75 ? textColorClasses[finalColor] : ''}>75%</span>
          <span className={progress >= 100 ? textColorClasses[finalColor] : ''}>100%</span>
        </div>
      )}
    </div>
  )
}

export default ProgressBar
