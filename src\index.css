@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

:root {
  /* Apple-inspired colors */
  --color-primary: #007AFF;
  --color-primary-dark: #0056CC;
  --color-secondary: #5856D6;
  --color-success: #34C759;
  --color-warning: #FF9500;
  --color-error: #FF3B30;
  --color-gray-50: #FAFAFA;
  --color-gray-100: #F5F5F7;
  --color-gray-200: #E5E5E7;
  --color-gray-300: #D2D2D7;
  --color-gray-400: #AEAEB2;
  --color-gray-500: #8E8E93;
  --color-gray-600: #636366;
  --color-gray-700: #48484A;
  --color-gray-800: #2C2C2E;
  --color-gray-900: #1C1C1E;
  
  /* Notion-inspired spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.5rem;
  --spacing-2xl: 2rem;
  
  /* Apple-style border radius */
  --radius-xs: 0.25rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Subtle shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* {
  box-sizing: border-box;
}

html,
:host {
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  background-color: var(--color-gray-50);
  color: var(--color-gray-900);
  font-size: 14px;
  line-height: 1.6;
  font-weight: 400;
}

/* Apple-style scrollbars */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* Clean focus states */
*:focus {
  outline: none;
}

*:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary);
  border-radius: var(--radius-lg);
}

button:focus {
  outline: none;
  box-shadow: none;
}

button:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary);
}

/* Button styles - Apple inspired */
.btn {
  @apply inline-flex items-center justify-center text-sm font-medium transition-all duration-200;
  @apply focus:outline-none;
  border: none;
  cursor: pointer;
  border-radius: var(--radius-lg);
  padding: 0.5rem 1rem;
  font-weight: 500;
}

.btn:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 0.9375rem;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-primary:hover {
  background: var(--color-primary-dark);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-200);
}

.btn-secondary:hover {
  background: var(--color-gray-200);
  border-color: var(--color-gray-300);
}

.btn-ghost {
  background: transparent;
  color: var(--color-gray-600);
}

.btn-ghost:hover {
  background: var(--color-gray-100);
}

/* Legacy button support */
.btn-primary {
  @apply bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all duration-200 font-medium;
}

/* Card styles - Notion inspired */
.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  min-height: 200px;
}

.card-hover {
  transition: all 0.2s ease;
}

.card-hover:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-gray-300);
  transform: translateY(-2px);
}

.card-hover:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Input styles */
.input {
  width: 100%;
  padding: 0.75rem;
  font-size: 0.875rem;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  background: white;
  transition: all 0.2s ease;
}

.input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.input::placeholder {
  color: var(--color-gray-400);
}

/* Typography - Apple style */
.text-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900);
  line-height: 1.4;
}

.text-subtitle {
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--color-gray-600);
  line-height: 1.4;
}

.text-body {
  font-size: 0.875rem;
  color: var(--color-gray-700);
  line-height: 1.5;
}

.text-caption {
  font-size: 0.8125rem;
  color: var(--color-gray-500);
  line-height: 1.4;
}

.text-large {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900);
  line-height: 1.3;
}

/* Status badges - Apple style */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-2xl);
  font-size: 0.8125rem;
  font-weight: 500;
  line-height: 1.2;
}

.badge-success {
  background: rgba(52, 199, 89, 0.1);
  color: var(--color-success);
}

.badge-warning {
  background: rgba(255, 149, 0, 0.1);
  color: var(--color-warning);
}

.badge-error {
  background: rgba(255, 59, 48, 0.1);
  color: var(--color-error);
}

.badge-primary {
  background: rgba(0, 122, 255, 0.1);
  color: var(--color-primary);
}

.badge-gray {
  background: var(--color-gray-100);
  color: var(--color-gray-600);
}

/* Animations - Subtle and smooth */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Dividers */
.divider {
  height: 1px;
  background: var(--color-gray-200);
  border: none;
  margin: var(--spacing-lg) 0;
}

/* Progress bars */
.progress {
  width: 100%;
  height: 0.5rem;
  background: var(--color-gray-200);
  border-radius: var(--radius-2xl);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--radius-2xl);
  transition: width 0.3s ease;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100% !important;
  }
  
  .mobile-stack {
    flex-direction: column !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}
