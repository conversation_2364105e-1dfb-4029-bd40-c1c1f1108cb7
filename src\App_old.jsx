import React, { useEffect, useState } from 'react'
import { Toaster } from 'react-hot-toast'
import useStore from './store/useStore'
import LoginPage from './components/auth/LoginPage'
import Dashboard from './components/dashboard/Dashboard'
import LoadingSpinner from './components/ui/LoadingSpinner'

// مكون مؤقت للتطوير (سيتم حذفه)
const OldDashboard = () => {
  const { user, signOut } = useStore()
  
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                مرحباً بك، {user?.name || user?.email}
              </h1>
              <p className="text-gray-600 mt-2">
                نظام إدارة العملاء - نماء الاحترافية
              </p>
            </div>
            <button
              onClick={signOut}
              className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              تسجيل الخروج
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">3</div>
              <div className="text-blue-800">الطلبات النشطة</div>
            </div>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">8</div>
              <div className="text-green-800">المشاريع المكتملة</div>
            </div>
            
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">2</div>
              <div className="text-orange-800">إجراءات مطلوبة</div>
            </div>
          </div>
          
          <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-teal-50 rounded-lg border border-blue-200">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">
              🚀 النظام قيد التطوير
            </h2>
            <p className="text-blue-800 leading-relaxed">
              نحن نعمل على تطوير النظام الكامل مع جميع الميزات المتقدمة. 
              سيتم إضافة المزيد من الوظائف قريباً بإذن الله.
            </p>
            
            <div className="mt-4 space-y-2">
              <div className="flex items-center space-x-2 space-x-reverse text-sm text-blue-700">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span>تسجيل الدخول والمصادقة ✅</span>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse text-sm text-blue-700">
                <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                <span>لوحة التحكم الرئيسية 🔄</span>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse text-sm text-blue-700">
                <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                <span>إدارة الطلبات 🔄</span>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse text-sm text-blue-700">
                <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                <span>نظام الإشعارات 🔄</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function App() {
  const { 
    isAuthenticated, 
    isLoading, 
    checkAuth, 
    user,
    startRealtimeSubscriptions,
    stopRealtimeSubscriptions 
  } = useStore()
  
  const [initializing, setInitializing] = useState(true)

  useEffect(() => {
    let mounted = true

    // التحقق من حالة المصادقة عند بدء التطبيق
    const initializeAuth = async () => {
      try {
        console.log('Initializing auth...')
        const result = await checkAuth()
        console.log('Auth check result:', result)

        // إذا كان المستخدم مصادق عليه، أنهي التحميل
        if (result && mounted) {
          console.log('User is authenticated, ending initialization')
          setInitializing(false)
          return
        }

        // إذا لم يكن مصادق عليه، انتظر قليلاً ثم أنهي التحميل
        if (mounted) {
          setTimeout(() => {
            console.log('No authentication found, ending initialization')
            setInitializing(false)
          }, 1000)
        }
      } catch (error) {
        console.error('خطأ في التحقق من المصادقة:', error)
        if (mounted) {
          setInitializing(false)
        }
      }
    }

    initializeAuth()

    // الاستماع لتغييرات حالة المصادقة
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email)

      if (event === 'SIGNED_IN' && session && mounted) {
        console.log('Processing SIGNED_IN event...')
        const result = await checkAuth()
        console.log('checkAuth result after SIGNED_IN:', result)
        if (result) {
          setInitializing(false) // إنهاء التحميل عند نجاح المصادقة
        }
        startRealtimeSubscriptions()
      } else if (event === 'SIGNED_OUT' && mounted) {
        console.log('Processing SIGNED_OUT event...')
        stopRealtimeSubscriptions()
        useStore.setState({
          user: null,
          isAuthenticated: false,
          session: null,
          orders: [],
          notifications: []
        })
        setInitializing(false)
      }
    })

    // تنظيف الاشتراك عند إلغاء تحميل المكون
    return () => {
      mounted = false
      subscription?.unsubscribe()
    }
  }, []) // إزالة التبعيات لمنع الدورة اللانهائية

  // إضافة timeout للتحميل
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (initializing) {
        console.log('Forcing initialization to complete after 3 seconds')
        setInitializing(false)
      }
    }, 3000) // 3 ثوان كحد أقصى

    return () => clearTimeout(timeout)
  }, [initializing])

  // شاشة التحميل الأولية
  if (initializing) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-teal-50 flex items-center justify-center">
        <ForceSkip onSkip={() => setInitializing(false)} />
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-900 to-teal-500 rounded-2xl shadow-2xl mb-6 animate-pulse">
            <span className="text-white text-3xl font-bold">ن</span>
          </div>
          <LoadingSpinner size="large" text="جاري تحميل النظام..." />
          <div className="mt-4 space-y-2">
            <button
              onClick={() => setInitializing(false)}
              className="block mx-auto text-blue-600 hover:text-blue-800 text-sm underline"
            >
              تخطي التحميل
            </button>
            <p className="text-xs text-gray-500">
              أو انقر على "دخول مباشر" في أعلى اليمين
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="App">
      {/* إعدادات Toast */}
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#fff',
            color: '#333',
            fontFamily: 'Cairo, sans-serif',
            fontSize: '14px',
            borderRadius: '12px',
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e5e7eb',
            padding: '16px',
            maxWidth: '400px'
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#fff',
            },
            style: {
              border: '1px solid #10b981',
            }
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
            style: {
              border: '1px solid #ef4444',
            }
          },
          loading: {
            iconTheme: {
              primary: '#3b82f6',
              secondary: '#fff',
            },
            style: {
              border: '1px solid #3b82f6',
            }
          }
        }}
      />

      {/* المحتوى الرئيسي */}
      {isLoading ? (
        <LoadingSpinner fullScreen text="جاري المعالجة..." />
      ) : isAuthenticated ? (
        <Dashboard />
      ) : (
        <LoginPage />
      )}

      {/* مكون التشخيص (في وضع التطوير فقط) */}
      {process.env.NODE_ENV === 'development' && <AuthDebug />}
    </div>
  )
}

export default App
