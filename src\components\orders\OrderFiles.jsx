import React, { useState } from 'react'
import { 
  Upload, 
  Download, 
  Eye, 
  Trash2,
  FileText,
  Image,
  Video,
  Archive,
  File,
  Search,
  Filter,
  MoreHorizontal,
  Calendar,
  User
} from 'lucide-react'

const OrderFiles = ({ orderId }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [viewMode, setViewMode] = useState('list') // list or grid

  // بيانات تجريبية للملفات
  const files = [
    {
      id: 1,
      filename: 'متطلبات_المشروع.pdf',
      original_filename: 'متطلبات المشروع.pdf',
      file_size: 2048576, // 2MB
      file_type: 'pdf',
      category: 'document',
      description: 'وثيقة متطلبات المشروع الأساسية',
      uploaded_by: 'أحمد محمد',
      created_at: '2024-06-15T10:00:00Z',
      download_count: 5
    },
    {
      id: 2,
      filename: 'تصميم_الواجهة.fig',
      original_filename: 'تصميم الواجهة.fig',
      file_size: 15728640, // 15MB
      file_type: 'fig',
      category: 'image',
      description: 'ملف تصميم واجهة المستخدم',
      uploaded_by: 'فاطمة علي',
      created_at: '2024-06-18T14:30:00Z',
      download_count: 12
    },
    {
      id: 3,
      filename: 'عرض_تقديمي.pptx',
      original_filename: 'عرض تقديمي.pptx',
      file_size: 8388608, // 8MB
      file_type: 'pptx',
      category: 'document',
      description: 'عرض تقديمي للمشروع',
      uploaded_by: 'محمد أحمد',
      created_at: '2024-06-20T09:15:00Z',
      download_count: 3
    },
    {
      id: 4,
      filename: 'فيديو_توضيحي.mp4',
      original_filename: 'فيديو توضيحي.mp4',
      file_size: 52428800, // 50MB
      file_type: 'mp4',
      category: 'video',
      description: 'فيديو توضيحي لطريقة استخدام النظام',
      uploaded_by: 'سارة محمد',
      created_at: '2024-06-22T16:45:00Z',
      download_count: 8
    },
    {
      id: 5,
      filename: 'ملفات_المصدر.zip',
      original_filename: 'ملفات المصدر.zip',
      file_size: 104857600, // 100MB
      file_type: 'zip',
      category: 'archive',
      description: 'أرشيف يحتوي على ملفات المصدر',
      uploaded_by: 'فريق التطوير',
      created_at: '2024-06-25T11:20:00Z',
      download_count: 2
    }
  ]

  // دالة لتحديد أيقونة نوع الملف
  const getFileIcon = (fileType, category) => {
    switch (category) {
      case 'document':
        return <FileText className="w-8 h-8 text-blue-500" />
      case 'image':
        return <Image className="w-8 h-8 text-green-500" />
      case 'video':
        return <Video className="w-8 h-8 text-red-500" />
      case 'archive':
        return <Archive className="w-8 h-8 text-purple-500" />
      default:
        return <File className="w-8 h-8 text-gray-500" />
    }
  }

  // دالة لتنسيق حجم الملف
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // دالة لتنسيق التاريخ
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // فلترة الملفات
  const filteredFiles = files.filter(file => {
    const matchesSearch = file.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         file.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterType === 'all' || file.category === filterType
    return matchesSearch && matchesFilter
  })

  // حساب الإحصائيات
  const stats = {
    total: files.length,
    totalSize: files.reduce((sum, file) => sum + file.file_size, 0),
    documents: files.filter(f => f.category === 'document').length,
    images: files.filter(f => f.category === 'image').length,
    videos: files.filter(f => f.category === 'video').length,
    archives: files.filter(f => f.category === 'archive').length
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات الملفات */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-sm text-gray-600">إجمالي الملفات</div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-blue-600">{stats.documents}</div>
          <div className="text-sm text-gray-600">مستندات</div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-green-600">{stats.images}</div>
          <div className="text-sm text-gray-600">صور</div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-lg font-bold text-purple-600">{formatFileSize(stats.totalSize)}</div>
          <div className="text-sm text-gray-600">الحجم الإجمالي</div>
        </div>
      </div>

      {/* أدوات التحكم */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          {/* البحث والفلترة */}
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1">
              <Search className="absolute right-3 top-3 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في الملفات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="w-5 h-5 text-gray-400" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الأنواع</option>
                <option value="document">مستندات</option>
                <option value="image">صور</option>
                <option value="video">فيديو</option>
                <option value="archive">أرشيف</option>
              </select>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex items-center gap-2">
            <button className="btn-primary flex items-center space-x-2 space-x-reverse">
              <Upload className="w-4 h-4" />
              <span>رفع ملف</span>
            </button>
          </div>
        </div>
      </div>

      {/* قائمة الملفات */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            ملفات المشروع ({filteredFiles.length})
          </h2>
        </div>

        {filteredFiles.length === 0 ? (
          <div className="text-center py-12">
            <File className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا توجد ملفات
            </h3>
            <p className="text-gray-500">
              لم يتم العثور على ملفات بالمعايير المحددة
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredFiles.map((file) => (
              <div key={file.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4 space-x-reverse">
                  {/* أيقونة الملف */}
                  <div className="flex-shrink-0">
                    {getFileIcon(file.file_type, file.category)}
                  </div>

                  {/* معلومات الملف */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {file.filename}
                      </h3>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                          <Download className="w-4 h-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                          <Trash2 className="w-4 h-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                          <MoreHorizontal className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <p className="text-gray-600 mt-1 mb-2">
                      {file.description}
                    </p>

                    <div className="flex items-center space-x-6 space-x-reverse text-sm text-gray-500">
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span>الحجم:</span>
                        <span className="font-medium">{formatFileSize(file.file_size)}</span>
                      </div>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <User className="w-4 h-4" />
                        <span>{file.uploaded_by}</span>
                      </div>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(file.created_at)}</span>
                      </div>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Download className="w-4 h-4" />
                        <span>{file.download_count} تحميل</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default OrderFiles
