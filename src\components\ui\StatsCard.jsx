import React from 'react'
import { TrendingUp, TrendingDown } from 'lucide-react'

const StatsCard = ({ 
  title, 
  value, 
  icon: Icon, 
  color = 'blue', 
  trend, 
  trendDirection = 'up',
  description,
  onClick 
}) => {
  // تحديد ألوان البطاقة حسب النوع
  const getColorClasses = (color) => {
    switch (color) {
      case 'blue':
        return {
          bg: 'bg-gradient-to-br from-blue-50 to-blue-100/50',
          icon: 'bg-blue-500/10 text-blue-600 border border-blue-200/50',
          border: 'border-blue-200/60',
          text: 'text-blue-600',
          trend: 'text-blue-600'
        }
      case 'green':
        return {
          bg: 'bg-gradient-to-br from-green-50 to-green-100/50',
          icon: 'bg-green-500/10 text-green-600 border border-green-200/50',
          border: 'border-green-200/60',
          text: 'text-green-600',
          trend: 'text-green-600'
        }
      case 'yellow':
        return {
          bg: 'bg-gradient-to-br from-yellow-50 to-yellow-100/50',
          icon: 'bg-yellow-500/10 text-yellow-600 border border-yellow-200/50',
          border: 'border-yellow-200/60',
          text: 'text-yellow-600',
          trend: 'text-yellow-600'
        }
      case 'red':
        return {
          bg: 'bg-gradient-to-br from-red-50 to-red-100/50',
          icon: 'bg-red-500/10 text-red-600 border border-red-200/50',
          border: 'border-red-200/60',
          text: 'text-red-600',
          trend: 'text-red-600'
        }
      case 'purple':
        return {
          bg: 'bg-gradient-to-br from-purple-50 to-purple-100/50',
          icon: 'bg-purple-500/10 text-purple-600 border border-purple-200/50',
          border: 'border-purple-200/60',
          text: 'text-purple-600',
          trend: 'text-purple-600'
        }
      case 'teal':
        return {
          bg: 'bg-gradient-to-br from-teal-50 to-teal-100/50',
          icon: 'bg-teal-500/10 text-teal-600 border border-teal-200/50',
          border: 'border-teal-200/60',
          text: 'text-teal-600',
          trend: 'text-teal-600'
        }
      default:
        return {
          bg: 'bg-gradient-to-br from-gray-50 to-gray-100/50',
          icon: 'bg-gray-500/10 text-gray-600 border border-gray-200/50',
          border: 'border-gray-200/60',
          text: 'text-gray-600',
          trend: 'text-gray-600'
        }
    }
  }

  const colors = getColorClasses(color)

  return (
    <div
      className={`${colors.bg} rounded-2xl p-6 border ${colors.border} hover:shadow-xl transition-all duration-300 h-full flex flex-col backdrop-blur-sm ${
        onClick ? 'cursor-pointer hover:scale-[1.02] hover:-translate-y-1' : ''
      }`}
      onClick={onClick}
    >
      {/* رأس البطاقة */}
      <div className="flex items-start justify-between mb-6">
        {/* العنوان */}
        <div className="flex-1 min-w-0 pr-3">
          <p className="text-sm font-semibold text-gray-700 leading-relaxed">
            {title}
          </p>
        </div>

        {/* الأيقونة */}
        <div className={`w-12 h-12 ${colors.icon} rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm`}>
          <Icon className="w-6 h-6" />
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="flex-1 flex flex-col justify-between">
        {/* القيمة */}
        <div className="mb-4">
          <p className="text-3xl font-bold text-gray-900 leading-tight mb-2">
            {value}
          </p>

          {/* الوصف */}
          {description && (
            <p className="text-sm text-gray-600 leading-relaxed">
              {description}
            </p>
          )}
        </div>

        {/* الاتجاه */}
        {trend && (
          <div className="flex items-center space-x-2 space-x-reverse">
            {trendDirection === 'up' ? (
              <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
            ) : (
              <TrendingDown className="w-4 h-4 text-red-500 flex-shrink-0" />
            )}
            <span className={`text-sm font-medium ${
              trendDirection === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {trend}
            </span>
          </div>
        )}
      </div>
    </div>
  )
}

export default StatsCard
