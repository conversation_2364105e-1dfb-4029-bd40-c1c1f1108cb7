import React, { useState, useEffect } from 'react'
import { 
  FileText, 
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  Clock,
  ChevronRight,
  Settings,
  Activity,
  AlertCircle,
  CheckCircle,
  XCircle,
  Star,
  BarChart3,
  Grid,
  List
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const CustomerServiceItemsManagement = ({ customer, service, onServiceItemSelect, selectedServiceItem }) => {
  const [serviceItems, setServiceItems] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all') // all, active, inactive
  const [serviceItemStats, setServiceItemStats] = useState({})

  // جلب عناصر الخدمة من قاعدة البيانات
  const loadServiceItems = async () => {
    if (!service) return
    
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('service_items')
        .select('*')
        .eq('service_id', service.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setServiceItems(data || [])
      
      // جلب إحصائيات كل عنصر للعميل
      if (customer && data) {
        await loadServiceItemStats(data, customer.id)
      }
    } catch (error) {
      console.error('Error loading service items:', error)
      toast.error('حدث خطأ في تحميل عناصر الخدمة')
    } finally {
      setLoading(false)
    }
  }

  // جلب إحصائيات عناصر الخدمة للعميل
  const loadServiceItemStats = async (itemsList, customerId) => {
    try {
      const stats = {}
      
      for (const item of itemsList) {
        // جلب عدد المطالب
        const { count: requirementsCount } = await supabase
          .from('service_requirements')
          .select('*', { count: 'exact', head: true })
          .eq('service_item_id', item.id)
          .eq('customer_id', customerId)

        // جلب عدد الأحداث
        const { count: actionsCount } = await supabase
          .from('service_actions')
          .select('*', { count: 'exact', head: true })
          .eq('service_item_id', item.id)
          .eq('customer_id', customerId)

        // جلب عدد مراحل الجدول الزمني
        const { count: timelineCount } = await supabase
          .from('service_timeline')
          .select('*', { count: 'exact', head: true })
          .eq('service_item_id', item.id)
          .eq('customer_id', customerId)

        stats[item.id] = {
          requirements: requirementsCount || 0,
          actions: actionsCount || 0,
          timeline: timelineCount || 0
        }
      }
      
      setServiceItemStats(stats)
    } catch (error) {
      console.error('Error loading service item stats:', error)
    }
  }

  useEffect(() => {
    if (service) {
      loadServiceItems()
    }
  }, [service, customer])

  // فلترة عناصر الخدمة
  const filteredServiceItems = serviceItems.filter(item => {
    const matchesSearch = 
      item.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = 
      filterStatus === 'all' || 
      (filterStatus === 'active' && item.is_active) ||
      (filterStatus === 'inactive' && !item.is_active)

    return matchesSearch && matchesStatus
  })

  if (!customer || !service) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-full flex items-center justify-center">
        <div className="text-center p-4">
          <FileText className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <h4 className="text-sm font-medium text-gray-900 mb-1">اختر خدمة</h4>
          <p className="text-gray-500 text-xs">
            اختر خدمة لعرض عناصرها
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-full flex flex-col">
      {/* Header مضغوط */}
      <div className="p-3 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-base font-semibold text-gray-900">
              عناصر الخدمة
            </h3>
            <p className="text-xs text-gray-500 truncate">
              {service.title}
            </p>
          </div>
        </div>

        {/* البحث فقط */}
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
          <input
            type="text"
            placeholder="البحث..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pr-8 pl-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-xs"
          />
        </div>
      </div>

      {/* قائمة عناصر الخدمة */}
      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-4">جاري تحميل عناصر الخدمة...</p>
          </div>
        ) : filteredServiceItems.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {filteredServiceItems.map((item) => {
              const stats = serviceItemStats[item.id] || { requirements: 0, actions: 0, timeline: 0 }
              const hasData = stats.requirements > 0 || stats.actions > 0 || stats.timeline > 0
              
              return (
                <div
                  key={item.id}
                  className={`p-4 hover:bg-gray-50 cursor-pointer transition-all duration-200 ${
                    selectedServiceItem?.id === item.id ? 'bg-blue-50 border-r-4 border-blue-500' : ''
                  }`}
                  onClick={() => onServiceItemSelect && onServiceItemSelect(item)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      {/* معلومات العنصر مضغوطة */}
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <FileText className="w-4 h-4 text-blue-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-xs font-semibold text-gray-900 truncate">
                            {item.title}
                          </h4>
                          <div className="flex items-center space-x-3 space-x-reverse text-xs text-gray-500">
                            <span>{stats.requirements + stats.actions + stats.timeline} عنصر</span>
                            {hasData && <span className="text-blue-600">• بيانات</span>}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* حالة وإجراءات */}
                    <div className="flex items-center space-x-1 space-x-reverse">
                      {/* مؤشر وجود البيانات */}
                      <div className={`w-2 h-2 rounded-full ${
                        hasData ? 'bg-blue-500' : 'bg-gray-300'
                      }`} title={hasData ? 'يحتوي على بيانات' : 'لا توجد بيانات'}></div>

                      {/* زر الاختيار */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          onServiceItemSelect && onServiceItemSelect(item)
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded transition-colors"
                        title="اختيار العنصر"
                      >
                        <ChevronRight className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="p-8 text-center">
            <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">لا توجد عناصر</h4>
            <p className="text-gray-500 text-sm">
              {searchTerm ? 'لا توجد نتائج تطابق البحث' : 'لم يتم إضافة أي عناصر لهذه الخدمة بعد'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default CustomerServiceItemsManagement
