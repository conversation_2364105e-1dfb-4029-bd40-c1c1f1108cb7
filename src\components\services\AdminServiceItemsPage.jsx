import React, { useState, useEffect } from 'react'
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Grid,
  List,
  DollarSign,
  Clock,
  Eye,
  Save,
  X,
  AlertCircle
} from 'lucide-react'
import PageHeader from '../layout/PageHeader'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'
import useStore from '../../store/useStore'

const AdminServiceItemsPage = ({ service, onBack }) => {
  const { user } = useStore()
  const [viewMode, setViewMode] = useState('grid')
  const [searchTerm, setSearchTerm] = useState('')
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    requirements: '',
    deliverables: '',
    price: '',
    duration_days: '',
    sort_order: 0,
    is_active: true
  })

  // تحميل عناصر الخدمة
  const loadServiceItems = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('service_items')
        .select('*')
        .eq('service_id', service.id)
        .order('sort_order', { ascending: true })

      if (error) throw error
      setItems(data || [])
    } catch (error) {
      console.error('خطأ في تحميل عناصر الخدمة:', error)
      toast.error('فشل في تحميل عناصر الخدمة')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (service?.id) {
      loadServiceItems()
    }
  }, [service?.id])

  // إضافة عنصر جديد
  const handleAddItem = async () => {
    try {
      const { data, error } = await supabase
        .from('service_items')
        .insert([{
          ...formData,
          service_id: service.id,
          price: formData.price ? parseFloat(formData.price) : null,
          duration_days: formData.duration_days ? parseInt(formData.duration_days) : null,
          created_by: user.id
        }])
        .select()

      if (error) throw error

      setItems([...items, data[0]])
      setShowAddModal(false)
      resetForm()
      toast.success('تم إضافة العنصر بنجاح')
    } catch (error) {
      console.error('خطأ في إضافة العنصر:', error)
      toast.error('فشل في إضافة العنصر')
    }
  }

  // تعديل عنصر
  const handleEditItem = async () => {
    try {
      const { data, error } = await supabase
        .from('service_items')
        .update({
          ...formData,
          price: formData.price ? parseFloat(formData.price) : null,
          duration_days: formData.duration_days ? parseInt(formData.duration_days) : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedItem.id)
        .select()

      if (error) throw error

      setItems(items.map(item => 
        item.id === selectedItem.id ? data[0] : item
      ))
      setShowEditModal(false)
      setSelectedItem(null)
      resetForm()
      toast.success('تم تحديث العنصر بنجاح')
    } catch (error) {
      console.error('خطأ في تحديث العنصر:', error)
      toast.error('فشل في تحديث العنصر')
    }
  }

  // حذف عنصر
  const handleDeleteItem = async (itemId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) return

    try {
      const { error } = await supabase
        .from('service_items')
        .delete()
        .eq('id', itemId)

      if (error) throw error

      setItems(items.filter(item => item.id !== itemId))
      toast.success('تم حذف العنصر بنجاح')
    } catch (error) {
      console.error('خطأ في حذف العنصر:', error)
      toast.error('فشل في حذف العنصر')
    }
  }

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      requirements: '',
      deliverables: '',
      price: '',
      duration_days: '',
      sort_order: 0,
      is_active: true
    })
  }

  // فتح نموذج التعديل
  const openEditModal = (item) => {
    setSelectedItem(item)
    setFormData({
      title: item.title || '',
      description: item.description || '',
      requirements: item.requirements || '',
      deliverables: item.deliverables || '',
      price: item.price || '',
      duration_days: item.duration_days || '',
      sort_order: item.sort_order || 0,
      is_active: item.is_active
    })
    setShowEditModal(true)
  }

  // فلترة العناصر
  const filteredItems = items.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesSearch
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <PageHeader
        backButton={{
          text: "العودة للخدمات",
          onClick: onBack
        }}
        title={`إدارة عناصر: ${service.title}`}
        subtitle="إضافة وتعديل وحذف عناصر الخدمة"
      />

      {/* المحتوى الرئيسي */}
      <div className="px-6 space-y-6">
        {/* شريط الأدوات */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* البحث */}
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في العناصر..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* أزرار العرض والإضافة */}
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
              <button
                onClick={() => setShowAddModal(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <Plus className="w-4 h-4" />
                <span>إضافة عنصر</span>
              </button>
            </div>
          </div>
        </div>

        {/* عرض العناصر */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-4">جاري تحميل العناصر...</p>
          </div>
        ) : viewMode === 'grid' ? (
          // عرض الشبكة
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredItems.map((item) => (
              <div key={item.id} className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-2">{item.title}</h3>
                      <p className="text-sm text-gray-600 line-clamp-2">{item.description}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {item.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                  </div>

                  <div className="space-y-2 mb-4">
                    {item.price && (
                      <div className="flex items-center space-x-1 space-x-reverse text-green-600">
                        <DollarSign className="w-4 h-4" />
                        <span className="text-sm font-medium">{item.price.toLocaleString()} ريال</span>
                      </div>
                    )}
                    {item.duration_days && (
                      <div className="flex items-center space-x-1 space-x-reverse text-blue-600">
                        <Clock className="w-4 h-4" />
                        <span className="text-sm font-medium">{item.duration_days} يوم</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button
                      onClick={() => openEditModal(item)}
                      className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-1 space-x-reverse"
                    >
                      <Edit className="w-4 h-4" />
                      <span>تعديل</span>
                    </button>
                    <button
                      onClick={() => handleDeleteItem(item.id)}
                      className="px-3 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // عرض القائمة
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">العنصر</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المدة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجراءات</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredItems.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{item.title}</div>
                          <div className="text-sm text-gray-500 line-clamp-1">{item.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        {item.price ? `${item.price.toLocaleString()} ريال` : '-'}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        {item.duration_days ? `${item.duration_days} يوم` : '-'}
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {item.is_active ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => openEditModal(item)}
                            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                          >
                            تعديل
                          </button>
                          <button
                            onClick={() => handleDeleteItem(item.id)}
                            className="text-red-600 hover:text-red-700 text-sm font-medium"
                          >
                            حذف
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* رسالة عدم وجود نتائج */}
        {filteredItems.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عناصر</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'لا توجد عناصر تطابق معايير البحث' : 'لم يتم إضافة أي عناصر لهذه الخدمة بعد'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => setShowAddModal(true)}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                إضافة أول عنصر
              </button>
            )}
          </div>
        )}
      </div>

      {/* نموذج إضافة عنصر */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">إضافة عنصر جديد</h3>
                <button
                  onClick={() => {
                    setShowAddModal(false)
                    resetForm()
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">عنوان العنصر *</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل عنوان العنصر"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="وصف مختصر للعنصر"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المتطلبات</label>
                <textarea
                  value={formData.requirements}
                  onChange={(e) => setFormData({...formData, requirements: e.target.value})}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="المتطلبات اللازمة من العميل"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المخرجات</label>
                <textarea
                  value={formData.deliverables}
                  onChange={(e) => setFormData({...formData, deliverables: e.target.value})}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="ما سيتم تسليمه للعميل"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السعر (ريال)</label>
                  <input
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData({...formData, price: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المدة (أيام)</label>
                  <input
                    type="number"
                    value={formData.duration_days}
                    onChange={(e) => setFormData({...formData, duration_days: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0"
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="mr-2 block text-sm text-gray-900">
                  عنصر نشط
                </label>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-3 space-x-reverse">
              <button
                onClick={() => {
                  setShowAddModal(false)
                  resetForm()
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleAddItem}
                disabled={!formData.title.trim()}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse"
              >
                <Save className="w-4 h-4" />
                <span>حفظ</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تعديل عنصر */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">تعديل العنصر</h3>
                <button
                  onClick={() => {
                    setShowEditModal(false)
                    setSelectedItem(null)
                    resetForm()
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">عنوان العنصر *</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل عنوان العنصر"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="وصف مختصر للعنصر"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المتطلبات</label>
                <textarea
                  value={formData.requirements}
                  onChange={(e) => setFormData({...formData, requirements: e.target.value})}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="المتطلبات اللازمة من العميل"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المخرجات</label>
                <textarea
                  value={formData.deliverables}
                  onChange={(e) => setFormData({...formData, deliverables: e.target.value})}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="ما سيتم تسليمه للعميل"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السعر (ريال)</label>
                  <input
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData({...formData, price: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المدة (أيام)</label>
                  <input
                    type="number"
                    value={formData.duration_days}
                    onChange={(e) => setFormData({...formData, duration_days: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0"
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="edit_is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="edit_is_active" className="mr-2 block text-sm text-gray-900">
                  عنصر نشط
                </label>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-3 space-x-reverse">
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setSelectedItem(null)
                  resetForm()
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleEditItem}
                disabled={!formData.title.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse"
              >
                <Save className="w-4 h-4" />
                <span>حفظ التغييرات</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminServiceItemsPage
