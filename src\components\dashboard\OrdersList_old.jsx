import React from 'react'
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  XCircle,
  Calendar,
  DollarSign,
  User,
  ArrowLeft,
  MoreHorizontal
} from 'lucide-react'

const OrdersList = ({ orders = [], compact = false, onOrderClick }) => {
  // دالة لتحديد لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // دالة لتحديد أيقونة الحالة
  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'in_progress':
        return <AlertCircle className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // دالة لتحديد نص الحالة
  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'in_progress':
        return 'قيد التنفيذ'
      case 'completed':
        return 'مكتمل'
      case 'cancelled':
        return 'ملغي'
      default:
        return 'غير محدد'
    }
  }

  // دالة لتحديد لون الأولوية
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600'
      case 'normal':
        return 'text-blue-600'
      case 'low':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  // دالة لتنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد'
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // دالة لتنسيق المبلغ
  const formatAmount = (amount) => {
    if (!amount) return 'غير محدد'
    return `${amount.toLocaleString()} ريال`
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Clock className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          لا توجد طلبات
        </h3>
        <p className="text-gray-500">
          لم يتم العثور على أي طلبات بالمعايير المحددة
        </p>
      </div>
    )
  }

  return (
    <div className="divide-y divide-gray-200">
      {orders.map((order) => (
        <div
          key={order.id}
          className={`p-6 hover:bg-gray-50 transition-colors cursor-pointer ${
            compact ? 'py-4' : ''
          }`}
          onClick={() => onOrderClick && onOrderClick(order)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              {/* العنوان ورقم الطلب */}
              <div className="flex items-center space-x-3 space-x-reverse mb-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  {order.title}
                </h3>
                <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  {order.order_number}
                </span>
              </div>

              {/* الوصف */}
              {!compact && order.description && (
                <p className="text-gray-600 mb-3 line-clamp-2">
                  {order.description}
                </p>
              )}

              {/* المعلومات الإضافية */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                {/* الفئة */}
                <div className="flex items-center space-x-1 space-x-reverse">
                  <span className="font-medium">الفئة:</span>
                  <span>{order.category}</span>
                </div>

                {/* التاريخ */}
                <div className="flex items-center space-x-1 space-x-reverse">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(order.due_date)}</span>
                </div>

                {/* التكلفة */}
                {order.estimated_cost && (
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <DollarSign className="w-4 h-4" />
                    <span>{formatAmount(order.estimated_cost)}</span>
                  </div>
                )}

                {/* الأولوية */}
                <div className={`flex items-center space-x-1 space-x-reverse ${getPriorityColor(order.priority)}`}>
                  <span className="font-medium">
                    {order.priority === 'urgent' ? 'عاجل' :
                     order.priority === 'normal' ? 'عادي' :
                     'منخفض'}
                  </span>
                </div>
              </div>
            </div>

            {/* الحالة والإجراءات */}
            <div className="flex items-center space-x-3 space-x-reverse">
              {/* حالة الطلب */}
              <span className={`inline-flex items-center space-x-1 space-x-reverse px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(order.status)}`}>
                {getStatusIcon(order.status)}
                <span>{getStatusText(order.status)}</span>
              </span>

              {/* زر المزيد */}
              {!compact && (
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <MoreHorizontal className="w-5 h-5" />
                  </button>
                  <button className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors">
                    <ArrowLeft className="w-5 h-5" />
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* شريط التقدم (للطلبات قيد التنفيذ) */}
          {order.status === 'in_progress' && !compact && (
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                <span>التقدم</span>
                <span>65%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: '65%' }}
                ></div>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default OrdersList
