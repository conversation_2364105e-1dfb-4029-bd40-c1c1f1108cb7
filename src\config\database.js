// إعدادات قاعدة البيانات
export const DATABASE_CONFIG = {
  // تبديل بين البيانات التجريبية والحقيقية
  USE_DEMO_DATA: false, // ✅ تم التبديل إلى Supabase!
  
  // إعدادات Supabase
  SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
  SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY,
  
  // جداول قاعدة البيانات
  TABLES: {
    USERS: 'users',
    ORDERS: 'orders',
    TASKS: 'tasks',
    COMMUNICATIONS: 'communications',
    NOTIFICATIONS: 'notifications',
    PAYMENTS: 'payments',
    FILES: 'files'
  },
  
  // Views
  VIEWS: {
    ORDERS_WITH_USERS: 'orders_with_users',
    TASKS_WITH_ORDERS: 'tasks_with_orders',
    UNREAD_NOTIFICATIONS: 'unread_notifications',
    ORDER_STATISTICS: 'order_statistics',
    PAYMENT_STATISTICS: 'payment_statistics'
  },
  
  // حالات الطلبات
  ORDER_STATUS: {
    PENDING: 'pending',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
  },
  
  // أولويات الطلبات
  ORDER_PRIORITY: {
    LOW: 'low',
    NORMAL: 'normal',
    HIGH: 'high',
    URGENT: 'urgent'
  },
  
  // فئات الطلبات
  ORDER_CATEGORIES: [
    'تطوير ويب',
    'تطبيقات الجوال',
    'أنظمة إدارية',
    'تصميم',
    'تسويق',
    'استشارة',
    'تقنية',
    'أخرى'
  ],
  
  // حالات الدفع
  PAYMENT_STATUS: {
    PENDING: 'pending',
    PARTIAL: 'partial',
    PAID: 'paid',
    REFUNDED: 'refunded'
  },
  
  // طرق الدفع
  PAYMENT_METHODS: [
    'bank_transfer',
    'credit_card',
    'cash',
    'check',
    'online'
  ],
  
  // أنواع المهام
  TASK_TYPES: [
    'development',
    'design',
    'testing',
    'review',
    'deployment',
    'documentation'
  ],
  
  // حالات المهام
  TASK_STATUS: {
    TODO: 'todo',
    IN_PROGRESS: 'in_progress',
    REVIEW: 'review',
    DONE: 'done',
    CANCELLED: 'cancelled'
  },
  
  // أنواع الاتصالات
  COMMUNICATION_TYPES: [
    'email',
    'phone',
    'meeting',
    'message',
    'notification'
  ],
  
  // اتجاهات الاتصال
  COMMUNICATION_DIRECTIONS: {
    INCOMING: 'incoming',
    OUTGOING: 'outgoing'
  },
  
  // أنواع الإشعارات
  NOTIFICATION_TYPES: [
    'order_created',
    'order_updated',
    'payment_received',
    'task_completed',
    'message_received',
    'deadline_reminder'
  ],
  
  // مستويات العضوية
  MEMBERSHIP_LEVELS: {
    BASIC: 'basic',
    PREMIUM: 'premium',
    GOLD: 'gold',
    PLATINUM: 'platinum'
  }
}

// دالة للحصول على Store المناسب
export const getStore = () => {
  if (DATABASE_CONFIG.USE_DEMO_DATA) {
    return import('../store/useStore').then(module => module.default)
  } else {
    return import('../store/useStore_supabase').then(module => module.default)
  }
}

// دالة للتحقق من صحة إعدادات Supabase
export const validateSupabaseConfig = () => {
  if (!DATABASE_CONFIG.USE_DEMO_DATA) {
    if (!DATABASE_CONFIG.SUPABASE_URL || !DATABASE_CONFIG.SUPABASE_ANON_KEY) {
      console.warn('⚠️ متغيرات Supabase غير مكتملة. سيتم استخدام البيانات التجريبية.')
      return false
    }
  }
  return true
}

// دالة لتنسيق البيانات من Supabase
export const formatSupabaseData = {
  // تنسيق بيانات الطلب
  order: (data) => ({
    ...data,
    id: data.id,
    order_number: data.order_number,
    title: data.title,
    description: data.description,
    category: data.category,
    status: data.status,
    priority: data.priority,
    estimated_cost: parseFloat(data.estimated_cost || 0),
    final_cost: parseFloat(data.final_cost || 0),
    estimated_duration: parseInt(data.estimated_duration || 0),
    actual_duration: parseInt(data.actual_duration || 0),
    created_at: data.created_at,
    updated_at: data.updated_at,
    due_date: data.due_date,
    completion_date: data.completion_date,
    user_id: data.user_id,
    users: data.users
  }),
  
  // تنسيق بيانات المستخدم
  user: (data) => ({
    ...data,
    id: data.id,
    email: data.email,
    name: data.name,
    organization_name: data.organization_name,
    contact_person: data.contact_person,
    phone: data.phone,
    address: data.address,
    membership_level: data.membership_level,
    created_at: data.created_at,
    updated_at: data.updated_at
  }),
  
  // تنسيق بيانات المهمة
  task: (data) => ({
    ...data,
    id: data.id,
    title: data.title,
    description: data.description,
    status: data.status,
    priority: data.priority,
    assigned_to: data.assigned_to,
    estimated_hours: parseInt(data.estimated_hours || 0),
    actual_hours: parseInt(data.actual_hours || 0),
    progress_percentage: parseInt(data.progress_percentage || 0),
    start_date: data.start_date,
    due_date: data.due_date,
    completion_date: data.completion_date,
    order_id: data.order_id,
    created_at: data.created_at,
    updated_at: data.updated_at
  })
}

export default DATABASE_CONFIG
