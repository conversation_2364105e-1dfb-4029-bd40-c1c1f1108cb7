# 🗄️ قاعدة البيانات - نماء الاحترافية

## ✅ **تم إنشاء قاعدة البيانات بنجاح!**

### **📊 معلومات المشروع:**
- **اسم المشروع**: nama-3meel
- **معرف المشروع**: gutvoiqzgdznpribsfnk
- **المنطقة**: ap-southeast-2
- **قاعدة البيانات**: PostgreSQL 17
- **الحالة**: ACTIVE_HEALTHY ✅

---

## 🗂️ **الجداول المنشأة:**

### **1. جدول المستخدمين (users)**
```sql
- id (UUID) - مرجع لـ auth.users
- email (TEXT) - البريد الإلكتروني
- name (TEXT) - اسم العميل/المؤسسة
- contact_person (TEXT) - الشخص المسؤول
- phone (TEXT) - رقم الهاتف
- organization_name (TEXT) - اسم المؤسسة
- logo (TEXT) - رمز المؤسسة
- membership_level (TEXT) - مستوى العضوية
- address (TEXT) - العنوان
- created_at, updated_at - تواريخ الإنشاء والتحديث
```

### **2. جدول الطلبات (orders)**
```sql
- id (UUID) - معرف فريد
- user_id (UUID) - مرجع للعميل
- order_number (TEXT) - رقم الطلب
- title (TEXT) - عنوان الطلب
- description (TEXT) - وصف مفصل
- category (TEXT) - فئة الطلب
- priority (TEXT) - الأولوية (urgent/normal/low)
- status (TEXT) - الحالة (pending/in_progress/completed/cancelled)
- estimated_cost (DECIMAL) - التكلفة المقدرة
- final_cost (DECIMAL) - التكلفة النهائية
- due_date (DATE) - تاريخ الاستحقاق
- created_at, updated_at
```

### **3. جدول المهام (tasks)**
```sql
- id (UUID) - معرف فريد
- order_id (UUID) - مرجع للطلب
- title (TEXT) - عنوان المهمة
- description (TEXT) - وصف المهمة
- status (TEXT) - حالة المهمة
- assigned_to (TEXT) - المسؤول عن المهمة
- progress_percentage (INTEGER) - نسبة الإنجاز
- estimated_hours (INTEGER) - الساعات المقدرة
- actual_hours (INTEGER) - الساعات الفعلية
- created_at, updated_at
```

### **4. جدول المدفوعات (payments)**
```sql
- id (UUID) - معرف فريد
- order_id (UUID) - مرجع للطلب
- user_id (UUID) - مرجع للعميل
- invoice_number (TEXT) - رقم الفاتورة
- amount (DECIMAL) - المبلغ
- payment_method (TEXT) - طريقة الدفع
- payment_status (TEXT) - حالة الدفع
- payment_date (DATE) - تاريخ الدفع
- total_amount (DECIMAL) - المبلغ الإجمالي
- created_at, updated_at
```

### **5. جدول التواصل (communications)**
```sql
- id (UUID) - معرف فريد
- order_id (UUID) - مرجع للطلب
- user_id (UUID) - مرجع للعميل
- type (TEXT) - نوع التواصل (message/email/call)
- direction (TEXT) - الاتجاه (incoming/outgoing)
- content (TEXT) - محتوى الرسالة
- status (TEXT) - حالة الرسالة
- created_at, updated_at
```

### **6. جدول الملفات (files)**
```sql
- id (UUID) - معرف فريد
- order_id (UUID) - مرجع للطلب
- filename (TEXT) - اسم الملف
- file_path (TEXT) - مسار الملف
- file_size (INTEGER) - حجم الملف
- file_type (TEXT) - نوع الملف
- category (TEXT) - فئة الملف
- created_at, updated_at
```

### **7. جدول الإشعارات (notifications)**
```sql
- id (UUID) - معرف فريد
- user_id (UUID) - مرجع للعميل
- order_id (UUID) - مرجع للطلب
- type (TEXT) - نوع الإشعار
- title (TEXT) - عنوان الإشعار
- message (TEXT) - محتوى الإشعار
- status (TEXT) - حالة الإشعار (unread/read)
- created_at, updated_at
```

---

## 🔐 **الأمان (Row Level Security)**

### **تم تفعيل RLS على جميع الجداول:**
- ✅ المستخدمون يرون بياناتهم فقط
- ✅ سياسات أمان شاملة
- ✅ حماية من الوصول غير المصرح

### **السياسات المطبقة:**
- المستخدمون يمكنهم عرض وتحديث ملفاتهم الشخصية
- المستخدمون يمكنهم عرض طلباتهم فقط
- المستخدمون يمكنهم إنشاء طلبات جديدة
- المستخدمون يمكنهم عرض المهام المرتبطة بطلباتهم
- المستخدمون يمكنهم عرض مدفوعاتهم فقط

---

## ⚡ **تحسينات الأداء**

### **الفهارس المنشأة:**
- فهارس على user_id في جميع الجداول
- فهارس على status و priority
- فهارس على التواريخ المهمة
- فهارس على order_id للجداول المرتبطة

### **الدوال المساعدة:**
- ✅ `handle_updated_at()` - تحديث تلقائي لـ updated_at
- ✅ `generate_order_number()` - توليد أرقام الطلبات
- ✅ `generate_invoice_number()` - توليد أرقام الفواتير

### **المشغلات (Triggers):**
- تحديث تلقائي لـ updated_at في جميع الجداول
- ضمان تناسق البيانات

---

## 📊 **Views المفيدة**

### **تم إنشاء Views للاستعلامات المعقدة:**
- `orders_with_users` - الطلبات مع تفاصيل العملاء
- `order_statistics` - إحصائيات الطلبات
- `payment_statistics` - إحصائيات المدفوعات
- `tasks_with_orders` - المهام مع تفاصيل الطلبات
- `unread_notifications` - الإشعارات غير المقروءة

---

## 🔗 **معلومات الاتصال**

### **تفاصيل قاعدة البيانات:**
```
Host: db.gutvoiqzgdznpribsfnk.supabase.co
Database: postgres
Port: 5432
```

### **للاتصال من التطبيق:**
```env
VITE_SUPABASE_URL=https://gutvoiqzgdznpribsfnk.supabase.co
VITE_SUPABASE_ANON_KEY=[سيتم توفيره من Supabase Dashboard]
```

---

## 🧪 **الخطوات التالية**

### **1. الحصول على مفاتيح API:**
1. اذهب إلى Supabase Dashboard
2. اختر مشروع nama-3meel
3. Settings > API
4. انسخ URL و anon key

### **2. تحديث متغيرات البيئة:**
```env
VITE_SUPABASE_URL=https://gutvoiqzgdznpribsfnk.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

### **3. إنشاء المستخدمين التجريبيين:**
- سجل دخول في التطبيق
- أنشئ حسابات للعملاء التجريبيين
- اختبر جميع الوظائف

---

## 📞 **الدعم**

### **إذا احتجت مساعدة:**
- **Supabase Dashboard**: https://app.supabase.com
- **الوثائق**: https://supabase.com/docs
- **الدعم الفني**: <EMAIL>

---

**🎉 قاعدة البيانات جاهزة للاستخدام! يمكنك الآن ربط التطبيق وبدء الاختبار.**
