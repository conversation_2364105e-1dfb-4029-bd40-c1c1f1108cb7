import { supabase } from './supabase'

// ==================== إدارة المطالب ====================

// جلب المطالب لعنصر خدمة وعميل محددين
export const getServiceRequirements = async (serviceItemId, customerId) => {
  try {
    const { data, error } = await supabase
      .from('service_requirements')
      .select('*')
      .eq('service_item_id', serviceItemId)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    console.error('Error fetching service requirements:', error)
    return { data: null, error }
  }
}

// إضافة مطلب جديد
export const addServiceRequirement = async (serviceItemId, customerId, requirementData) => {
  try {
    const { data, error } = await supabase
      .from('service_requirements')
      .insert([{
        service_item_id: serviceItemId,
        customer_id: customerId,
        title: requirementData.title,
        description: requirementData.description,
        priority: requirementData.priority || 'normal',
        requires_file_upload: requirementData.requires_file_upload || false,
        file_upload_method: requirementData.file_upload_method || null,
        file_upload_details: requirementData.file_upload_details || null,
        created_by: (await supabase.auth.getUser()).data.user?.id
      }])
      .select()

    if (error) throw error
    return { data: data[0], error: null }
  } catch (error) {
    console.error('Error adding service requirement:', error)
    return { data: null, error }
  }
}

// تحديث مطلب
export const updateServiceRequirement = async (requirementId, updates) => {
  try {
    const { data, error } = await supabase
      .from('service_requirements')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', requirementId)
      .select()

    if (error) throw error
    return { data: data[0], error: null }
  } catch (error) {
    console.error('Error updating service requirement:', error)
    return { data: null, error }
  }
}

// حذف مطلب
export const deleteServiceRequirement = async (requirementId) => {
  try {
    const { error } = await supabase
      .from('service_requirements')
      .delete()
      .eq('id', requirementId)

    if (error) throw error
    return { error: null }
  } catch (error) {
    console.error('Error deleting service requirement:', error)
    return { error }
  }
}

// إنجاز مطلب من قبل العميل
export const completeServiceRequirement = async (requirementId, completionComment) => {
  try {
    const user = (await supabase.auth.getUser()).data.user
    console.log('Completing requirement with user:', user?.id, 'comment:', completionComment)

    const { data, error } = await supabase
      .from('service_requirements')
      .update({
        is_completed: true,
        completion_comment: completionComment,
        completed_by: user?.id,
        completed_at: new Date().toISOString()
      })
      .eq('id', requirementId)
      .select()

    console.log('Supabase update result:', { data, error })

    if (error) throw error
    return { data: data?.[0] || data, error: null }
  } catch (error) {
    console.error('Error completing service requirement:', error)
    return { data: null, error }
  }
}

// ==================== إدارة الأحداث ====================

// جلب الأحداث لعنصر خدمة وعميل محددين
export const getServiceActions = async (serviceItemId, customerId) => {
  try {
    const { data, error } = await supabase
      .from('service_actions')
      .select('*')
      .eq('service_item_id', serviceItemId)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    console.error('Error fetching service actions:', error)
    return { data: null, error }
  }
}

// إضافة حدث جديد
export const addServiceAction = async (serviceItemId, customerId, actionData) => {
  try {
    const { data, error } = await supabase
      .from('service_actions')
      .insert([{
        service_item_id: serviceItemId,
        customer_id: customerId,
        title: actionData.title,
        description: actionData.description,
        action_type: actionData.action_type || 'update',
        created_by: (await supabase.auth.getUser()).data.user?.id
      }])
      .select()

    if (error) throw error
    return { data: data[0], error: null }
  } catch (error) {
    console.error('Error adding service action:', error)
    return { data: null, error }
  }
}

// حذف حدث
export const deleteServiceAction = async (actionId) => {
  try {
    const { error } = await supabase
      .from('service_actions')
      .delete()
      .eq('id', actionId)

    if (error) throw error
    return { error: null }
  } catch (error) {
    console.error('Error deleting service action:', error)
    return { error }
  }
}

// ==================== إدارة الجدول الزمني ====================

// جلب الجدول الزمني لعنصر خدمة وعميل محددين
export const getServiceTimeline = async (serviceItemId, customerId) => {
  try {
    const { data, error } = await supabase
      .from('service_timeline')
      .select('*')
      .eq('service_item_id', serviceItemId)
      .eq('customer_id', customerId)
      .order('order_index', { ascending: true })

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    console.error('Error fetching service timeline:', error)
    return { data: null, error }
  }
}

// إضافة مرحلة جديدة للجدول الزمني
export const addServiceTimelinePhase = async (serviceItemId, customerId, phaseData) => {
  try {
    const { data, error } = await supabase
      .from('service_timeline')
      .insert([{
        service_item_id: serviceItemId,
        customer_id: customerId,
        phase_name: phaseData.phase_name,
        description: phaseData.description,
        duration_text: phaseData.duration_text,
        status: phaseData.status || 'pending',
        start_date: phaseData.start_date,
        end_date: phaseData.end_date,
        order_index: phaseData.order_index || 0,
        created_by: (await supabase.auth.getUser()).data.user?.id
      }])
      .select()

    if (error) throw error
    return { data: data[0], error: null }
  } catch (error) {
    console.error('Error adding service timeline phase:', error)
    return { data: null, error }
  }
}

// تحديث مرحلة في الجدول الزمني
export const updateServiceTimelinePhase = async (phaseId, updates) => {
  try {
    const { data, error } = await supabase
      .from('service_timeline')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', phaseId)
      .select()

    if (error) throw error
    return { data: data[0], error: null }
  } catch (error) {
    console.error('Error updating service timeline phase:', error)
    return { data: null, error }
  }
}

// حذف مرحلة من الجدول الزمني
export const deleteServiceTimelinePhase = async (phaseId) => {
  try {
    const { error } = await supabase
      .from('service_timeline')
      .delete()
      .eq('id', phaseId)

    if (error) throw error
    return { error: null }
  } catch (error) {
    console.error('Error deleting service timeline phase:', error)
    return { error }
  }
}

// ==================== دوال مساعدة ====================

// جلب معرف العميل الحالي
export const getCurrentCustomerId = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    return user?.id || null
  } catch (error) {
    console.error('Error getting current user:', error)
    return null
  }
}

// التحقق من صلاحيات الإداري
export const isCurrentUserAdmin = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return false

    const { data, error } = await supabase
      .from('users')
      .select('account_type')
      .eq('id', user.id)
      .single()

    if (error) throw error
    return data?.account_type === 'admin'
  } catch (error) {
    console.error('Error checking admin status:', error)
    return false
  }
}
