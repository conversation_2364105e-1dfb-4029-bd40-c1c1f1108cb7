import { supabase } from '../lib/supabase.js'

const users = [
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'شركة نماء للخدمات الرقمية',
    organization_name: 'شركة نماء للخدمات الرقمية',
    contact_person: 'الإدارة العامة',
    phone: '+966501234567',
    address: 'الرياض، المملكة العربية السعودية',
    membership_level: 'premium',
    logo: '🏢',
    website: 'https://nama3meel.com',
    preferences: {
      user_type: 'admin',
      permissions: {
        view_all_orders: true,
        manage_orders: true,
        add_events: true,
        manage_users: true,
        view_analytics: true,
        manage_settings: true
      }
    }
  },
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'مؤسسة روافد التنمية',
    organization_name: 'مؤسسة روافد التنمية',
    contact_person: 'محمد الروافد',
    phone: '+966501234568',
    address: 'الرياض، المملكة العربية السعودية',
    membership_level: 'gold',
    logo: '🌱',
    website: 'https://rawafed.org',
    preferences: {
      user_type: 'customer',
      notifications: true,
      language: 'ar'
    }
  },
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'جمعية نور الخيرية',
    organization_name: 'جمعية نور الخيرية',
    contact_person: 'عبدالله أحمد',
    phone: '+966509876543',
    address: 'جدة، المملكة العربية السعودية',
    membership_level: 'basic',
    logo: '💡',
    website: 'https://noor.org',
    preferences: {
      user_type: 'customer',
      notifications: true,
      language: 'ar'
    }
  },
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'جمعية ديم الخيرية',
    organization_name: 'جمعية ديم الخيرية',
    contact_person: 'أحمد الديم',
    phone: '+966551234567',
    address: 'الدمام، المنطقة الشرقية، المملكة العربية السعودية',
    membership_level: 'gold',
    logo: '🌟',
    website: 'https://deem.org',
    preferences: {
      user_type: 'customer',
      notifications: true,
      language: 'ar'
    }
  },
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'جمعية جمان الخيرية',
    organization_name: 'جمعية جمان الخيرية',
    contact_person: 'فاطمة جمان',
    phone: '+966561234567',
    address: 'جدة، منطقة مكة المكرمة، المملكة العربية السعودية',
    membership_level: 'basic',
    logo: '🌸',
    website: 'https://juman.org',
    preferences: {
      user_type: 'customer',
      notifications: true,
      language: 'ar'
    }
  }
]

async function createAllUsers() {
  console.log('🚀 بدء إنشاء المستخدمين...')
  
  for (const userData of users) {
    try {
      console.log(`📝 إنشاء مستخدم: ${userData.email}`)
      
      // إنشاء المستخدم في Auth
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true
      })

      if (authError) {
        console.error(`❌ خطأ في إنشاء Auth للمستخدم ${userData.email}:`, authError)
        continue
      }

      console.log(`✅ تم إنشاء Auth للمستخدم: ${userData.email}`)

      // إنشاء البروفايل
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: userData.email,
          name: userData.name,
          organization_name: userData.organization_name,
          contact_person: userData.contact_person,
          phone: userData.phone,
          address: userData.address,
          membership_level: userData.membership_level,
          logo: userData.logo,
          website: userData.website,
          preferences: userData.preferences
        })

      if (profileError) {
        console.error(`❌ خطأ في إنشاء البروفايل للمستخدم ${userData.email}:`, profileError)
        continue
      }

      console.log(`✅ تم إنشاء البروفايل للمستخدم: ${userData.email}`)
      
    } catch (error) {
      console.error(`❌ خطأ عام في إنشاء المستخدم ${userData.email}:`, error)
    }
  }
  
  console.log('🎉 انتهى إنشاء جميع المستخدمين!')
}

// تشغيل الدالة
createAllUsers().catch(console.error)
