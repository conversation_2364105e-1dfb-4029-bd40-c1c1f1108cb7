import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import toast from 'react-hot-toast'

// بيانات تجريبية للمستخدمين
const demoUsers = {
  '<EMAIL>': {
    id: '1',
    email: '<EMAIL>',
    name: 'مؤسسة الأمل الخيرية',
    organization_name: 'مؤسسة الأمل الخيرية',
    contact_person: 'أحمد محمد',
    phone: '+966501234567',
    address: 'الرياض، المملكة العربية السعودية',
    membership_level: 'premium'
  },
  '<EMAIL>': {
    id: '2', 
    email: '<EMAIL>',
    name: 'شركة الروافد للتقنية',
    organization_name: 'شركة الروافد للتقنية',
    contact_person: 'فاطمة علي',
    phone: '+966507654321',
    address: 'جدة، المملكة العربية السعودية',
    membership_level: 'gold'
  },
  '<EMAIL>': {
    id: '3',
    email: '<EMAIL>', 
    name: 'جمعية النور الخيرية',
    organization_name: 'جمعية النور الخيرية',
    contact_person: 'محمد أحمد',
    phone: '+966509876543',
    address: 'الدمام، المملكة العربية السعودية',
    membership_level: 'basic'
  }
}

// بيانات تجريبية للطلبات
const demoOrders = [
  {
    id: 1,
    order_number: 'ORD-2024-001',
    title: 'تطوير موقع إلكتروني متكامل',
    description: 'تطوير موقع إلكتروني متكامل للشركة يشمل لوحة تحكم إدارية ونظام إدارة المحتوى',
    status: 'in_progress',
    priority: 'normal',
    category: 'تطوير ويب',
    estimated_cost: 15000,
    final_cost: 15000,
    estimated_duration: 30,
    actual_duration: 15,
    due_date: '2024-07-15T23:59:59Z',
    created_at: '2024-06-15T10:00:00Z',
    updated_at: '2024-06-25T14:30:00Z',
    user_id: '1'
  },
  {
    id: 2,
    order_number: 'ORD-2024-002',
    title: 'تطبيق جوال للتجارة الإلكترونية',
    description: 'تطوير تطبيق جوال متكامل للتجارة الإلكترونية مع نظام دفع آمن',
    status: 'pending',
    priority: 'urgent',
    category: 'تطبيقات الجوال',
    estimated_cost: 25000,
    final_cost: null,
    estimated_duration: 45,
    actual_duration: null,
    due_date: '2024-08-30T23:59:59Z',
    created_at: '2024-06-20T09:00:00Z',
    updated_at: '2024-06-20T09:00:00Z',
    user_id: '2'
  },
  {
    id: 3,
    order_number: 'ORD-2024-003',
    title: 'نظام إدارة المخزون',
    description: 'تطوير نظام شامل لإدارة المخزون والمبيعات',
    status: 'completed',
    priority: 'normal',
    category: 'أنظمة إدارية',
    estimated_cost: 12000,
    final_cost: 11500,
    estimated_duration: 20,
    actual_duration: 18,
    due_date: '2024-06-30T23:59:59Z',
    created_at: '2024-05-15T08:00:00Z',
    updated_at: '2024-06-28T16:00:00Z',
    user_id: '3'
  },
  {
    id: 4,
    order_number: 'ORD-2024-004',
    title: 'تصميم هوية بصرية',
    description: 'تصميم هوية بصرية متكاملة تشمل الشعار والألوان والخطوط',
    status: 'completed',
    priority: 'low',
    category: 'تصميم',
    estimated_cost: 8000,
    final_cost: 7500,
    estimated_duration: 15,
    actual_duration: 12,
    due_date: '2024-06-15T23:59:59Z',
    created_at: '2024-05-01T10:00:00Z',
    updated_at: '2024-06-12T14:00:00Z',
    user_id: '1'
  }
]

const useStore = create(
  persist(
    (set, get) => ({
      // الحالة الأولية
      user: null,
      session: null,
      isLoading: false,
      isAuthenticated: false,
      
      // الطلبات
      orders: [],
      selectedOrder: null,
      ordersLoading: false,
      
      // الإشعارات
      notifications: [],
      unreadCount: 0,
      notificationsLoading: false,
      
      // إعدادات الواجهة
      sidebarOpen: true,
      theme: 'light',
      language: 'ar',

      // ==================== دوال المصادقة ====================
      
      // تسجيل الدخول
      signIn: async (email, password) => {
        set({ isLoading: true })
        
        try {
          if (demoUsers[email] && password === '123456') {
            const user = demoUsers[email]
            
            set({
              user: user,
              session: { user: user },
              isAuthenticated: true,
              isLoading: false
            })

            // تحميل البيانات التجريبية
            setTimeout(() => {
              get().loadOrders()
              get().loadNotifications()
            }, 100)

            toast.success(`مرحباً بك ${user.name}`)
            return { success: true }
          } else {
            set({ isLoading: false })
            toast.error('بيانات الدخول غير صحيحة')
            return { success: false, error: 'Invalid credentials' }
          }
          
        } catch (error) {
          set({ isLoading: false })
          toast.error('حدث خطأ في تسجيل الدخول')
          return { success: false, error: error.message }
        }
      },

      // تسجيل الخروج
      signOut: async () => {
        try {
          set({
            user: null,
            session: null,
            isAuthenticated: false,
            orders: [],
            notifications: [],
            selectedOrder: null
          })
          
          toast.success('تم تسجيل الخروج بنجاح')
          
        } catch (error) {
          toast.error('حدث خطأ في تسجيل الخروج')
        }
      },

      // ==================== دوال الطلبات ====================
      
      // تحميل الطلبات
      loadOrders: async () => {
        const { user } = get()
        if (!user) return

        set({ ordersLoading: true })
        
        try {
          // محاكاة تحميل البيانات
          await new Promise(resolve => setTimeout(resolve, 500))
          
          // فلترة الطلبات حسب المستخدم
          const userOrders = demoOrders.filter(order => order.user_id === user.id)
          
          set({ 
            orders: userOrders,
            ordersLoading: false 
          })
          
        } catch (error) {
          set({ ordersLoading: false })
          console.error('حدث خطأ في تحميل الطلبات:', error)
        }
      },

      // تحديد طلب محدد
      selectOrder: async (orderId) => {
        const { orders } = get()
        const order = orders.find(o => o.id === parseInt(orderId))
        
        if (order) {
          set({ selectedOrder: order })
        }
      },

      // ==================== دوال الإشعارات ====================
      
      // تحميل الإشعارات
      loadNotifications: async () => {
        const { user } = get()
        if (!user) return

        set({ notificationsLoading: true })
        
        try {
          // بيانات تجريبية للإشعارات
          const demoNotifications = [
            {
              id: 1,
              title: 'تم قبول طلبك',
              message: 'تم قبول طلب تطوير الموقع الإلكتروني',
              type: 'success',
              status: 'unread',
              created_at: '2024-06-25T10:00:00Z'
            },
            {
              id: 2,
              title: 'تحديث على المشروع',
              message: 'تم إنجاز 50% من المشروع',
              type: 'info',
              status: 'read',
              created_at: '2024-06-24T14:30:00Z'
            }
          ]
          
          const unreadCount = demoNotifications.filter(n => n.status === 'unread').length
          
          set({ 
            notifications: demoNotifications,
            unreadCount: unreadCount,
            notificationsLoading: false 
          })
          
        } catch (error) {
          set({ notificationsLoading: false })
          console.error('حدث خطأ في تحميل الإشعارات:', error)
        }
      },

      // ==================== دوال الواجهة ====================
      
      // تبديل الشريط الجانبي
      toggleSidebar: () => {
        set(state => ({ sidebarOpen: !state.sidebarOpen }))
      },

      // تبديل المظهر
      toggleTheme: () => {
        set(state => ({
          theme: state.theme === 'light' ? 'dark' : 'light'
        }))
      },

      // إعادة تعيين الحالة
      reset: () => {
        set({
          user: null,
          session: null,
          isLoading: false,
          isAuthenticated: false,
          orders: [],
          selectedOrder: null,
          ordersLoading: false,
          notifications: [],
          unreadCount: 0,
          notificationsLoading: false
        })
      }
    }),
    {
      name: 'nama-store',
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        sidebarOpen: state.sidebarOpen
      })
    }
  )
)

export default useStore
