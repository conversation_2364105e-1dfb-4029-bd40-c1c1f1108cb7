import React, { useState, useEffect } from 'react'
import { 
  ArrowRight, 
  Calendar, 
  DollarSign, 
  User, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  XCircle,
  FileText,
  MessageSquare,
  Paperclip,
  Edit,
  Trash2,
  Plus,
  Download,
  Eye,
  Star,
  Flag
} from 'lucide-react'
import useStore from '../../store/useStore'
import LoadingSpinner from '../ui/LoadingSpinner'
import OrderTimeline from './OrderTimeline'
import OrderTasks from './OrderTasks'
import OrderFiles from './OrderFiles'
import OrderComments from './OrderComments'
import OrderPayments from './OrderPayments'

const OrderDetailsPage = ({ orderId, onBack }) => {
  const {
    selectedOrder,
    selectOrder,
    updateOrderStatus,
    selectedOrderLoading,
    user
  } = useStore()
  
  const [activeTab, setActiveTab] = useState('overview')
  const [showEditModal, setShowEditModal] = useState(false)

  // تحميل تفاصيل الطلب
  useEffect(() => {
    if (orderId) {
      // إذا لم يكن هناك طلب محدد أو كان مختلف، حمل الطلب
      if (!selectedOrder || selectedOrder.id !== orderId) {
        selectOrder(orderId)
      }
    }
  }, [orderId, selectOrder, selectedOrder])

  // دالة لتحديد لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // دالة لتحديد أيقونة الحالة
  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'in_progress':
        return <AlertCircle className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // دالة لتحديد نص الحالة
  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'in_progress':
        return 'قيد التنفيذ'
      case 'completed':
        return 'مكتمل'
      case 'cancelled':
        return 'ملغي'
      default:
        return 'غير محدد'
    }
  }

  // دالة لتنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد'
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // دالة لتنسيق المبلغ
  const formatAmount = (amount) => {
    if (!amount) return 'غير محدد'
    return `${amount.toLocaleString()} ريال`
  }

  // دالة لتحديد لون الأولوية
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-50'
      case 'normal':
        return 'text-blue-600 bg-blue-50'
      case 'low':
        return 'text-gray-600 bg-gray-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  // التبويبات
  const tabs = [
    { id: 'overview', label: 'نظرة عامة', icon: FileText },
    { id: 'timeline', label: 'الجدول الزمني', icon: Calendar },
    { id: 'tasks', label: 'المهام', icon: CheckCircle },
    { id: 'files', label: 'الملفات', icon: Paperclip },
    { id: 'comments', label: 'التعليقات', icon: MessageSquare },
    { id: 'payments', label: 'المدفوعات', icon: DollarSign }
  ]

  // بيانات تجريبية في حالة عدم وجود بيانات
  const demoOrder = {
    id: orderId,
    title: 'تطوير موقع إلكتروني متكامل',
    order_number: 'ORD-2024-001',
    description: 'تطوير موقع إلكتروني متكامل للشركة يشمل لوحة تحكم إدارية ونظام إدارة المحتوى',
    status: 'in_progress',
    priority: 'normal',
    category: 'تطوير ويب',
    estimated_cost: 15000,
    final_cost: 15000,
    estimated_duration: 30,
    actual_duration: 15,
    due_date: '2024-07-15T23:59:59Z',
    created_at: '2024-06-15T10:00:00Z',
    updated_at: '2024-06-25T14:30:00Z'
  }

  const currentOrder = selectedOrder || demoOrder

  if (selectedOrderLoading) {
    return <LoadingSpinner fullScreen text="جاري تحميل تفاصيل الطلب..." />
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* الهيدر */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            {/* زر العودة والعنوان */}
            <div className="flex items-center space-x-4 space-x-reverse">
              <button
                onClick={onBack}
                className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowRight className="w-5 h-5" />
                <span>العودة للطلبات</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {currentOrder.title}
                </h1>
                <p className="text-sm text-gray-500">
                  {currentOrder.order_number}
                </p>
              </div>
            </div>

            {/* حالة الطلب والإجراءات */}
            <div className="flex items-center space-x-3 space-x-reverse">
              {/* حالة الطلب */}
              <span className={`inline-flex items-center space-x-1 space-x-reverse px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(currentOrder.status)}`}>
                {getStatusIcon(currentOrder.status)}
                <span>{getStatusText(currentOrder.status)}</span>
              </span>

              {/* الأولوية */}
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(currentOrder.priority)}`}>
                {currentOrder.priority === 'urgent' ? 'عاجل' :
                 currentOrder.priority === 'normal' ? 'عادي' :
                 'منخفض'}
              </span>

              {/* أزرار الإجراءات */}
              <div className="flex items-center space-x-2 space-x-reverse">
                <button
                  onClick={() => setShowEditModal(true)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                  title="تعديل الطلب"
                >
                  <Edit className="w-5 h-5" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Star className="w-5 h-5" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Flag className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>

          {/* التبويبات */}
          <div className="flex space-x-8 space-x-reverse">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isActive = activeTab === tab.id
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 space-x-reverse py-4 border-b-2 font-medium text-sm transition-colors ${
                    isActive
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* المحتوى */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* العمود الرئيسي */}
            <div className="lg:col-span-2 space-y-6">
              {/* وصف الطلب */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  وصف الطلب
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  {currentOrder.description || 'لا يوجد وصف متاح'}
                </p>
              </div>

              {/* تفاصيل إضافية */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  تفاصيل إضافية
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الفئة
                    </label>
                    <p className="text-gray-900">{currentOrder.category}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      تاريخ الإنشاء
                    </label>
                    <p className="text-gray-900">{formatDate(currentOrder.created_at)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      تاريخ الاستحقاق
                    </label>
                    <p className="text-gray-900">{formatDate(currentOrder.due_date)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      آخر تحديث
                    </label>
                    <p className="text-gray-900">{formatDate(currentOrder.updated_at)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* الشريط الجانبي */}
            <div className="space-y-6">
              {/* معلومات سريعة */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  معلومات سريعة
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">التكلفة المقدرة</span>
                    <span className="font-semibold text-gray-900">
                      {formatAmount(currentOrder.estimated_cost)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">التكلفة النهائية</span>
                    <span className="font-semibold text-gray-900">
                      {formatAmount(currentOrder.final_cost)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">المدة المقدرة</span>
                    <span className="font-semibold text-gray-900">
                      {currentOrder.estimated_duration ? `${currentOrder.estimated_duration} يوم` : 'غير محدد'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">المدة الفعلية</span>
                    <span className="font-semibold text-gray-900">
                      {currentOrder.actual_duration ? `${currentOrder.actual_duration} يوم` : 'غير محدد'}
                    </span>
                  </div>
                </div>
              </div>

              {/* شريط التقدم */}
              {currentOrder.status === 'in_progress' && (
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    تقدم المشروع
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">التقدم الإجمالي</span>
                      <span className="font-semibold text-gray-900">65%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: '65%' }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500">
                      متوقع الانتهاء خلال 5 أيام
                    </p>
                  </div>
                </div>
              )}

              {/* إجراءات سريعة */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  إجراءات سريعة
                </h3>
                <div className="space-y-3">
                  <button className="w-full btn-primary">
                    إضافة تعليق
                  </button>
                  <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                    رفع ملف
                  </button>
                  <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                    إنشاء مهمة
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'timeline' && <OrderTimeline order={currentOrder} />}
        {activeTab === 'tasks' && <OrderTasks orderId={currentOrder.id} />}
        {activeTab === 'files' && <OrderFiles orderId={currentOrder.id} />}
        {activeTab === 'comments' && <OrderComments orderId={currentOrder.id} />}
        {activeTab === 'payments' && <OrderPayments orderId={currentOrder.id} />}
      </div>
    </div>
  )
}

export default OrderDetailsPage
