import { useState, useEffect } from 'react'

// Hook لإدارة حالة السايدبار
export const useSidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(true) // مطوي بشكل افتراضي

  // حفظ الحالة في localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-collapsed')
    if (savedState !== null) {
      setIsCollapsed(JSON.parse(savedState))
    }
  }, [])

  const toggleSidebar = () => {
    const newState = !isCollapsed
    setIsCollapsed(newState)
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newState))
  }

  return {
    isCollapsed,
    toggleSidebar,
    sidebarWidth: isCollapsed ? 'w-16' : 'w-64',

    // إضافة مساعدات للتخطيط المتجاوب - دائماً موسع
    gridCols: {
      normal: 'md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
      stats: 'sm:grid-cols-2 lg:grid-cols-4',
      admin: 'xl:grid-cols-4 2xl:grid-cols-5'
    }
  }
}

export default useSidebar
