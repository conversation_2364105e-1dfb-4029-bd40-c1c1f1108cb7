import React, { useState, useEffect } from 'react'
import { 
  Plus, 
  Edit, 
  Trash2, 
  ArrowLeft,
  Save,
  X
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const ServiceItemsManagement = ({ service, onBack }) => {
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState(null)

  // بيانات العنصر الجديد
  const [newItem, setNewItem] = useState({
    title: '',
    description: '',
    price: '',
    duration_days: '',
    requirements: '',
    deliverables: ''
  })

  // تحميل عناصر الخدمة
  const loadItems = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('service_items')
        .select('*')
        .eq('service_id', service.id)
        .order('sort_order', { ascending: true })

      if (error) throw error
      setItems(data || [])
    } catch (error) {
      console.error('خطأ في تحميل عناصر الخدمة:', error)
      toast.error('فشل في تحميل عناصر الخدمة')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (service?.id) {
      loadItems()
    }
  }, [service?.id])

  // إضافة عنصر جديد
  const handleAddItem = async () => {
    try {
      if (!newItem.title || !newItem.description) {
        toast.error('يرجى ملء جميع الحقول المطلوبة')
        return
      }

      const { error } = await supabase
        .from('service_items')
        .insert([{
          service_id: service.id,
          title: newItem.title,
          description: newItem.description,
          price: parseFloat(newItem.price) || null,
          duration_days: parseInt(newItem.duration_days) || null,
          requirements: newItem.requirements,
          deliverables: newItem.deliverables,
          sort_order: items.length + 1
        }])

      if (error) throw error

      toast.success('تم إضافة العنصر بنجاح')
      setShowAddModal(false)
      setNewItem({
        title: '',
        description: '',
        price: '',
        duration_days: '',
        requirements: '',
        deliverables: ''
      })
      loadItems()
    } catch (error) {
      console.error('خطأ في إضافة العنصر:', error)
      toast.error('فشل في إضافة العنصر')
    }
  }

  // تحديث عنصر
  const handleUpdateItem = async () => {
    try {
      if (!selectedItem.title || !selectedItem.description) {
        toast.error('يرجى ملء جميع الحقول المطلوبة')
        return
      }

      const { error } = await supabase
        .from('service_items')
        .update({
          title: selectedItem.title,
          description: selectedItem.description,
          price: parseFloat(selectedItem.price) || null,
          duration_days: parseInt(selectedItem.duration_days) || null,
          requirements: selectedItem.requirements,
          deliverables: selectedItem.deliverables,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedItem.id)

      if (error) throw error

      toast.success('تم تحديث العنصر بنجاح')
      setShowEditModal(false)
      setSelectedItem(null)
      loadItems()
    } catch (error) {
      console.error('خطأ في تحديث العنصر:', error)
      toast.error('فشل في تحديث العنصر')
    }
  }

  // حذف عنصر
  const handleDeleteItem = async (itemId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      return
    }

    try {
      const { error } = await supabase
        .from('service_items')
        .delete()
        .eq('id', itemId)

      if (error) throw error

      toast.success('تم حذف العنصر بنجاح')
      loadItems()
    } catch (error) {
      console.error('خطأ في حذف العنصر:', error)
      toast.error('فشل في حذف العنصر')
    }
  }

  // تبديل حالة العنصر
  const toggleItemStatus = async (itemId, currentStatus) => {
    try {
      const { error } = await supabase
        .from('service_items')
        .update({ 
          is_active: !currentStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', itemId)

      if (error) throw error

      toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} العنصر`)
      loadItems()
    } catch (error) {
      console.error('خطأ في تغيير حالة العنصر:', error)
      toast.error('فشل في تغيير حالة العنصر')
    }
  }

  return (
    <div className="space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-3 space-x-reverse mb-2">
            <button
              onClick={onBack}
              className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>العودة للخدمات</span>
            </button>
          </div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة عناصر: {service.title}</h1>
          <p className="text-gray-600 mt-1">إضافة وتعديل وحذف عناصر الخدمة</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>إضافة عنصر جديد</span>
        </button>
      </div>

      {/* قائمة العناصر */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-4">جاري تحميل العناصر...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {items.map((item) => (
            <div key={item.id} className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-2">{item.title}</h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{item.description}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    item.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {item.is_active ? 'نشط' : 'غير نشط'}
                  </span>
                </div>

                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  {item.price && (
                    <p><strong>السعر:</strong> {item.price.toLocaleString()} ريال</p>
                  )}
                  {item.duration_days && (
                    <p><strong>المدة:</strong> {item.duration_days} يوم</p>
                  )}
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button
                      onClick={() => {
                        setSelectedItem(item)
                        setShowEditModal(true)
                      }}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="تعديل"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteItem(item.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="حذف"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                  
                  <button
                    onClick={() => toggleItemStatus(item.id, item.is_active)}
                    className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                      item.is_active 
                        ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                    }`}
                  >
                    {item.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* رسالة عدم وجود عناصر */}
      {!loading && items.length === 0 && (
        <div className="text-center py-12">
          <Plus className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عناصر</h3>
          <p className="text-gray-500 mb-4">لم يتم إضافة أي عناصر لهذه الخدمة بعد</p>
          <button
            onClick={() => setShowAddModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            إضافة أول عنصر
          </button>
        </div>
      )}

      {/* نموذج إضافة عنصر جديد */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900">إضافة عنصر جديد</h2>
              <button
                onClick={() => setShowAddModal(false)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">عنوان العنصر *</label>
                <input
                  type="text"
                  value={newItem.title}
                  onChange={(e) => setNewItem({...newItem, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="مثال: موقع شركة تجارية"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">وصف العنصر *</label>
                <textarea
                  value={newItem.description}
                  onChange={(e) => setNewItem({...newItem, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="وصف مفصل للعنصر..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السعر (ريال)</label>
                  <input
                    type="number"
                    value={newItem.price}
                    onChange={(e) => setNewItem({...newItem, price: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="5000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المدة (أيام)</label>
                  <input
                    type="number"
                    value={newItem.duration_days}
                    onChange={(e) => setNewItem({...newItem, duration_days: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="30"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المتطلبات</label>
                <textarea
                  value={newItem.requirements}
                  onChange={(e) => setNewItem({...newItem, requirements: e.target.value})}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="المتطلبات اللازمة من العميل..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المخرجات</label>
                <textarea
                  value={newItem.deliverables}
                  onChange={(e) => setNewItem({...newItem, deliverables: e.target.value})}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="ما سيتم تسليمه للعميل..."
                />
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 space-x-reverse mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleAddItem}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                إضافة العنصر
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تعديل العنصر */}
      {showEditModal && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900">تعديل العنصر</h2>
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setSelectedItem(null)
                }}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">عنوان العنصر *</label>
                <input
                  type="text"
                  value={selectedItem.title}
                  onChange={(e) => setSelectedItem({...selectedItem, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">وصف العنصر *</label>
                <textarea
                  value={selectedItem.description}
                  onChange={(e) => setSelectedItem({...selectedItem, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السعر (ريال)</label>
                  <input
                    type="number"
                    value={selectedItem.price || ''}
                    onChange={(e) => setSelectedItem({...selectedItem, price: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المدة (أيام)</label>
                  <input
                    type="number"
                    value={selectedItem.duration_days || ''}
                    onChange={(e) => setSelectedItem({...selectedItem, duration_days: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المتطلبات</label>
                <textarea
                  value={selectedItem.requirements || ''}
                  onChange={(e) => setSelectedItem({...selectedItem, requirements: e.target.value})}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المخرجات</label>
                <textarea
                  value={selectedItem.deliverables || ''}
                  onChange={(e) => setSelectedItem({...selectedItem, deliverables: e.target.value})}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 space-x-reverse mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setSelectedItem(null)
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleUpdateItem}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                حفظ التغييرات
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ServiceItemsManagement
