import React, { useState, useEffect } from 'react'
import {
  Search,
  Filter,
  Clock,
  CheckCircle,
  AlertCircle,
  X,
  Activity,
  Calendar,
  FileText,
  Plus,
  Edit,
  Trash2,
  Package,
  MessageSquare
} from 'lucide-react'
import PageHeader from '../layout/PageHeader'
import {
  getServiceRequirements,
  addServiceRequirement,
  updateServiceRequirement,
  deleteServiceRequirement,
  completeServiceRequirement,
  getServiceActions,
  addServiceAction,
  deleteServiceAction,
  getServiceTimeline,
  addServiceTimelinePhase,
  updateServiceTimelinePhase,
  deleteServiceTimelinePhase,
  getCurrentCustomerId,
  isCurrentUserAdmin
} from '../../lib/serviceData'
import toast from 'react-hot-toast'
import { supabase } from '../../lib/supabase'
import useStore from '../../store/useStore'
import useSidebar from '../../hooks/useSidebar'
// import ServiceRequestModal from './ServiceRequestModal' // تم إزالة طلب العنصر

const ServiceItemsPage = ({ service, onItemSelect, onBack }) => {
  const { user } = useStore()
  const { isCollapsed, gridCols } = useSidebar()
  // تم إزالة viewMode - نستخدم عرض الجدول فقط
  const [searchTerm, setSearchTerm] = useState('')
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedItemForDetails, setSelectedItemForDetails] = useState(null)
  // تم إزالة expandedCard - نستخدم عرض الجدول فقط
  const [activeTab, setActiveTab] = useState('required')
  const [relatedRequests, setRelatedRequests] = useState([])
  const [loadingRequests, setLoadingRequests] = useState(false)

  // البيانات الحقيقية من قاعدة البيانات
  const [requirements, setRequirements] = useState([])
  const [currentActions, setCurrentActions] = useState([])
  const [timeline, setTimeline] = useState([])
  const [loadingData, setLoadingData] = useState(false)

  // نماذج الإضافة
  const [showAddRequirement, setShowAddRequirement] = useState(false)
  const [showAddAction, setShowAddAction] = useState(false)
  const [showAddTimelinePhase, setShowAddTimelinePhase] = useState(false)
  const [newRequirement, setNewRequirement] = useState('')
  const [newAction, setNewAction] = useState('')
  const [newTimelinePhase, setNewTimelinePhase] = useState({
    phase_name: '',
    description: '',
    duration_text: '',
    status: 'pending'
  })

  // معرف العميل الحالي
  const [currentCustomerId, setCurrentCustomerId] = useState(null)
  const [isAdminUser, setIsAdminUser] = useState(false)

  // التحقق من نوع المستخدم
  const isAdmin = isAdminUser || user?.account_type === 'admin' || user?.email === '<EMAIL>'

  // جلب البيانات عند تحديد عنصر
  useEffect(() => {
    const initializeData = async () => {
      if (selectedItemForDetails) {
        setLoadingData(true)

        // جلب معرف العميل الحالي وصلاحيات الإداري
        const customerId = await getCurrentCustomerId()
        const adminStatus = await isCurrentUserAdmin()

        setCurrentCustomerId(customerId)
        setIsAdminUser(adminStatus)

        if (customerId) {
          await loadServiceData(selectedItemForDetails.id, customerId)
        }

        setLoadingData(false)
      }
    }

    initializeData()
  }, [selectedItemForDetails])

  // جلب جميع البيانات لعنصر الخدمة والعميل
  const loadServiceData = async (serviceItemId, customerId) => {
    try {
      console.log('Loading service data for:', { serviceItemId, customerId })

      // جلب المطالب
      const { data: requirementsData, error: reqError } = await getServiceRequirements(serviceItemId, customerId)
      console.log('Requirements data:', requirementsData, 'Error:', reqError)
      setRequirements(requirementsData || [])

      // جلب الأحداث
      const { data: actionsData, error: actError } = await getServiceActions(serviceItemId, customerId)
      console.log('Actions data:', actionsData, 'Error:', actError)
      setCurrentActions(actionsData || [])

      // جلب الجدول الزمني
      const { data: timelineData, error: timeError } = await getServiceTimeline(serviceItemId, customerId)
      console.log('Timeline data:', timelineData, 'Error:', timeError)
      setTimeline(timelineData || [])

    } catch (error) {
      console.error('Error loading service data:', error)
      toast.error('حدث خطأ في تحميل البيانات')
    }
  }

  // دوال إضافة المطالب والإجراءات
  const addRequirement = async () => {
    if (newRequirement.trim() && selectedItemForDetails && currentCustomerId) {
      try {
        const { data, error } = await addServiceRequirement(
          selectedItemForDetails.id,
          currentCustomerId,
          {
            title: newRequirement,
            priority: 'normal'
          }
        )

        if (error) throw error

        setRequirements([data, ...requirements])
        setNewRequirement('')
        setShowAddRequirement(false)
        toast.success('تم إضافة المطلب بنجاح')
      } catch (error) {
        console.error('Error adding requirement:', error)
        toast.error('حدث خطأ في إضافة المطلب')
      }
    }
  }

  const addCurrentAction = async () => {
    if (newAction.trim() && selectedItemForDetails && currentCustomerId) {
      try {
        const { data, error } = await addServiceAction(
          selectedItemForDetails.id,
          currentCustomerId,
          {
            title: newAction,
            description: 'إجراء جديد تم إضافته',
            action_type: 'update'
          }
        )

        if (error) throw error

        setCurrentActions([data, ...currentActions])
        setNewAction('')
        setShowAddAction(false)
        toast.success('تم إضافة الحدث بنجاح')
      } catch (error) {
        console.error('Error adding action:', error)
        toast.error('حدث خطأ في إضافة الحدث')
      }
    }
  }

  // حذف مطلب
  const removeRequirement = async (requirementId) => {
    try {
      const { error } = await deleteServiceRequirement(requirementId)
      if (error) throw error

      setRequirements(requirements.filter(req => req.id !== requirementId))
      toast.success('تم حذف المطلب بنجاح')
    } catch (error) {
      console.error('Error deleting requirement:', error)
      toast.error('حدث خطأ في حذف المطلب')
    }
  }

  // حذف حدث
  const removeAction = async (actionId) => {
    try {
      const { error } = await deleteServiceAction(actionId)
      if (error) throw error

      setCurrentActions(currentActions.filter(action => action.id !== actionId))
      toast.success('تم حذف الحدث بنجاح')
    } catch (error) {
      console.error('Error deleting action:', error)
      toast.error('حدث خطأ في حذف الحدث')
    }
  }

  // حالة نموذج الإنجاز
  const [showCompletionModal, setShowCompletionModal] = useState(false)
  const [selectedRequirement, setSelectedRequirement] = useState(null)
  const [completionComment, setCompletionComment] = useState('')
  const [showEditCommentModal, setShowEditCommentModal] = useState(false)
  const [editingRequirement, setEditingRequirement] = useState(null)
  const [editComment, setEditComment] = useState('')

  // فتح نموذج الإنجاز
  const openCompletionModal = (requirement) => {
    setSelectedRequirement(requirement)
    setShowCompletionModal(true)
    setCompletionComment('')
  }

  // إنجاز متطلب (للعملاء)
  const handleCompleteRequirement = async () => {
    if (!selectedRequirement) return

    try {
      console.log('Completing requirement:', selectedRequirement.id, 'Comment:', completionComment)

      const { data, error } = await completeServiceRequirement(selectedRequirement.id, completionComment.trim())

      console.log('Completion result:', { data, error })

      if (error) throw error

      // إعادة تحميل البيانات لضمان التحديث
      if (selectedItemForDetails && currentCustomerId) {
        console.log('Reloading data after completion...')
        await loadServiceData(selectedItemForDetails.id, currentCustomerId)
      }

      setShowCompletionModal(false)
      setSelectedRequirement(null)
      setCompletionComment('')
      toast.success(`تم إنجاز "${selectedRequirement.title}" بنجاح!`)
    } catch (error) {
      console.error('Error completing requirement:', error)
      toast.error('حدث خطأ في إنجاز المطلب')
    }
  }

  // تعديل تعليق الإنجاز
  const handleEditComment = async () => {
    if (!editingRequirement) return

    try {
      const { data, error } = await updateServiceRequirement(editingRequirement.id, {
        completion_comment: editComment.trim()
      })

      if (error) throw error

      // إعادة تحميل البيانات لضمان التحديث
      if (selectedItemForDetails && currentCustomerId) {
        await loadServiceData(selectedItemForDetails.id, currentCustomerId)
      }

      setShowEditCommentModal(false)
      setEditingRequirement(null)
      setEditComment('')
      toast.success('تم تحديث التعليق بنجاح!')
    } catch (error) {
      console.error('Error updating comment:', error)
      toast.error('حدث خطأ في تحديث التعليق')
    }
  }

  // حذف الإنجاز (إعادة المتطلب لحالة غير مكتمل)
  const handleUndoCompletion = async (requirement) => {
    try {
      const { data, error } = await updateServiceRequirement(requirement.id, {
        is_completed: false,
        completion_comment: null,
        completed_by: null,
        completed_at: null
      })

      if (error) throw error

      // إعادة تحميل البيانات لضمان التحديث
      if (selectedItemForDetails && currentCustomerId) {
        await loadServiceData(selectedItemForDetails.id, currentCustomerId)
      }

      toast.success('تم إلغاء الإنجاز بنجاح!')
    } catch (error) {
      console.error('Error undoing completion:', error)
      toast.error('حدث خطأ في إلغاء الإنجاز')
    }
  }

  // فتح نموذج تعديل التعليق
  const openEditCommentModal = (requirement) => {
    setEditingRequirement(requirement)
    setEditComment(requirement.completion_comment || '')
    setShowEditCommentModal(true)
  }

  // تحميل عناصر الخدمة من قاعدة البيانات
  const loadServiceItems = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('service_items')
        .select('*')
        .eq('service_id', service.id)
        .eq('is_active', true)
        .order('sort_order', { ascending: true })

      if (error) throw error
      setItems(data || [])
    } catch (error) {
      console.error('خطأ في تحميل عناصر الخدمة:', error)
      toast.error('فشل في تحميل عناصر الخدمة')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (service?.id) {
      loadServiceItems()
    }
  }, [service?.id])

  // عناصر الخدمة التجريبية (احتياطية)
  const getDemoServiceItems = (serviceId) => {
    const items = {
      'web-development': [
        {
          id: 'web-1',
          title: 'موقع شركة تجارية',
          description: 'موقع إلكتروني احترافي لشركة تجارية مع نظام إدارة المحتوى',
          status: 'completed',
          priority: 'normal',
          client: 'شركة الرياض التجارية',
          startDate: '2024-01-15',
          endDate: '2024-02-15',
          progress: 100,
          cost: 12000,
          rating: 4.8
        },
        {
          id: 'web-2',
          title: 'منصة تعليمية إلكترونية',
          description: 'منصة تعليمية متكاملة مع نظام إدارة الطلاب والمحاضرات',
          status: 'in_progress',
          priority: 'urgent',
          client: 'معهد التقنية المتقدمة',
          startDate: '2024-02-01',
          endDate: '2024-03-15',
          progress: 65,
          cost: 25000,
          rating: null
        },
        {
          id: 'web-3',
          title: 'موقع مطعم مع طلبات أونلاين',
          description: 'موقع مطعم مع نظام طلبات أونلاين ودفع إلكتروني',
          status: 'pending',
          priority: 'normal',
          client: 'مطعم الأصالة',
          startDate: '2024-03-01',
          endDate: '2024-03-30',
          progress: 0,
          cost: 8000,
          rating: null
        },
        {
          id: 'web-4',
          title: 'نظام إدارة المخزون',
          description: 'نظام ويب لإدارة المخزون والمبيعات للشركات الصغيرة',
          status: 'completed',
          priority: 'normal',
          client: 'شركة النور للتجارة',
          startDate: '2023-12-01',
          endDate: '2024-01-10',
          progress: 100,
          cost: 15000,
          rating: 4.9
        }
      ],
      'ui-ux-design': [
        {
          id: 'design-1',
          title: 'تصميم تطبيق بنكي',
          description: 'تصميم واجهات مستخدم لتطبيق بنكي محمول',
          status: 'completed',
          priority: 'urgent',
          client: 'البنك الأهلي',
          startDate: '2024-01-20',
          endDate: '2024-02-20',
          progress: 100,
          cost: 18000,
          rating: 4.7
        }
      ]
    }
    return items[serviceId] || []
  }

  // فلترة العناصر
  const filteredItems = items.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesSearch
  })

  // تحديد أول عنصر كافتراضي عند تحميل الصفحة
  useEffect(() => {
    if (filteredItems.length > 0 && !selectedItemForDetails) {
      setSelectedItemForDetails(filteredItems[0])
    }
  }, [filteredItems, selectedItemForDetails])

  // دالة لاختيار عنصر لعرض التفاصيل
  const handleCardClick = (item) => {
    setSelectedItemForDetails(item)
  }

  // لا نحتاج عرض صفحة منفصلة - سنستخدم التقسيم داخل نفس الصفحة

  return (
    <div className="space-y-6">
      {/* Header الجديد */}
      <PageHeader
        backButton={{
          text: "العودة للخدمات",
          onClick: onBack
        }}
        title={service.title}
        subtitle="عناصر ومشاريع الخدمة"
      />

      {/* المحتوى الرئيسي - شاشة مقسومة دائماً */}
      <div className="px-6 grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]">
        {/* قسم قائمة العناصر */}
        <div className="space-y-4 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">

          {/* شريط البحث والأدوات */}
          <div className="bg-white/95 backdrop-blur-sm rounded-xl shadow-sm sticky top-0 z-30 p-4 mb-4">
            <div className="flex items-center gap-3">
              {/* البحث */}
              <div className="flex-1 relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="البحث في العناصر..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* مؤشر عدد العناصر */}
              <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                <Package className="w-4 h-4" />
                <span>{filteredItems.length} عنصر</span>
              </div>
            </div>
          </div>

      {/* عرض العناصر */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-4">جاري تحميل عناصر الخدمة...</p>
        </div>
      ) : (
        // عرض الجدول
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنصر</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredItems.map((item) => (
                  <tr
                    key={item.id}
                    className={`transition-all duration-200 cursor-pointer ${
                      selectedItemForDetails?.id === item.id
                        ? 'bg-blue-50 border-l-4 border-blue-500'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => handleCardClick(item)}
                  >
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        {selectedItemForDetails?.id === item.id && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        )}
                        <div className="flex-1">
                          <div className={`text-sm font-medium ${
                            selectedItemForDetails?.id === item.id ? 'text-blue-900' : 'text-gray-900'
                          }`}>
                            {item.title}
                          </div>
                          <div className={`text-sm ${
                            selectedItemForDetails?.id === item.id ? 'text-blue-700' : 'text-gray-500'
                          }`}>
                            {item.description}
                          </div>
                        </div>
                        {selectedItemForDetails?.id === item.id && (
                          <span className="text-blue-600 text-xs font-medium flex items-center space-x-1 space-x-reverse">
                            <CheckCircle className="w-3 h-3" />
                            <span>محدد</span>
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        item.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {item.is_active ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* رسالة عدم وجود نتائج */}
      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عناصر</h3>
          <p className="text-gray-600">لا توجد عناصر تطابق معايير البحث</p>
        </div>
      )}

      </div>

        {/* قسم تفاصيل العنصر */}
        <div className="overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          {selectedItemForDetails ? (
            <div className="bg-white rounded-xl shadow-sm h-full">
              {/* رأس التفاصيل */}
              <div className="p-4 border-b border-gray-200 bg-white/95 backdrop-blur-sm sticky top-0 z-25 shadow-sm">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">{selectedItemForDetails.title}</h2>
                  <button
                    onClick={() => setSelectedItemForDetails(null)}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* التبويبات */}
            <nav className="flex border-b border-gray-200 bg-white/95 backdrop-blur-sm sticky top-16 z-20 shadow-sm">
              {[
                { id: 'required', label: 'المطلوب منك', icon: AlertCircle },
                { id: 'current', label: 'يُحدث حاليًا', icon: Activity },
                { id: 'timeline', label: 'الجدول الزمني', icon: Calendar },
                ...(isAdmin ? [{ id: 'requests', label: 'الطلبات', icon: FileText }] : [])
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium border-b-2 transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-4 h-4 ml-1" />
                  {tab.label}
                </button>
              ))}
            </nav>

            {/* محتوى التبويبات */}
            <div className="p-4 pt-6">
              {/* تبويب المطلوب منك */}
              {console.log('Active tab:', activeTab, 'Requirements count:', requirements.length)}
              {activeTab === 'required' && (
                <div className="space-y-4">
                  {/* رأس القسم مع زر الإضافة للإداري */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">المطلوب من العميل</h3>
                    {isAdmin && (
                      <button
                        onClick={() => setShowAddRequirement(true)}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        إضافة مطلب
                      </button>
                    )}
                  </div>

                  {/* نموذج إضافة مطلب جديد */}
                  {showAddRequirement && (
                    <div className="bg-blue-50 border border-blue-200 p-4 rounded-xl">
                      <div className="space-y-3">
                        <input
                          type="text"
                          placeholder="اكتب المطلب الجديد..."
                          value={newRequirement}
                          onChange={(e) => setNewRequirement(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={addRequirement}
                            className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                          >
                            إضافة
                          </button>
                          <button
                            onClick={() => {
                              setShowAddRequirement(false)
                              setNewRequirement('')
                            }}
                            className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded-lg hover:bg-gray-400 transition-colors"
                          >
                            إلغاء
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* قائمة المهام المطلوبة */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold text-gray-900">المطالب والمهام:</h4>

                    {/* المطالب المضافة من الإداري */}
                    {console.log('Rendering requirements:', requirements)}
                    {requirements.map((requirement) => (
                      <div key={requirement.id} className={`${
                        requirement.is_completed
                          ? 'bg-green-50 border border-green-200'
                          : 'bg-blue-50 border border-blue-200'
                      } rounded-lg p-3 transition-colors`}>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 space-x-reverse mb-1">
                              <h5 className="text-sm font-medium text-gray-900">{requirement.title}</h5>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                requirement.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                                requirement.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {requirement.priority === 'urgent' ? 'عاجل' :
                                 requirement.priority === 'high' ? 'مهم' :
                                 requirement.priority === 'normal' ? 'عادي' : 'منخفض'}
                              </span>
                              {requirement.is_completed && (
                                <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  مكتمل
                                </span>
                              )}
                            </div>
                            {requirement.description && (
                              <p className="text-xs text-gray-600 mb-1">{requirement.description}</p>
                            )}

                            {/* معلومات رفع الملف */}
                            {requirement.requires_file_upload && (
                              <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 mb-2">
                                <div className="flex items-center space-x-1 space-x-reverse mb-1">
                                  <FileText className="w-3 h-3 text-blue-600" />
                                  <span className="text-xs font-medium text-blue-800">يتطلب رفع ملف</span>
                                </div>
                                {requirement.file_upload_method && (
                                  <div className="space-y-1">
                                    <p className="text-xs text-blue-700">
                                      <span className="font-medium">طريقة الرفع:</span> {
                                        requirement.file_upload_method === 'whatsapp' ? 'إرسال عبر واتساب' :
                                        requirement.file_upload_method === 'link' ? 'مشاركة رابط' :
                                        'أخرى'
                                      }
                                    </p>
                                    {requirement.file_upload_details && (
                                      <p className="text-xs text-blue-600 bg-blue-100 p-1 rounded">
                                        {requirement.file_upload_details}
                                      </p>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}

                            {/* تعليق الإنجاز ورد المدير */}
                            {requirement.is_completed && (
                              <div className="space-y-2 mb-2">
                                {/* تعليق العميل */}
                                {console.log('Requirement completion data:', {
                                  id: requirement.id,
                                  title: requirement.title,
                                  is_completed: requirement.is_completed,
                                  completion_comment: requirement.completion_comment,
                                  completed_at: requirement.completed_at
                                })}
                                {requirement.completion_comment && (
                                  <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                                    <div className="flex items-center space-x-1 space-x-reverse mb-1">
                                      <MessageSquare className="w-3 h-3 text-green-600" />
                                      <span className="text-xs font-medium text-green-800">تعليقك</span>
                                    </div>
                                    <p className="text-xs text-green-700">{requirement.completion_comment}</p>
                                    {requirement.completed_at && (
                                      <p className="text-xs text-green-600 mt-1">
                                        تم الإنجاز في {new Date(requirement.completed_at).toLocaleDateString('ar-SA')}
                                      </p>
                                    )}
                                  </div>
                                )}

                                {/* رد المدير */}
                                {requirement.admin_response && (
                                  <div className={`border rounded-lg p-2 ${
                                    requirement.admin_approval_status === 'approved'
                                      ? 'bg-blue-50 border-blue-200'
                                      : 'bg-red-50 border-red-200'
                                  }`}>
                                    <div className="flex items-center space-x-1 space-x-reverse mb-1">
                                      {requirement.admin_approval_status === 'approved' ? (
                                        <CheckCircle className="w-3 h-3 text-blue-600" />
                                      ) : (
                                        <X className="w-3 h-3 text-red-600" />
                                      )}
                                      <span className={`text-xs font-medium ${
                                        requirement.admin_approval_status === 'approved'
                                          ? 'text-blue-800'
                                          : 'text-red-800'
                                      }`}>
                                        رد المدير - {requirement.admin_approval_status === 'approved' ? 'موافقة' : 'رفض'}
                                      </span>
                                    </div>
                                    <p className={`text-xs ${
                                      requirement.admin_approval_status === 'approved'
                                        ? 'text-blue-700'
                                        : 'text-red-700'
                                    }`}>
                                      {requirement.admin_response}
                                    </p>
                                    {requirement.admin_responded_at && (
                                      <p className="text-xs text-gray-500 mt-1">
                                        تم الرد في {new Date(requirement.admin_responded_at).toLocaleDateString('ar-SA')}
                                      </p>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}

                            <p className="text-xs text-gray-500">
                              تم إضافته في {new Date(requirement.created_at).toLocaleDateString('ar-SA')}
                            </p>
                          </div>
                          <div className="flex items-center space-x-1 space-x-reverse">
                            {/* أزرار العميل */}
                            {!isAdmin && (
                              <>
                                {/* زر تم الإنجاز للمتطلبات غير المكتملة */}
                                {!requirement.is_completed && (
                                  <button
                                    onClick={() => openCompletionModal(requirement)}
                                    className="px-3 py-1 bg-green-600 text-white text-xs rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-1 space-x-reverse"
                                    title="تم الإنجاز"
                                  >
                                    <CheckCircle className="w-3 h-3" />
                                    <span>تم الإنجاز</span>
                                  </button>
                                )}

                                {/* أزرار للمتطلبات المكتملة */}
                                {requirement.is_completed && (
                                  <div className="flex items-center space-x-1 space-x-reverse">
                                    {/* زر تعديل التعليق */}
                                    <button
                                      onClick={() => openEditCommentModal(requirement)}
                                      className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-lg hover:bg-blue-200 transition-colors flex items-center space-x-1 space-x-reverse"
                                      title="تعديل التعليق"
                                    >
                                      <Edit className="w-3 h-3" />
                                      <span>تعديل</span>
                                    </button>

                                    {/* زر إلغاء الإنجاز */}
                                    <button
                                      onClick={() => handleUndoCompletion(requirement)}
                                      className="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-lg hover:bg-red-200 transition-colors flex items-center space-x-1 space-x-reverse"
                                      title="إلغاء الإنجاز"
                                    >
                                      <X className="w-3 h-3" />
                                      <span>إلغاء</span>
                                    </button>

                                    {/* أيقونة الإنجاز */}
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                  </div>
                                )}
                              </>
                            )}

                            {/* أزرار الإداري */}
                            {isAdmin && (
                              <>
                                <AlertCircle className="w-4 h-4 text-blue-500" />
                                <button
                                  onClick={() => removeRequirement(requirement.id)}
                                  className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                                  title="حذف المطلب"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </button>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* المهام الافتراضية */}
                    {[
                      {
                        id: 1,
                        title: 'مراجعة وصف العنصر',
                        description: 'تأكد من أن وصف العنصر يتطابق مع احتياجاتك',
                        priority: 'normal'
                      },
                      {
                        id: 2,
                        title: 'فهم المتطلبات',
                        description: 'اقرأ المتطلبات بعناية وتأكد من قدرتك على توفيرها',
                        priority: 'urgent'
                      },
                      {
                        id: 3,
                        title: 'مراجعة المخرجات المتوقعة',
                        description: 'تأكد من أن المخرجات تلبي توقعاتك',
                        priority: 'normal'
                      }
                    ].map((task) => (
                      <div key={task.id} className="bg-white border border-gray-200 rounded-lg p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 space-x-reverse mb-1">
                              <h5 className="text-sm font-medium text-gray-900">{task.title}</h5>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                task.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {task.priority === 'urgent' ? 'مهم' : 'عادي'}
                              </span>
                            </div>
                            <p className="text-xs text-gray-600">{task.description}</p>
                          </div>
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 text-yellow-500" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* تبويب يُحدث حاليًا */}
              {activeTab === 'current' && (
                <div className="space-y-4">
                  {/* رأس القسم مع زر الإضافة للإداري */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">الأحداث والتحديثات</h3>
                    {isAdmin && (
                      <button
                        onClick={() => setShowAddAction(true)}
                        className="px-3 py-1 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
                      >
                        إضافة حدث
                      </button>
                    )}
                  </div>

                  {/* نموذج إضافة حدث جديد */}
                  {showAddAction && (
                    <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
                      <div className="space-y-3">
                        <input
                          type="text"
                          placeholder="اكتب الحدث أو الإجراء الجديد..."
                          value={newAction}
                          onChange={(e) => setNewAction(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        />
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={addCurrentAction}
                            className="px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
                          >
                            إضافة
                          </button>
                          <button
                            onClick={() => {
                              setShowAddAction(false)
                              setNewAction('')
                            }}
                            className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded-lg hover:bg-gray-400 transition-colors"
                          >
                            إلغاء
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* سلسلة الأحداث */}
                  <div className="space-y-3">
                    {currentActions.length > 0 ? (
                      currentActions.map((action, index) => (
                        <div key={action.id} className="flex items-start space-x-3 space-x-reverse">
                          {/* خط الزمن */}
                          <div className="flex flex-col items-center">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            {index < currentActions.length - 1 && (
                              <div className="w-0.5 h-8 bg-gray-300 mt-2"></div>
                            )}
                          </div>

                          {/* محتوى الحدث */}
                          <div className="flex-1 bg-white border border-gray-200 rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="text-sm font-medium text-gray-900">{action.title}</h4>
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <span className="text-xs text-gray-500">
                                  {new Date(action.created_at).toLocaleString('ar-SA')}
                                </span>
                                {isAdmin && (
                                  <button
                                    onClick={() => removeAction(action.id)}
                                    className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                                    title="حذف الحدث"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </button>
                                )}
                              </div>
                            </div>
                            <p className="text-sm text-gray-600">{action.description}</p>
                            <div className="flex items-center mt-2">
                              <Activity className="w-4 h-4 text-green-500 ml-1" />
                              <span className={`text-xs font-medium ${
                                action.action_type === 'milestone' ? 'text-blue-600' :
                                action.action_type === 'issue' ? 'text-red-600' :
                                action.action_type === 'completion' ? 'text-green-600' :
                                'text-green-600'
                              }`}>
                                {action.action_type === 'milestone' ? 'معلم مهم' :
                                 action.action_type === 'issue' ? 'مشكلة' :
                                 action.action_type === 'completion' ? 'إنجاز' :
                                 'تحديث'}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <Activity className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-base font-medium text-gray-900 mb-2">لا توجد أحداث حالياً</h3>
                        <p className="text-gray-500 text-sm">ستظهر هنا سلسلة الأحداث والتحديثات</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* تبويب الجدول الزمني */}
              {activeTab === 'timeline' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">الخطة الزمنية المتوقعة</h3>

                  {/* الجدول الزمني */}
                  <div className="space-y-4">
                    {[
                      {
                        id: 1,
                        phase: 'مرحلة التخطيط',
                        description: 'مراجعة المتطلبات ووضع الخطة التفصيلية',
                        duration: '3-5 أيام',
                        status: 'completed',
                        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                        endDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
                      },
                      {
                        id: 2,
                        phase: 'مرحلة التنفيذ',
                        description: 'البدء في تنفيذ العمل وفقاً للخطة المحددة',
                        duration: '10-15 يوم',
                        status: 'in-progress',
                        startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                        endDate: new Date(Date.now() + 13 * 24 * 60 * 60 * 1000)
                      },
                      {
                        id: 3,
                        phase: 'مرحلة المراجعة',
                        description: 'مراجعة العمل المنجز وإجراء التعديلات المطلوبة',
                        duration: '2-3 أيام',
                        status: 'pending',
                        startDate: new Date(Date.now() + 13 * 24 * 60 * 60 * 1000),
                        endDate: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000)
                      },
                      {
                        id: 4,
                        phase: 'مرحلة التسليم',
                        description: 'تسليم العمل النهائي والحصول على الموافقة',
                        duration: '1-2 يوم',
                        status: 'pending',
                        startDate: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000),
                        endDate: new Date(Date.now() + 18 * 24 * 60 * 60 * 1000)
                      }
                    ].map((phase, index) => (
                      <div key={phase.id} className="flex items-start space-x-4 space-x-reverse">
                        {/* مؤشر المرحلة */}
                        <div className="flex flex-col items-center">
                          <div className={`w-4 h-4 rounded-full ${
                            phase.status === 'completed' ? 'bg-green-500' :
                            phase.status === 'in-progress' ? 'bg-blue-500' :
                            'bg-gray-300'
                          }`}></div>
                          {index < 3 && (
                            <div className="w-0.5 h-12 bg-gray-300 mt-2"></div>
                          )}
                        </div>

                        {/* محتوى المرحلة */}
                        <div className="flex-1 bg-white border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-base font-semibold text-gray-900">{phase.phase}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              phase.status === 'completed' ? 'bg-green-100 text-green-800' :
                              phase.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {phase.status === 'completed' ? 'مكتملة' :
                               phase.status === 'in-progress' ? 'قيد التنفيذ' :
                               'في الانتظار'}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{phase.description}</p>
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <Calendar className="w-4 h-4" />
                              <span>
                                {phase.startDate.toLocaleDateString('ar-SA')} - {phase.endDate.toLocaleDateString('ar-SA')}
                              </span>
                            </div>
                            <div className="flex items-center space-x-1 space-x-reverse">
                              <Clock className="w-4 h-4" />
                              <span>{phase.duration}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* ملخص الجدول الزمني */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                    <div className="flex items-center space-x-2 space-x-reverse mb-2">
                      <Calendar className="w-5 h-5 text-blue-600" />
                      <h4 className="text-base font-semibold text-blue-900">ملخص الجدول الزمني</h4>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-blue-700 font-medium">إجمالي المدة المتوقعة:</span>
                        <span className="text-blue-900 mr-2">16-25 يوم</span>
                      </div>
                      <div>
                        <span className="text-blue-700 font-medium">تاريخ التسليم المتوقع:</span>
                        <span className="text-blue-900 mr-2">
                          {new Date(Date.now() + 18 * 24 * 60 * 60 * 1000).toLocaleDateString('ar-SA')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* تبويب الطلبات (للإداري فقط) */}
              {activeTab === 'requests' && isAdmin && (
                <div className="space-y-4">
                  <div className="text-center py-8">
                    <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-base font-medium text-gray-900 mb-2">لا توجد طلبات</h3>
                    <p className="text-gray-500 text-sm">لم يتم تقديم أي طلبات لهذا العنصر بعد</p>
                  </div>
                </div>
              )}
            </div>
          </div>
          ) : (
            /* حالة عدم تحديد عنصر */
            <div className="bg-white rounded-xl shadow-sm h-full flex items-center justify-center">
              <div className="text-center p-8">
                <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">اختر عنصراً لعرض تفاصيله</h3>
                <p className="text-gray-500 text-sm">
                  اضغط على أي عنصر من القائمة لعرض تفاصيله والمتطلبات والجدول الزمني
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* نموذج إنجاز المطلب */}
      {showCompletionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">إنجاز المطلب</h3>
                <button
                  onClick={() => setShowCompletionModal(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">{selectedRequirement?.title}</h4>
                <p className="text-sm text-gray-600">هل تم إنجاز هذا المطلب بالفعل؟</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تعليق (اختياري)
                </label>
                <textarea
                  value={completionComment}
                  onChange={(e) => setCompletionComment(e.target.value)}
                  placeholder="أضف تعليق حول إنجاز هذا المطلب..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  rows="3"
                />
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <button
                  onClick={handleCompleteRequirement}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <Send className="w-4 h-4" />
                  <span>تأكيد الإنجاز</span>
                </button>
                <button
                  onClick={() => setShowCompletionModal(false)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تعديل التعليق */}
      {showEditCommentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">تعديل التعليق</h3>
                <button
                  onClick={() => setShowEditCommentModal(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">{editingRequirement?.title}</h4>
                <p className="text-sm text-gray-600">تعديل تعليق الإنجاز</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التعليق
                </label>
                <textarea
                  value={editComment}
                  onChange={(e) => setEditComment(e.target.value)}
                  placeholder="أضف تعليق حول إنجاز هذا المطلب..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows="3"
                />
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <button
                  onClick={handleEditComment}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <Edit className="w-4 h-4" />
                  <span>حفظ التعديل</span>
                </button>
                <button
                  onClick={() => setShowEditCommentModal(false)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ServiceItemsPage
