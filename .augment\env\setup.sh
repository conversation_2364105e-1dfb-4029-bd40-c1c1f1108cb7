#!/bin/bash

# Update system packages
sudo apt-get update

# Install Node.js 18.x (LTS)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm installation
node --version
npm --version

# Navigate to workspace directory
cd /mnt/persist/workspace

# Install project dependencies
npm install

# Create .env file from .env.example if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
fi

# Add npm global bin to PATH in user profile
echo 'export PATH="$HOME/.npm-global/bin:$PATH"' >> $HOME/.profile

# Source the profile to make PATH available immediately
source $HOME/.profile

echo "Setup completed successfully!"
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "Project dependencies installed"