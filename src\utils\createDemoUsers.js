import { supabase } from '../lib/supabase'

// بيانات المستخدمين التجريبيين
const demoUsers = [
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'شركة نماء للخدمات الرقمية',
    organization_name: 'شركة نماء للخدمات الرقمية',
    membership_level: 'premium',
    logo: '🏢'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'مؤسسة روافد التنمية',
    organization_name: 'مؤسسة روافد التنمية',
    membership_level: 'gold',
    logo: '🌱'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'جمعية نور الخيرية',
    organization_name: 'جمعية نور الخيرية',
    membership_level: 'basic',
    logo: '💡'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'جمعية ديم الخيرية',
    organization_name: 'جمعية ديم الخيرية',
    membership_level: 'gold',
    logo: '🌟'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    name: 'جمعية جمان الخيرية',
    organization_name: 'جمعية جمان الخيرية',
    membership_level: 'basic',
    logo: '🌸'
  }
]

// دالة لإنشاء المستخدمين التجريبيين
export const createDemoUsers = async () => {
  console.log('🔧 بدء إنشاء المستخدمين التجريبيين...')
  
  const results = []
  
  for (const userData of demoUsers) {
    try {
      console.log(`📝 إنشاء مستخدم: ${userData.email}`)
      
      // إنشاء المستخدم في نظام المصادقة باستخدام signUp
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            name: userData.name,
            organization_name: userData.organization_name
          }
        }
      })
      
      if (authError) {
        console.error(`❌ خطأ في إنشاء مستخدم ${userData.email}:`, authError)
        results.push({ email: userData.email, success: false, error: authError.message })
        continue
      }
      
      console.log(`✅ تم إنشاء مستخدم في Auth: ${userData.email}`)
      
      // إضافة المستخدم إلى جدول المستخدمين العام
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: userData.email,
          name: userData.name,
          organization_name: userData.organization_name,
          membership_level: userData.membership_level,
          logo: userData.logo,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
      
      if (profileError) {
        console.error(`❌ خطأ في إنشاء ملف تعريف ${userData.email}:`, profileError)
        results.push({ email: userData.email, success: false, error: profileError.message })
        continue
      }
      
      console.log(`✅ تم إنشاء ملف تعريف: ${userData.email}`)
      results.push({ email: userData.email, success: true })
      
    } catch (error) {
      console.error(`❌ خطأ عام في إنشاء ${userData.email}:`, error)
      results.push({ email: userData.email, success: false, error: error.message })
    }
  }
  
  console.log('🎯 انتهاء إنشاء المستخدمين:', results)
  return results
}

// دالة للتحقق من المستخدمين الموجودين
export const checkExistingUsers = async () => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('email, name, membership_level')
    
    if (error) {
      console.error('خطأ في جلب المستخدمين:', error)
      return []
    }
    
    console.log('👥 المستخدمين الموجودين:', data)
    return data
  } catch (error) {
    console.error('خطأ في التحقق من المستخدمين:', error)
    return []
  }
}
