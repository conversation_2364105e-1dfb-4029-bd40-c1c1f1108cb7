import React, { useState, useEffect } from 'react'
import { 
  CheckCircle, 
  FileText, 
  MessageSquare,
  Clock,
  AlertCircle,
  X,
  Send
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const CustomerRequirements = ({ serviceItem, customer }) => {
  console.log('🔍 CustomerRequirements - Props received:', { serviceItem, customer })

  const [requirements, setRequirements] = useState([])
  const [loading, setLoading] = useState(false)
  const [showCompletionModal, setShowCompletionModal] = useState(false)
  const [selectedRequirement, setSelectedRequirement] = useState(null)
  const [completionComment, setCompletionComment] = useState('')

  // تحميل المتطلبات
  const loadRequirements = async () => {
    console.log('🔄 loadRequirements called with:', {
      serviceItem: serviceItem?.id,
      customer: customer?.id
    })

    if (!serviceItem || !customer) {
      console.log('❌ Missing serviceItem or customer:', { serviceItem, customer })
      return
    }

    try {
      setLoading(true)
      console.log('📡 Fetching requirements from database...')

      const { data, error } = await supabase
        .from('service_requirements')
        .select('*')
        .eq('service_item_id', serviceItem.id)
        .eq('customer_id', customer.id)
        .order('created_at', { ascending: false })

      console.log('📊 Database response:', { data, error })

      if (error) throw error

      console.log('✅ Requirements loaded:', data?.length || 0, 'items')
      setRequirements(data || [])
    } catch (error) {
      console.error('❌ Error loading requirements:', error)
      toast.error('حدث خطأ في تحميل المتطلبات')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRequirements()
  }, [serviceItem, customer])

  // إنجاز المتطلب
  const handleCompleteRequirement = async () => {
    if (!selectedRequirement) return

    try {
      const user = (await supabase.auth.getUser()).data.user

      const { data, error } = await supabase
        .from('service_requirements')
        .update({
          is_completed: true,
          completion_comment: completionComment.trim(),
          completed_by: user?.id,
          completed_at: new Date().toISOString()
        })
        .eq('id', selectedRequirement.id)
        .select()

      if (error) throw error

      // تحديث القائمة
      setRequirements(requirements.map(req =>
        req.id === selectedRequirement.id ? { ...req, ...data[0] } : req
      ))

      setShowCompletionModal(false)
      setSelectedRequirement(null)
      setCompletionComment('')
      toast.success('تم إنجاز المطلب بنجاح')
    } catch (error) {
      console.error('Error completing requirement:', error)
      toast.error('حدث خطأ في إنجاز المطلب')
    }
  }

  // فتح نموذج الإنجاز
  const openCompletionModal = (requirement) => {
    setSelectedRequirement(requirement)
    setShowCompletionModal(true)
    setCompletionComment('')
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-gray-500 mt-2 text-sm">جاري تحميل المتطلبات...</p>
      </div>
    )
  }

  console.log('🎨 Rendering CustomerRequirements with:', {
    requirementsCount: requirements.length,
    loading,
    requirements: requirements.map(r => ({ id: r.id, title: r.title, is_completed: r.is_completed }))
  })

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-semibold text-gray-900">
          المطلوب منك ({requirements.filter(req => !req.is_completed).length})
        </h4>
        <span className="text-sm text-gray-500">
          {requirements.filter(req => req.is_completed).length} مكتمل من {requirements.length}
        </span>
      </div>

      {requirements.length > 0 ? (
        <div className="space-y-3">
          {requirements.map((requirement) => (
            <div 
              key={requirement.id} 
              className={`bg-white border rounded-xl p-4 ${
                requirement.is_completed 
                  ? 'border-green-200 bg-green-50' 
                  : 'border-gray-200 hover:border-blue-300'
              } transition-colors`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                    <h5 className="font-medium text-gray-900">{requirement.title}</h5>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      requirement.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                      requirement.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                      requirement.priority === 'normal' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {requirement.priority === 'urgent' ? 'عاجل' :
                       requirement.priority === 'high' ? 'مهم' :
                       requirement.priority === 'normal' ? 'عادي' : 'منخفض'}
                    </span>
                    {requirement.is_completed && (
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        مكتمل
                      </span>
                    )}
                  </div>

                  {requirement.description && (
                    <p className="text-sm text-gray-600 mb-3">{requirement.description}</p>
                  )}

                  {/* معلومات رفع الملف */}
                  {requirement.requires_file_upload && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        <FileText className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800">يتطلب رفع ملف</span>
                      </div>
                      {requirement.file_upload_method && (
                        <div className="space-y-1">
                          <p className="text-sm text-blue-700">
                            <span className="font-medium">طريقة الرفع:</span> {
                              requirement.file_upload_method === 'whatsapp' ? 'إرسال عبر واتساب' :
                              requirement.file_upload_method === 'link' ? 'مشاركة رابط' :
                              'أخرى'
                            }
                          </p>
                          {requirement.file_upload_details && (
                            <p className="text-sm text-blue-600 bg-blue-100 p-2 rounded">
                              {requirement.file_upload_details}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {/* تعليق الإنجاز */}
                  {requirement.is_completed && requirement.completion_comment && (
                    <div className="bg-green-100 border border-green-200 rounded-lg p-3 mb-3">
                      <div className="flex items-center space-x-2 space-x-reverse mb-1">
                        <MessageSquare className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">تعليقك</span>
                      </div>
                      <p className="text-sm text-green-700">{requirement.completion_comment}</p>
                      {requirement.completed_at && (
                        <p className="text-xs text-green-600 mt-1">
                          تم الإنجاز في {new Date(requirement.completed_at).toLocaleDateString('ar-SA')}
                        </p>
                      )}
                    </div>
                  )}

                  <p className="text-xs text-gray-500">
                    تم إضافته في {new Date(requirement.created_at).toLocaleDateString('ar-SA')}
                  </p>
                </div>

                {/* زر الإنجاز */}
                {(() => {
                  console.log(`🔘 Button for requirement ${requirement.id}:`, {
                    title: requirement.title,
                    is_completed: requirement.is_completed,
                    shouldShowButton: !requirement.is_completed
                  })
                  return !requirement.is_completed && (
                    <button
                      onClick={() => {
                        console.log('🖱️ Button clicked for requirement:', requirement.title)
                        openCompletionModal(requirement)
                      }}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 space-x-reverse"
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span>تم الإنجاز</span>
                    </button>
                  )
                })()}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <AlertCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد متطلبات</h3>
          <p className="text-gray-500">لم يتم إضافة أي متطلبات لهذه الخدمة بعد</p>
        </div>
      )}

      {/* نموذج إنجاز المطلب */}
      {showCompletionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">إنجاز المطلب</h3>
                <button
                  onClick={() => setShowCompletionModal(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">{selectedRequirement?.title}</h4>
                <p className="text-sm text-gray-600">{selectedRequirement?.description}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تعليق (اختياري)
                </label>
                <textarea
                  value={completionComment}
                  onChange={(e) => setCompletionComment(e.target.value)}
                  placeholder="أضف تعليق حول إنجاز هذا المطلب..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  rows="3"
                />
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <button
                  onClick={handleCompleteRequirement}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <Send className="w-4 h-4" />
                  <span>تأكيد الإنجاز</span>
                </button>
                <button
                  onClick={() => setShowCompletionModal(false)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CustomerRequirements
