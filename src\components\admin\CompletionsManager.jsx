import React, { useState, useEffect } from 'react'
import {
  CheckCircle,
  MessageSquare,
  Clock,
  User,
  AlertCircle,
  ThumbsUp,
  ThumbsDown,
  Edit,
  Send,
  Bell,
  Calendar,
  FileText
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const CompletionsManager = () => {
  const [completions, setCompletions] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedCompletion, setSelectedCompletion] = useState(null)
  const [adminResponse, setAdminResponse] = useState('')
  const [showResponseModal, setShowResponseModal] = useState(false)
  const [filter, setFilter] = useState('pending') // pending, approved, rejected, all

  // تحميل الإنجازات
  const loadCompletions = async () => {
    try {
      setLoading(true)
      
      let query = supabase
        .from('service_requirements')
        .select(`
          *,
          users!service_requirements_customer_id_fkey (
            id,
            name,
            email,
            organization_name
          ),
          service_items (
            id,
            title,
            services (
              id,
              title
            )
          )
        `)
        .eq('is_completed', true)
        .order('completed_at', { ascending: false })

      // تطبيق الفلتر
      if (filter === 'pending') {
        query = query.is('admin_response', null)
      } else if (filter === 'approved') {
        query = query.eq('admin_approval_status', 'approved')
      } else if (filter === 'rejected') {
        query = query.eq('admin_approval_status', 'rejected')
      }

      const { data, error } = await query

      if (error) throw error
      setCompletions(data || [])
    } catch (error) {
      console.error('Error loading completions:', error)
      toast.error('حدث خطأ في تحميل الإنجازات')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadCompletions()
  }, [filter])

  // الرد على الإنجاز
  const handleAdminResponse = async (status) => {
    if (!selectedCompletion) return

    try {
      const { error } = await supabase
        .from('service_requirements')
        .update({
          admin_response: adminResponse.trim(),
          admin_approval_status: status,
          admin_responded_at: new Date().toISOString(),
          admin_responded_by: (await supabase.auth.getUser()).data.user?.id
        })
        .eq('id', selectedCompletion.id)

      if (error) throw error

      // تحديث القائمة
      await loadCompletions()
      
      setShowResponseModal(false)
      setSelectedCompletion(null)
      setAdminResponse('')
      
      const statusText = status === 'approved' ? 'موافقة' : 'رفض'
      toast.success(`تم ${statusText} الإنجاز بنجاح!`)
    } catch (error) {
      console.error('Error responding to completion:', error)
      toast.error('حدث خطأ في الرد على الإنجاز')
    }
  }

  // فتح نموذج الرد
  const openResponseModal = (completion) => {
    setSelectedCompletion(completion)
    setAdminResponse('')
    setShowResponseModal(true)
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-gray-500 mt-4">جاري تحميل الإنجازات...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة الإنجازات</h2>
          <p className="text-gray-600">مراجعة والرد على إنجازات العملاء</p>
        </div>
        
        {/* إحصائيات سريعة */}
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
            {completions.filter(c => !c.admin_response).length} في الانتظار
          </div>
          <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
            {completions.filter(c => c.admin_approval_status === 'approved').length} موافق عليها
          </div>
        </div>
      </div>

      {/* فلاتر */}
      <div className="flex items-center space-x-2 space-x-reverse">
        {[
          { id: 'pending', label: 'في الانتظار', icon: Clock },
          { id: 'approved', label: 'موافق عليها', icon: ThumbsUp },
          { id: 'rejected', label: 'مرفوضة', icon: ThumbsDown },
          { id: 'all', label: 'الكل', icon: FileText }
        ].map((filterOption) => (
          <button
            key={filterOption.id}
            onClick={() => setFilter(filterOption.id)}
            className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === filterOption.id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <filterOption.icon className="w-4 h-4" />
            <span>{filterOption.label}</span>
          </button>
        ))}
      </div>

      {/* قائمة الإنجازات */}
      <div className="space-y-4">
        {completions.length > 0 ? (
          completions.map((completion) => (
            <div
              key={completion.id}
              className={`bg-white border rounded-xl p-6 ${
                !completion.admin_response
                  ? 'border-yellow-200 bg-yellow-50'
                  : completion.admin_approval_status === 'approved'
                  ? 'border-green-200 bg-green-50'
                  : 'border-red-200 bg-red-50'
              } transition-colors`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* معلومات المتطلب */}
                  <div className="flex items-center space-x-3 space-x-reverse mb-3">
                    <CheckCircle className={`w-5 h-5 ${
                      completion.admin_approval_status === 'approved' ? 'text-green-600' :
                      completion.admin_approval_status === 'rejected' ? 'text-red-600' :
                      'text-yellow-600'
                    }`} />
                    <h3 className="text-lg font-semibold text-gray-900">{completion.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      completion.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                      completion.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {completion.priority === 'urgent' ? 'عاجل' :
                       completion.priority === 'high' ? 'مهم' : 'عادي'}
                    </span>
                  </div>

                  {/* معلومات العميل والخدمة */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <User className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-700">
                        {completion.users?.name || completion.users?.organization_name}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <FileText className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-700">
                        {completion.service_items?.services?.title} - {completion.service_items?.title}
                      </span>
                    </div>
                  </div>

                  {/* تعليق العميل */}
                  {completion.completion_comment && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        <MessageSquare className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800">تعليق العميل</span>
                      </div>
                      <p className="text-sm text-blue-700">{completion.completion_comment}</p>
                    </div>
                  )}

                  {/* رد المدير */}
                  {completion.admin_response && (
                    <div className={`border rounded-lg p-3 mb-4 ${
                      completion.admin_approval_status === 'approved'
                        ? 'bg-green-50 border-green-200'
                        : 'bg-red-50 border-red-200'
                    }`}>
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        {completion.admin_approval_status === 'approved' ? (
                          <ThumbsUp className="w-4 h-4 text-green-600" />
                        ) : (
                          <ThumbsDown className="w-4 h-4 text-red-600" />
                        )}
                        <span className={`text-sm font-medium ${
                          completion.admin_approval_status === 'approved'
                            ? 'text-green-800'
                            : 'text-red-800'
                        }`}>
                          رد المدير - {completion.admin_approval_status === 'approved' ? 'موافقة' : 'رفض'}
                        </span>
                      </div>
                      <p className={`text-sm ${
                        completion.admin_approval_status === 'approved'
                          ? 'text-green-700'
                          : 'text-red-700'
                      }`}>
                        {completion.admin_response}
                      </p>
                      {completion.admin_responded_at && (
                        <p className="text-xs text-gray-500 mt-2">
                          تم الرد في {new Date(completion.admin_responded_at).toLocaleDateString('ar-SA')}
                        </p>
                      )}
                    </div>
                  )}

                  {/* تاريخ الإنجاز */}
                  <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                    <Calendar className="w-4 h-4" />
                    <span>تم الإنجاز في {new Date(completion.completed_at).toLocaleDateString('ar-SA')}</span>
                  </div>
                </div>

                {/* أزرار الإجراءات */}
                {!completion.admin_response && (
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button
                      onClick={() => openResponseModal(completion)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 space-x-reverse"
                    >
                      <MessageSquare className="w-4 h-4" />
                      <span>رد</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-12">
            <CheckCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد إنجازات</h3>
            <p className="text-gray-500">لا توجد إنجازات تطابق الفلتر المحدد</p>
          </div>
        )}
      </div>

      {/* نموذج الرد */}
      {showResponseModal && selectedCompletion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-lg w-full">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                الرد على إنجاز: {selectedCompletion.title}
              </h3>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رد المدير
                </label>
                <textarea
                  value={adminResponse}
                  onChange={(e) => setAdminResponse(e.target.value)}
                  placeholder="اكتب ردك على إنجاز العميل..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows="4"
                />
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <button
                  onClick={() => handleAdminResponse('approved')}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <ThumbsUp className="w-4 h-4" />
                  <span>موافقة</span>
                </button>
                <button
                  onClick={() => handleAdminResponse('rejected')}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <ThumbsDown className="w-4 h-4" />
                  <span>رفض</span>
                </button>
                <button
                  onClick={() => setShowResponseModal(false)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CompletionsManager
