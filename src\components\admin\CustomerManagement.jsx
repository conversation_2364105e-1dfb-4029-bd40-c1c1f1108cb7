import React, { useState, useEffect } from 'react'
import { 
  Users, 
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  Mail,
  Phone,
  Calendar,
  Building,
  User,
  ChevronRight,
  MoreVertical,
  UserCheck,
  UserX
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const CustomerManagement = ({ onCustomerSelect, selectedCustomer }) => {
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all') // all, active, inactive
  const [sortBy, setSortBy] = useState('created_at') // created_at, name, email
  const [sortOrder, setSortOrder] = useState('desc') // asc, desc

  // جلب العملاء من قاعدة البيانات
  const loadCustomers = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('account_type', 'customer')
        .order(sortBy, { ascending: sortOrder === 'asc' })

      if (error) throw error
      setCustomers(data || [])
    } catch (error) {
      console.error('Error loading customers:', error)
      toast.error('حدث خطأ في تحميل العملاء')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadCustomers()
  }, [sortBy, sortOrder])

  // فلترة العملاء
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = 
      customer.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.organization_name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = 
      filterStatus === 'all' || 
      (filterStatus === 'active' && customer.is_active) ||
      (filterStatus === 'inactive' && !customer.is_active)

    return matchesSearch && matchesStatus
  })

  // تبديل حالة العميل
  const toggleCustomerStatus = async (customerId, currentStatus) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: !currentStatus })
        .eq('id', customerId)

      if (error) throw error

      setCustomers(customers.map(customer => 
        customer.id === customerId 
          ? { ...customer, is_active: !currentStatus }
          : customer
      ))

      toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} العميل بنجاح`)
    } catch (error) {
      console.error('Error updating customer status:', error)
      toast.error('حدث خطأ في تحديث حالة العميل')
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-full flex flex-col">
      {/* Header مضغوط */}
      <div className="p-3 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-base font-semibold text-gray-900">العملاء</h3>
            <p className="text-xs text-gray-500">
              {filteredCustomers.length} عميل
            </p>
          </div>
          <button className="btn btn-primary btn-xs">
            <Plus className="w-3 h-3 ml-1" />
            إضافة
          </button>
        </div>

        {/* البحث فقط */}
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
          <input
            type="text"
            placeholder="البحث..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pr-8 pl-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-xs"
          />
        </div>
      </div>

      {/* قائمة العملاء */}
      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-4">جاري تحميل العملاء...</p>
          </div>
        ) : filteredCustomers.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {filteredCustomers.map((customer) => (
              <div
                key={customer.id}
                className={`p-4 hover:bg-gray-50 cursor-pointer transition-all duration-200 ${
                  selectedCustomer?.id === customer.id ? 'bg-blue-50 border-r-4 border-blue-500' : ''
                }`}
                onClick={() => onCustomerSelect && onCustomerSelect(customer)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    {/* معلومات العميل مضغوطة */}
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-xs font-semibold text-gray-900 truncate">
                          {customer.full_name || 'بدون اسم'}
                        </h4>
                        <p className="text-xs text-gray-500 truncate">
                          {customer.organization_name || customer.email}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* حالة وإجراءات */}
                  <div className="flex items-center space-x-1 space-x-reverse">
                    {/* حالة العميل */}
                    <div className={`w-2 h-2 rounded-full ${
                      customer.is_active ? 'bg-green-500' : 'bg-red-500'
                    }`} title={customer.is_active ? 'نشط' : 'غير نشط'}></div>

                    {/* زر الاختيار */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        onCustomerSelect && onCustomerSelect(customer)
                      }}
                      className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded transition-colors"
                      title="اختيار العميل"
                    >
                      <ChevronRight className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-8 text-center">
            <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">لا توجد عملاء</h4>
            <p className="text-gray-500 text-sm">
              {searchTerm ? 'لا توجد نتائج تطابق البحث' : 'لم يتم إضافة أي عملاء بعد'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default CustomerManagement
