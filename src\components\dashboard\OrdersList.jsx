import React from 'react'
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Calendar,
  DollarSign,
  User,
  ChevronLeft,
  Activity,
  Zap,
  TrendingUp
} from 'lucide-react'

const OrdersList = ({ orders = [], onOrderClick, compact = false, showCustomer = false }) => {
  if (orders.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Clock className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات</h3>
        <p className="text-gray-500">لم يتم العثور على أي طلبات حتى الآن</p>
      </div>
    )
  }

  const getStatusConfig = (status) => {
    switch (status) {
      case 'completed':
        return {
          icon: CheckCircle,
          label: 'مكتمل',
          color: 'bg-green-100 text-green-800 border-green-200',
          iconColor: 'text-green-600',
          bgColor: 'bg-green-50'
        }
      case 'in_progress':
        return {
          icon: Activity,
          label: 'قيد التنفيذ',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-50'
        }
      case 'pending':
        return {
          icon: Clock,
          label: 'في الانتظار',
          color: 'bg-orange-100 text-orange-800 border-orange-200',
          iconColor: 'text-orange-600',
          bgColor: 'bg-orange-50'
        }
      case 'cancelled':
        return {
          icon: AlertCircle,
          label: 'ملغي',
          color: 'bg-red-100 text-red-800 border-red-200',
          iconColor: 'text-red-600',
          bgColor: 'bg-red-50'
        }
      default:
        return {
          icon: Clock,
          label: 'غير محدد',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          iconColor: 'text-gray-600',
          bgColor: 'bg-gray-50'
        }
    }
  }

  const getPriorityConfig = (priority) => {
    switch (priority) {
      case 'urgent':
        return { color: 'bg-red-100 text-red-800', icon: '🔴', label: 'عاجل' }
      case 'normal':
        return { color: 'bg-blue-100 text-blue-800', icon: '🔵', label: 'عادي' }
      case 'low':
        return { color: 'bg-gray-100 text-gray-800', icon: '⚪', label: 'منخفض' }
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: '⚪', label: 'غير محدد' }
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const calculateProgress = (order) => {
    if (order.status === 'completed') return 100
    if (order.status === 'in_progress') return 65
    if (order.status === 'pending') return 25
    return 0
  }

  const getServiceIcon = (category) => {
    switch (category) {
      case 'تطوير ويب':
        return '🌐'
      case 'تطبيقات الجوال':
        return '📱'
      case 'أنظمة إدارية':
        return '⚙️'
      case 'تصميم':
        return '🎨'
      default:
        return '📋'
    }
  }

  return (
    <div className={compact ? "space-y-3" : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"}>
      {orders.map((order) => {
        const statusConfig = getStatusConfig(order.status)
        const priorityConfig = getPriorityConfig(order.priority)
        const progress = calculateProgress(order)
        const StatusIcon = statusConfig.icon
        const serviceIcon = getServiceIcon(order.category)

        return (
          <div
            key={order.id}
            onClick={() => onOrderClick && onOrderClick(order)}
            className={`card card-hover cursor-pointer ${
              compact ? 'p-3' : 'p-4 h-full flex flex-col'
            }`}
          >
            <div className={`flex items-start justify-between ${compact ? '' : 'h-full'}`}>
              <div className={`flex-1 ${compact ? '' : 'flex flex-col h-full'}`}>
                {/* الهيدر المدمج */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${statusConfig.bgColor}`}>
                      <span className="text-lg">{serviceIcon}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <h3 className={`font-semibold text-gray-900 ${compact ? 'text-sm' : 'text-base'}`}>
                          {order.title}
                        </h3>
                        <span className={`badge ${
                          order.status === 'completed' ? 'badge-success' :
                          order.status === 'in_progress' ? 'badge-primary' :
                          order.status === 'pending' ? 'badge-warning' :
                          'badge-gray'
                        }`}>
                          <StatusIcon className="w-3 h-3 ml-1" />
                          {statusConfig.label}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse mt-1">
                        <p className="text-xs text-gray-500">#{order.order_number}</p>
                        {order.priority && (
                          <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
                            order.priority === 'urgent' ? 'bg-red-100 text-red-700' :
                            order.priority === 'normal' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {priorityConfig.icon} {priorityConfig.label}
                          </span>
                        )}
                      </div>

                      {/* عرض اسم العميل للإداري */}
                      {showCustomer && order.users && (
                        <div className="mt-2 flex items-center space-x-2 space-x-reverse">
                          <User className="w-4 h-4 text-blue-500" />
                          <span className="text-sm font-medium text-blue-600">
                            {order.users.name || order.users.organization_name}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* السهم */}
                  {onOrderClick && (
                    <div className="flex items-center">
                      <ChevronLeft className="w-5 h-5 text-gray-400" />
                    </div>
                  )}
                </div>

                {/* شريط التقدم المدمج */}
                <div className="mb-3">
                  <div className="flex justify-between items-center mb-1.5">
                    <span className="text-xs text-gray-600 font-medium">التقدم</span>
                    <span className={`text-xs font-semibold ${statusConfig.iconColor}`}>{progress}%</span>
                  </div>
                  <div className="progress h-1.5">
                    <div
                      className={`progress-bar ${
                        order.status === 'completed' ? 'bg-green-500' :
                        order.status === 'in_progress' ? 'bg-blue-500' :
                        'bg-orange-500'
                      }`}
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* المعلومات المدمجة */}
                <div className={`grid grid-cols-3 gap-2 ${compact ? '' : 'mt-auto'}`}>
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1 space-x-reverse">
                      <Calendar className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-600">{formatDate(order.created_at)}</span>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1 space-x-reverse">
                      <DollarSign className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-600">{order.estimated_cost?.toLocaleString()} ريال</span>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1 space-x-reverse">
                      <Clock className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-600">{order.estimated_duration} يوم</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default OrdersList
