import React, { useState, useEffect } from 'react'
import {
  Users,
  Search,
  Plus,
  Edit,
  Trash2,
  Mail,
  Phone,
  Calendar,
  Shield,
  User,
  Building,
  Activity,
  Filter,
  Link,
  Unlink,
  Settings,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import useStore from '../../store/useStore'
import useSidebar from '../../hooks/useSidebar'
import PageHeader from '../layout/PageHeader'
import AddCustomerModal from './AddCustomerModal'
import EditCustomerModal from './EditCustomerModal'
import toast from 'react-hot-toast'

const CustomersPage = () => {
  const { user } = useStore()
  const { isCollapsed, gridCols } = useSidebar()
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all') // all, admin, customer
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState(null)
  const [services, setServices] = useState([])
  const [userServices, setUserServices] = useState([])

  // التحقق من الصلاحيات الإدارية
  const isAdmin = user?.account_type === 'admin' || user?.email === '<EMAIL>'

  // تحميل العملاء من قاعدة البيانات
  const loadCustomers = async () => {
    try {
      setLoading(true)
      
      // تحميل جميع المستخدمين
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })

      if (usersError) throw usersError

      // تحميل إحصائيات الخدمات لكل عميل
      const usersWithStats = await Promise.all(
        (usersData || []).map(async (customer) => {
          // عدد الخدمات المشتركة
          const { count: servicesCount } = await supabase
            .from('user_services')
            .select('*', { count: 'exact' })
            .eq('user_id', customer.id)
            .eq('is_active', true)

          return {
            ...customer,
            servicesCount: servicesCount || 0
          }
        })
      )

      setCustomers(usersWithStats)
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error)
      toast.error('فشل في تحميل العملاء')
    } finally {
      setLoading(false)
    }
  }

  // تحميل الخدمات
  const loadServices = async () => {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('is_active', true)
        .order('title')

      if (error) throw error
      setServices(data || [])
    } catch (error) {
      console.error('خطأ في تحميل الخدمات:', error)
      toast.error('فشل في تحميل الخدمات')
    }
  }

  // تحميل خدمات العميل
  const loadUserServices = async (customerId) => {
    try {
      const { data, error } = await supabase
        .from('user_services')
        .select(`
          *,
          services (*)
        `)
        .eq('user_id', customerId)
        .eq('is_active', true)

      if (error) throw error
      setUserServices(data || [])
    } catch (error) {
      console.error('خطأ في تحميل خدمات العميل:', error)
      toast.error('فشل في تحميل خدمات العميل')
    }
  }

  // ربط خدمة بعميل
  const linkServiceToCustomer = async (customerId, serviceId) => {
    try {
      // التحقق من وجود الربط مسبقاً
      const { data: existing } = await supabase
        .from('user_services')
        .select('*')
        .eq('user_id', customerId)
        .eq('service_id', serviceId)
        .single()

      if (existing) {
        toast.error('هذه الخدمة مربوطة بالعميل مسبقاً')
        return
      }

      // إنشاء ربط جديد
      const { error } = await supabase
        .from('user_services')
        .insert({
          user_id: customerId,
          service_id: serviceId,
          is_active: true,
          created_at: new Date().toISOString()
        })

      if (error) throw error

      toast.success('تم ربط الخدمة بالعميل بنجاح!')
      if (selectedCustomer?.id === customerId) {
        loadUserServices(customerId)
      }
      loadCustomers() // لتحديث عدد الخدمات
    } catch (error) {
      console.error('خطأ في ربط الخدمة:', error)
      toast.error('فشل في ربط الخدمة بالعميل')
    }
  }

  // فك ربط خدمة من عميل
  const unlinkServiceFromCustomer = async (customerId, serviceId) => {
    if (!window.confirm('هل أنت متأكد من فك ربط هذه الخدمة من العميل؟')) {
      return
    }

    try {
      const { error } = await supabase
        .from('user_services')
        .delete()
        .eq('user_id', customerId)
        .eq('service_id', serviceId)

      if (error) throw error

      toast.success('تم فك ربط الخدمة من العميل بنجاح!')
      if (selectedCustomer?.id === customerId) {
        loadUserServices(customerId)
      }
      loadCustomers() // لتحديث عدد الخدمات
    } catch (error) {
      console.error('خطأ في فك ربط الخدمة:', error)
      toast.error('فشل في فك ربط الخدمة من العميل')
    }
  }

  // التحقق من ربط الخدمة
  const isServiceLinked = (serviceId) => {
    return userServices.some(us => us.service_id === serviceId)
  }

  // اختيار العميل لعرض التفاصيل
  const selectCustomer = async (customer) => {
    setSelectedCustomer(customer)
    if (customer && customer.account_type !== 'admin') {
      await loadUserServices(customer.id)
    } else {
      setUserServices([])
    }
  }

  useEffect(() => {
    if (isAdmin) {
      loadCustomers()
      loadServices()
    }
  }, [isAdmin])

  // فلترة العملاء
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = 
      customer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone?.includes(searchTerm)

    const matchesFilter = 
      filterType === 'all' ||
      (filterType === 'admin' && customer.account_type === 'admin') ||
      (filterType === 'customer' && customer.account_type !== 'admin')

    return matchesSearch && matchesFilter
  })

  // حذف عميل
  const handleDeleteCustomer = async (customerId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا العميل؟ سيتم حذف جميع بياناته المرتبطة.')) {
      return
    }

    try {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', customerId)

      if (error) throw error

      toast.success('تم حذف العميل بنجاح!')
      loadCustomers()
    } catch (error) {
      console.error('خطأ في حذف العميل:', error)
      toast.error('فشل في حذف العميل')
    }
  }

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد'
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // الحصول على أيقونة نوع الحساب
  const getAccountTypeIcon = (accountType) => {
    return accountType === 'admin' ? Shield : User
  }

  // الحصول على لون نوع الحساب
  const getAccountTypeColor = (accountType) => {
    return accountType === 'admin' ? 'text-purple-600 bg-purple-100' : 'text-blue-600 bg-blue-100'
  }

  // الحصول على نص نوع الحساب
  const getAccountTypeText = (accountType) => {
    return accountType === 'admin' ? 'مدير' : 'عميل'
  }

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Shield className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">غير مصرح</h2>
          <p className="text-gray-600">ليس لديك صلاحية للوصول لهذه الصفحة</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <PageHeader
        title="إدارة العملاء"
        subtitle="عرض وإدارة جميع العملاء والمستخدمين في النظام"
      />

      {/* المحتوى الرئيسي */}
      <div className="px-6 space-y-6">
        {/* شريط البحث والفلاتر */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* البحث */}
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث في العملاء (الاسم، الإيميل، الهاتف)..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* فلتر نوع الحساب */}
            <div className="flex items-center space-x-3 space-x-reverse">
              <Filter className="w-5 h-5 text-gray-500" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الأنواع</option>
                <option value="customer">العملاء</option>
                <option value="admin">المديرين</option>
              </select>
            </div>

            {/* زر إضافة عميل */}
            <button
              onClick={() => setShowAddModal(true)}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="w-5 h-5" />
              <span>إضافة عميل</span>
            </button>
          </div>
        </div>

        {/* التخطيط المقسوم: قائمة العملاء + تفاصيل العميل */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6" style={{ height: 'calc(100vh - 20rem)' }}>

          {/* القسم الأيسر: قائمة العملاء المضغوطة */}
          <div className="bg-white rounded-xl shadow-sm flex flex-col h-full">
            <div className="p-6 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">قائمة العملاء</h3>
                <span className="text-sm text-gray-500">({filteredCustomers.length} عميل)</span>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-4">جاري تحميل العملاء...</p>
                </div>
              ) : filteredCustomers.length > 0 ? (
                <div className="space-y-2 p-4">
                  {filteredCustomers.map((customer) => {
                    const AccountIcon = getAccountTypeIcon(customer.account_type)
                    const isSelected = selectedCustomer?.id === customer.id

                    return (
                      <div
                        key={customer.id}
                        onClick={() => selectCustomer(customer)}
                        className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50 shadow-md'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        {/* السطر الأول: الاسم والنوع */}
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3 space-x-reverse">
                            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getAccountTypeColor(customer.account_type)}`}>
                              <AccountIcon className="w-4 h-4" />
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900 truncate max-w-[200px]">
                                {customer.name || 'غير محدد'}
                              </h4>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className={`text-xs px-2 py-1 rounded-full ${getAccountTypeColor(customer.account_type)}`}>
                              {getAccountTypeText(customer.account_type)}
                            </span>
                          </div>
                        </div>

                        {/* السطر الثاني: الإيميل والإحصائيات */}
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Mail className="w-4 h-4" />
                            <span className="truncate max-w-[180px]">{customer.email || 'غير محدد'}</span>
                          </div>

                          {customer.account_type !== 'admin' && (
                            <div className="flex items-center space-x-1 space-x-reverse">
                              <Activity className="w-4 h-4" />
                              <span>{customer.servicesCount || 0}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عملاء</h3>
                  <p className="text-gray-500 mb-4">
                    {searchTerm || filterType !== 'all'
                      ? 'لا توجد نتائج تطابق البحث أو الفلتر المحدد'
                      : 'لم يتم إضافة أي عملاء بعد'
                    }
                  </p>
                  {!searchTerm && filterType === 'all' && (
                    <button
                      onClick={() => setShowAddModal(true)}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      إضافة أول عميل
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* القسم الأيمن: تفاصيل العميل المختار */}
          <div className="bg-white rounded-xl shadow-sm flex flex-col h-full">
            {selectedCustomer ? (
              <>
                {/* رأس تفاصيل العميل */}
                <div className="p-6 border-b border-gray-200 flex-shrink-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${getAccountTypeColor(selectedCustomer.account_type)}`}>
                        {React.createElement(getAccountTypeIcon(selectedCustomer.account_type), { className: "w-6 h-6" })}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{selectedCustomer.name}</h3>
                        <p className="text-sm text-gray-500">{selectedCustomer.email}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => {
                          setShowEditModal(true)
                        }}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="تعديل العميل"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCustomer(selectedCustomer.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="حذف العميل"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* محتوى تفاصيل العميل */}
                <div className="flex-1 overflow-y-auto p-6 space-y-6">
                  {/* معلومات العميل */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">معلومات العميل</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3 space-x-reverse text-gray-600">
                          <Mail className="w-4 h-4" />
                          <span className="text-sm">{selectedCustomer.email || 'غير محدد'}</span>
                        </div>
                        {selectedCustomer.phone && (
                          <div className="flex items-center space-x-3 space-x-reverse text-gray-600">
                            <Phone className="w-4 h-4" />
                            <span className="text-sm">{selectedCustomer.phone}</span>
                          </div>
                        )}
                        {selectedCustomer.contact_person && (
                          <div className="flex items-center space-x-3 space-x-reverse text-gray-600">
                            <Building className="w-4 h-4" />
                            <span className="text-sm">{selectedCustomer.contact_person}</span>
                          </div>
                        )}
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center space-x-3 space-x-reverse text-gray-600">
                          <Calendar className="w-4 h-4" />
                          <span className="text-sm">انضم في {formatDate(selectedCustomer.created_at)}</span>
                        </div>
                        <div className="flex items-center space-x-3 space-x-reverse text-gray-600">
                          <Shield className="w-4 h-4" />
                          <span className="text-sm">{getAccountTypeText(selectedCustomer.account_type)}</span>
                        </div>
                        {selectedCustomer.account_type !== 'admin' && (
                          <div className="flex items-center space-x-3 space-x-reverse text-gray-600">
                            <Activity className="w-4 h-4" />
                            <span className="text-sm">
                              {selectedCustomer.servicesCount > 0
                                ? `${selectedCustomer.servicesCount} خدمة نشطة`
                                : 'لا توجد خدمات مشتركة'
                              }
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* جدول إدارة الخدمات */}
                  {selectedCustomer.account_type !== 'admin' && (
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">إدارة الخدمات</h4>
                      <div className="border border-gray-200 rounded-lg overflow-hidden">
                        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                          <div className="grid grid-cols-3 gap-4 text-sm font-medium text-gray-700">
                            <span>اسم الخدمة</span>
                            <span>الحالة</span>
                            <span>الإجراء</span>
                          </div>
                        </div>

                        <div className="max-h-[300px] overflow-y-auto">
                          {services.map(service => {
                            const isLinked = isServiceLinked(service.id)
                            return (
                              <div key={service.id} className="px-4 py-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50">
                                <div className="grid grid-cols-3 gap-4 items-center">
                                  <div>
                                    <h5 className="font-medium text-gray-900">{service.title}</h5>
                                    <p className="text-sm text-gray-500 truncate">{service.description}</p>
                                  </div>

                                  <div className="flex items-center space-x-2 space-x-reverse">
                                    <div className={`w-3 h-3 rounded-full ${
                                      isLinked ? 'bg-green-500' : 'bg-gray-300'
                                    }`}></div>
                                    <span className={`text-sm ${
                                      isLinked ? 'text-green-700' : 'text-gray-500'
                                    }`}>
                                      {isLinked ? 'مربوطة' : 'غير مربوطة'}
                                    </span>
                                  </div>

                                  <div>
                                    <button
                                      onClick={() => isLinked
                                        ? unlinkServiceFromCustomer(selectedCustomer.id, service.id)
                                        : linkServiceToCustomer(selectedCustomer.id, service.id)
                                      }
                                      className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2 space-x-reverse ${
                                        isLinked
                                          ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                          : 'bg-green-100 text-green-700 hover:bg-green-200'
                                      }`}
                                    >
                                      {isLinked ? (
                                        <>
                                          <Unlink className="w-4 h-4" />
                                          <span>فك</span>
                                        </>
                                      ) : (
                                        <>
                                          <Link className="w-4 h-4" />
                                          <span>ربط</span>
                                        </>
                                      )}
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>

                        {services.length === 0 && (
                          <div className="text-center py-8">
                            <Activity className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                            <p className="text-gray-500">لا توجد خدمات متاحة</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center h-full min-h-[400px]">
                <div className="text-center">
                  <User className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">اختر عميل</h3>
                  <p className="text-gray-500">اختر عميل من القائمة لعرض تفاصيله وإدارة خدماته</p>
                </div>
              </div>
            )}
          </div>
        </div>


      </div>

      {/* نموذج إضافة عميل */}
      <AddCustomerModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={loadCustomers}
      />

      {/* نموذج تعديل عميل */}
      <EditCustomerModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false)
          setSelectedCustomer(null)
        }}
        customer={selectedCustomer}
        onSuccess={loadCustomers}
      />
    </div>
  )
}

export default CustomersPage
