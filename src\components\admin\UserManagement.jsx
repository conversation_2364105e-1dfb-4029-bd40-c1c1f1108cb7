import React, { useState, useEffect } from 'react'
import { 
  Users, 
  Plus, 
  Edit, 
  Key, 
  Mail, 
  Phone, 
  MapPin,
  Building,
  User,
  Save,
  X,
  Eye,
  EyeOff,
  Shield,
  Search,
  Filter
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const UserManagement = () => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterLevel, setFilterLevel] = useState('all')

  // بيانات إنشاء مستخدم جديد
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    name: '',
    organization_name: '',
    contact_person: '',
    phone: '',
    address: '',
    membership_level: 'basic',
    account_type: 'customer',
    website: '',
    logo: '🏢'
  })

  // بيانات تغيير كلمة المرور
  const [passwordData, setPasswordData] = useState({
    newPassword: '',
    confirmPassword: ''
  })

  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  useEffect(() => {
    loadUsers()
  }, [])

  // تحميل جميع المستخدمين
  const loadUsers = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setUsers(data || [])
    } catch (error) {
      console.error('خطأ في تحميل المستخدمين:', error)
      toast.error('حدث خطأ في تحميل المستخدمين')
    } finally {
      setLoading(false)
    }
  }

  // إنشاء مستخدم جديد
  const handleCreateUser = async (e) => {
    e.preventDefault()
    
    if (!newUser.email || !newUser.password || !newUser.name) {
      toast.error('يرجى ملء الحقول المطلوبة')
      return
    }

    if (newUser.password.length < 6) {
      toast.error('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      return
    }

    setLoading(true)
    try {
      // إنشاء المستخدم باستخدام signUp (لا يتطلب Service Key)
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: newUser.email,
        password: newUser.password,
        options: {
          data: {
            name: newUser.name,
            organization_name: newUser.organization_name
          }
        }
      })

      if (authError) {
        console.error('Auth error:', authError)
        throw new Error('فشل في إنشاء حساب المصادقة: ' + authError.message)
      }

      if (!authData.user) {
        throw new Error('لم يتم إنشاء المستخدم بشكل صحيح')
      }

      const userId = authData.user.id

      // إنشاء بروفايل المستخدم
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: userId,
          email: newUser.email,
          name: newUser.name,
          organization_name: newUser.organization_name || newUser.name,
          contact_person: newUser.contact_person,
          phone: newUser.phone,
          address: newUser.address,
          membership_level: newUser.membership_level,
          website: newUser.website,
          logo: newUser.logo,
          account_type: newUser.account_type, // تعيين نوع الحساب
          preferences: {
            notifications: true,
            language: 'ar'
          }
        })

      if (profileError) {
        console.error('Profile error:', profileError)
        // إذا فشل إنشاء البروفايل، احذف المستخدم من auth
        await supabase.from('auth.users').delete().eq('id', userId)
        throw new Error('فشل في إنشاء بروفايل المستخدم')
      }

      toast.success('تم إنشاء المستخدم بنجاح!')
      setShowCreateModal(false)
      setNewUser({
        email: '',
        password: '',
        name: '',
        organization_name: '',
        contact_person: '',
        phone: '',
        address: '',
        membership_level: 'basic',
        account_type: 'customer',
        website: '',
        logo: '🏢'
      })
      loadUsers()

    } catch (error) {
      console.error('خطأ في إنشاء المستخدم:', error)
      toast.error('حدث خطأ في إنشاء المستخدم: ' + (error.message || 'خطأ غير معروف'))
    } finally {
      setLoading(false)
    }
  }

  // تغيير كلمة المرور
  const handleChangePassword = async (e) => {
    e.preventDefault()

    if (!passwordData.newPassword || !passwordData.confirmPassword) {
      toast.error('يرجى ملء جميع الحقول')
      return
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('كلمة المرور وتأكيدها غير متطابقتين')
      return
    }

    if (passwordData.newPassword.length < 6) {
      toast.error('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      return
    }

    setLoading(true)
    try {
      // تحديث كلمة المرور باستخدام RPC function
      const { error } = await supabase.rpc('update_user_password', {
        user_id: selectedUser.id,
        new_password: passwordData.newPassword
      })

      if (error) {
        console.error('Password update error:', error)
        throw new Error('فشل في تحديث كلمة المرور: ' + error.message)
      }

      toast.success('تم تغيير كلمة المرور بنجاح!')
      setShowPasswordModal(false)
      setPasswordData({ newPassword: '', confirmPassword: '' })
      setSelectedUser(null)

    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error)
      toast.error('حدث خطأ في تغيير كلمة المرور: ' + (error.message || 'خطأ غير معروف'))
    } finally {
      setLoading(false)
    }
  }

  // فلترة المستخدمين
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.organization_name?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesLevel = filterLevel === 'all' || user.membership_level === filterLevel
    
    return matchesSearch && matchesLevel
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة المستخدمين</h2>
          <p className="text-gray-600 mt-1">إنشاء وإدارة حسابات العملاء</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="btn btn-primary flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          إنشاء مستخدم جديد
        </button>
      </div>

      {/* Search and Filter */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="relative">
            <Search className="w-5 h-5 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في المستخدمين..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pr-10"
            />
          </div>
          <select
            value={filterLevel}
            onChange={(e) => setFilterLevel(e.target.value)}
            className="input"
          >
            <option value="all">جميع المستويات</option>
            <option value="basic">أساسي</option>
            <option value="gold">ذهبي</option>
            <option value="premium">بريميوم</option>
          </select>
        </div>
        <button
          onClick={loadUsers}
          disabled={loading}
          className="btn btn-ghost btn-sm"
        >
          تحديث
        </button>
      </div>

      {/* Users Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستخدم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المؤسسة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستوى
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الإنشاء
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <span className="text-lg">{user.logo || '👤'}</span>
                        </div>
                      </div>
                      <div className="mr-4">
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{user.organization_name}</div>
                    <div className="text-sm text-gray-500">{user.contact_person}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`badge ${
                      user.membership_level === 'premium' ? 'badge-success' :
                      user.membership_level === 'gold' ? 'badge-warning' :
                      'badge-gray'
                    }`}>
                      {user.membership_level === 'premium' ? 'بريميوم' :
                       user.membership_level === 'gold' ? 'ذهبي' :
                       'أساسي'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(user.created_at).toLocaleDateString('ar-SA')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => {
                        setSelectedUser(user)
                        setShowPasswordModal(true)
                      }}
                      className="text-blue-600 hover:text-blue-900 ml-3"
                      title="تغيير كلمة المرور"
                    >
                      <Key className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create User Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">إنشاء مستخدم جديد</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <form onSubmit={handleCreateUser} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني *
                  </label>
                  <input
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                    className="input"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور *
                  </label>
                  <input
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                    className="input"
                    placeholder="••••••••"
                    required
                    minLength={6}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اسم المؤسسة *
                  </label>
                  <input
                    type="text"
                    value={newUser.name}
                    onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                    className="input"
                    placeholder="اسم المؤسسة"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الشخص المسؤول
                  </label>
                  <input
                    type="text"
                    value={newUser.contact_person}
                    onChange={(e) => setNewUser({...newUser, contact_person: e.target.value})}
                    className="input"
                    placeholder="اسم المسؤول"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الهاتف
                  </label>
                  <input
                    type="tel"
                    value={newUser.phone}
                    onChange={(e) => setNewUser({...newUser, phone: e.target.value})}
                    className="input"
                    placeholder="+966501234567"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    مستوى العضوية
                  </label>
                  <select
                    value={newUser.membership_level}
                    onChange={(e) => setNewUser({...newUser, membership_level: e.target.value})}
                    className="input"
                  >
                    <option value="basic">أساسي</option>
                    <option value="gold">ذهبي</option>
                    <option value="premium">بريميوم</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  العنوان
                </label>
                <textarea
                  value={newUser.address}
                  onChange={(e) => setNewUser({...newUser, address: e.target.value})}
                  className="input"
                  rows={2}
                  placeholder="عنوان المؤسسة"
                />
              </div>

              <div className="flex justify-end space-x-3 space-x-reverse pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="btn btn-secondary"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="btn btn-primary"
                >
                  {loading ? 'جاري الإنشاء...' : 'إنشاء المستخدم'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Change Password Modal */}
      {showPasswordModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">تغيير كلمة المرور</h3>
              <button
                onClick={() => setShowPasswordModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">المستخدم:</p>
              <p className="font-medium">{selectedUser.name}</p>
              <p className="text-sm text-gray-500">{selectedUser.email}</p>
            </div>

            <form onSubmit={handleChangePassword} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور الجديدة
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    value={passwordData.newPassword}
                    onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                    className="input pr-10"
                    placeholder="••••••••"
                    required
                    minLength={6}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تأكيد كلمة المرور
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    value={passwordData.confirmPassword}
                    onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                    className="input pr-10"
                    placeholder="••••••••"
                    required
                    minLength={6}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  >
                    {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              <div className="flex justify-end space-x-3 space-x-reverse pt-4">
                <button
                  type="button"
                  onClick={() => setShowPasswordModal(false)}
                  className="btn btn-secondary"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="btn btn-primary"
                >
                  {loading ? 'جاري التحديث...' : 'تغيير كلمة المرور'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserManagement
