@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* إصلاح مشكلة text-size-adjust للتوافق مع جميع المتصفحات */
html,
:host {
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* إعدادات عامة للعربية */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  background-color: #f9fafb;
}

/* تحسينات للنصوص العربية */
.arabic-text {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, Helvetica, sans-serif;
  line-height: 1.6;
  letter-spacing: 0.02em;
}

/* تحسينات للأزرار */
.btn-primary {
  @apply bg-gradient-to-r from-nama-blue-600 to-nama-blue-700 text-white px-6 py-3 rounded-lg hover:from-nama-blue-700 hover:to-nama-blue-800 transition-all duration-200 font-medium;
}

.btn-secondary {
  @apply bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-all duration-200 font-medium;
}

/* تحسينات للكروت */
.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300;
}

.card-header {
  @apply p-6 border-b border-gray-200;
}

.card-body {
  @apply p-6;
}

/* تحسينات للنماذج */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nama-blue-500 focus:border-transparent transition-all duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* تحسينات للحالات */
.status-urgent {
  @apply bg-red-50 border-red-200 text-red-800;
}

.status-normal {
  @apply bg-blue-50 border-blue-200 text-blue-800;
}

.status-completed {
  @apply bg-green-50 border-green-200 text-green-800;
}

/* تحسينات للانيميشن */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات للسكرول */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* تحسينات للطباعة */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100% !important;
  }
}

/* تحسينات للتركيز */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-nama-blue-500 focus:ring-offset-2;
}

/* تحسينات للنصوص */
.text-gradient {
  background: linear-gradient(135deg, #1e3a8a 0%, #14b8a6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تحسينات للظلال */
.shadow-nama {
  box-shadow: 0 10px 25px -5px rgba(30, 58, 138, 0.1), 0 10px 10px -5px rgba(30, 58, 138, 0.04);
}

.shadow-nama-lg {
  box-shadow: 0 20px 25px -5px rgba(30, 58, 138, 0.1), 0 10px 10px -5px rgba(30, 58, 138, 0.04);
}
