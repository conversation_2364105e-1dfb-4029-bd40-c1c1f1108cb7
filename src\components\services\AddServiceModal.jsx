import React, { useState } from 'react'
import {
  X,
  Plus,
  Save,
  Globe,
  Palette,
  Smartphone,
  ShoppingCart,
  BarChart3,
  Shield,
  FileText,
  Upload,
  Image
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const AddServiceModal = ({ isOpen, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    base_price: '',
    duration_weeks: '',
    icon: 'Globe',
    color: 'blue',
    is_active: true
  })

  // قائمة الأيقونات المتاحة
  const availableIcons = [
    { name: 'Globe', icon: Globe, label: 'موقع إلكتروني' },
    { name: 'Palette', icon: Palette, label: 'تصميم' },
    { name: 'Smartphone', icon: Smartphone, label: 'تطبيق جوال' },
    { name: 'ShoppingCart', icon: ShoppingCart, label: 'متجر إلكتروني' },
    { name: 'BarChart3', icon: BarChart3, label: 'تسويق' },
    { name: 'Shield', icon: Shield, label: 'أمان' },
    { name: 'FileText', icon: FileText, label: 'مستندات' }
  ]

  // قائمة الألوان المتاحة
  const availableColors = [
    { name: 'blue', label: 'أزرق', class: 'bg-blue-500' },
    { name: 'purple', label: 'بنفسجي', class: 'bg-purple-500' },
    { name: 'green', label: 'أخضر', class: 'bg-green-500' },
    { name: 'orange', label: 'برتقالي', class: 'bg-orange-500' },
    { name: 'pink', label: 'وردي', class: 'bg-pink-500' },
    { name: 'red', label: 'أحمر', class: 'bg-red-500' }
  ]



  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    // التحقق من البيانات المطلوبة
    if (!formData.title.trim() || !formData.description.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    try {
      setLoading(true)

      // إعداد البيانات للإرسال
      const serviceData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: 'general', // قيمة افتراضية
        base_price: formData.base_price ? parseInt(formData.base_price) : null,
        duration_weeks: formData.duration_weeks ? parseInt(formData.duration_weeks) : null,
        icon: formData.icon,
        color: formData.color,
        is_active: formData.is_active,
        created_at: new Date().toISOString()
      }

      // إضافة الخدمة إلى قاعدة البيانات
      const { data, error } = await supabase
        .from('services')
        .insert([serviceData])
        .select()
        .single()

      if (error) throw error

      toast.success('تم إضافة الخدمة بنجاح!')
      
      // إعادة تعيين النموذج
      setFormData({
        title: '',
        description: '',
        base_price: '',
        duration_weeks: '',
        icon: 'Globe',
        color: 'blue',
        is_active: true
      })

      // إغلاق النموذج وتحديث القائمة
      onSuccess && onSuccess(data)
      onClose()

    } catch (error) {
      console.error('خطأ في إضافة الخدمة:', error)
      toast.error('فشل في إضافة الخدمة')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[85vh] overflow-hidden">
        {/* رأس النموذج */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
              <Plus className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">إضافة خدمة جديدة</h2>
              <p className="text-sm text-gray-500">أضف خدمة جديدة إلى معرض الخدمات</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* محتوى النموذج */}
        <div className="p-4 overflow-y-auto max-h-[calc(75vh-120px)]">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* المعلومات الأساسية */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* عنوان الخدمة */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الخدمة *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="مثال: تطوير موقع إلكتروني احترافي"
                  required
                />
              </div>

              {/* الوصف */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف الخدمة *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="وصف مفصل للخدمة وما تشمله..."
                  required
                />
              </div>



              {/* السعر الأساسي */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  السعر الأساسي (ريال)
                </label>
                <input
                  type="number"
                  name="base_price"
                  value={formData.base_price}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="5000"
                  min="0"
                />
              </div>

              {/* المدة بالأسابيع */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المدة المتوقعة (أسابيع)
                </label>
                <input
                  type="number"
                  name="duration_weeks"
                  value={formData.duration_weeks}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="4"
                  min="1"
                />
              </div>

              {/* الأيقونة واللون */}
              <div className="md:col-span-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* الأيقونة */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      أيقونة الخدمة
                    </label>
                    <div className="grid grid-cols-4 gap-2">
                      {availableIcons.map(iconItem => {
                        const IconComponent = iconItem.icon
                        return (
                          <button
                            key={iconItem.name}
                            type="button"
                            onClick={() => setFormData(prev => ({ ...prev, icon: iconItem.name }))}
                            className={`p-2 rounded-lg border-2 transition-all ${
                              formData.icon === iconItem.name
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            title={iconItem.label}
                          >
                            <IconComponent className="w-4 h-4 mx-auto" />
                          </button>
                        )
                      })}
                    </div>
                  </div>

                  {/* اللون */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون الخدمة
                    </label>
                    <div className="grid grid-cols-6 gap-2">
                      {availableColors.map(colorItem => (
                        <button
                          key={colorItem.name}
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, color: colorItem.name }))}
                          className={`w-8 h-8 rounded-lg ${colorItem.class} border-2 transition-all ${
                            formData.color === colorItem.name
                              ? 'border-gray-800 scale-110'
                              : 'border-gray-300 hover:scale-105'
                          }`}
                          title={colorItem.label}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>



            {/* حالة الخدمة */}
            <div className="flex items-center space-x-3 space-x-reverse p-4 bg-gray-50 rounded-lg">
              <input
                type="checkbox"
                name="is_active"
                id="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                تفعيل الخدمة (ستظهر للعملاء)
              </label>
            </div>
          </form>
        </div>

        {/* أزرار التحكم */}
        <div className="flex items-center justify-end space-x-3 space-x-reverse p-4 border-t border-gray-200 bg-gray-50">
          <button
            type="button"
            onClick={onClose}
            className="px-6 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            إلغاء
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2 space-x-reverse"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>جاري الحفظ...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>حفظ الخدمة</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default AddServiceModal
