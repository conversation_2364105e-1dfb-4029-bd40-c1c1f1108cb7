import React, { useState } from 'react'
import { 
  Plus, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  User,
  Calendar,
  MoreHorizontal,
  Edit,
  Trash2,
  Flag
} from 'lucide-react'

const OrderTasks = ({ orderId }) => {
  const [showAddTask, setShowAddTask] = useState(false)

  // بيانات تجريبية للمهام
  const tasks = [
    {
      id: 1,
      title: 'تحليل المتطلبات',
      description: 'دراسة وتحليل متطلبات المشروع بالتفصيل',
      status: 'completed',
      priority: 'urgent',
      assigned_to: 'أحمد محمد',
      progress_percentage: 100,
      estimated_hours: 8,
      actual_hours: 6,
      start_date: '2024-06-16',
      due_date: '2024-06-17',
      completion_date: '2024-06-17',
      created_at: '2024-06-15T10:00:00Z'
    },
    {
      id: 2,
      title: 'تصميم واجهة المستخدم',
      description: 'إنشاء تصاميم أولية لواجهة المستخدم',
      status: 'in_progress',
      priority: 'normal',
      assigned_to: 'فاطمة علي',
      progress_percentage: 75,
      estimated_hours: 16,
      actual_hours: 12,
      start_date: '2024-06-18',
      due_date: '2024-06-22',
      completion_date: null,
      created_at: '2024-06-17T14:00:00Z'
    },
    {
      id: 3,
      title: 'تطوير قاعدة البيانات',
      description: 'إنشاء هيكل قاعدة البيانات والجداول المطلوبة',
      status: 'pending',
      priority: 'normal',
      assigned_to: 'محمد أحمد',
      progress_percentage: 0,
      estimated_hours: 12,
      actual_hours: 0,
      start_date: '2024-06-20',
      due_date: '2024-06-25',
      completion_date: null,
      created_at: '2024-06-17T16:00:00Z'
    },
    {
      id: 4,
      title: 'اختبار النظام',
      description: 'إجراء اختبارات شاملة للنظام',
      status: 'pending',
      priority: 'low',
      assigned_to: 'سارة محمد',
      progress_percentage: 0,
      estimated_hours: 20,
      actual_hours: 0,
      start_date: '2024-06-26',
      due_date: '2024-06-30',
      completion_date: null,
      created_at: '2024-06-17T18:00:00Z'
    }
  ]

  // دالة لتحديد لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // دالة لتحديد أيقونة الحالة
  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'in_progress':
        return <AlertCircle className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // دالة لتحديد نص الحالة
  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'in_progress':
        return 'قيد التنفيذ'
      case 'completed':
        return 'مكتملة'
      case 'cancelled':
        return 'ملغية'
      default:
        return 'غير محدد'
    }
  }

  // دالة لتحديد لون الأولوية
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600'
      case 'normal':
        return 'text-blue-600'
      case 'low':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  // دالة لتنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد'
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA')
  }

  // حساب الإحصائيات
  const stats = {
    total: tasks.length,
    completed: tasks.filter(t => t.status === 'completed').length,
    inProgress: tasks.filter(t => t.status === 'in_progress').length,
    pending: tasks.filter(t => t.status === 'pending').length,
    totalHours: tasks.reduce((sum, t) => sum + (t.actual_hours || 0), 0),
    estimatedHours: tasks.reduce((sum, t) => sum + (t.estimated_hours || 0), 0)
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات المهام */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-sm text-gray-600">إجمالي المهام</div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          <div className="text-sm text-gray-600">مكتملة</div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
          <div className="text-sm text-gray-600">قيد التنفيذ</div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          <div className="text-sm text-gray-600">في الانتظار</div>
        </div>
      </div>

      {/* قائمة المهام */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              مهام المشروع
            </h2>
            <button
              onClick={() => setShowAddTask(true)}
              className="btn-primary flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="w-4 h-4" />
              <span>إضافة مهمة</span>
            </button>
          </div>
        </div>

        <div className="divide-y divide-gray-200">
          {tasks.map((task) => (
            <div key={task.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* عنوان المهمة والحالة */}
                  <div className="flex items-center space-x-3 space-x-reverse mb-2">
                    <h3 className="text-lg font-medium text-gray-900">
                      {task.title}
                    </h3>
                    <span className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {getStatusIcon(task.status)}
                      <span>{getStatusText(task.status)}</span>
                    </span>
                    <span className={`text-xs font-medium ${getPriorityColor(task.priority)}`}>
                      {task.priority === 'urgent' ? '🔴 عاجل' :
                       task.priority === 'normal' ? '🔵 عادي' :
                       '⚪ منخفض'}
                    </span>
                  </div>

                  {/* وصف المهمة */}
                  <p className="text-gray-600 mb-3">
                    {task.description}
                  </p>

                  {/* تفاصيل المهمة */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">المسؤول:</span>
                      <div className="flex items-center space-x-1 space-x-reverse mt-1">
                        <User className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{task.assigned_to}</span>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">تاريخ البداية:</span>
                      <div className="flex items-center space-x-1 space-x-reverse mt-1">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{formatDate(task.start_date)}</span>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">تاريخ الاستحقاق:</span>
                      <div className="flex items-center space-x-1 space-x-reverse mt-1">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{formatDate(task.due_date)}</span>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">الساعات:</span>
                      <div className="mt-1">
                        <span className="text-gray-900">
                          {task.actual_hours} / {task.estimated_hours} ساعة
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* شريط التقدم */}
                  {task.status === 'in_progress' && (
                    <div className="mt-4">
                      <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                        <span>التقدم</span>
                        <span>{task.progress_percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${task.progress_percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <MoreHorizontal className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* ملخص الساعات */}
        <div className="p-6 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">إجمالي الساعات المنجزة:</span>
            <span className="font-semibold text-gray-900">{stats.totalHours} ساعة</span>
          </div>
          <div className="flex items-center justify-between text-sm mt-1">
            <span className="text-gray-600">إجمالي الساعات المقدرة:</span>
            <span className="font-semibold text-gray-900">{stats.estimatedHours} ساعة</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OrderTasks
