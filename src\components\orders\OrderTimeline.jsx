import React, { useState, useEffect } from 'react'
import {
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  User,
  Calendar,
  MessageSquare,
  FileText,
  DollarSign,
  Edit,
  Upload,
  Download
} from 'lucide-react'
import useStore from '../../store/useStore'
import toast from 'react-hot-toast'

const OrderTimeline = ({ order }) => {
  const [filterType, setFilterType] = useState('all')
  const [timelineEvents, setTimelineEvents] = useState([])
  const [loading, setLoading] = useState(false)
  const [showAddEventModal, setShowAddEventModal] = useState(false)
  const [newEventData, setNewEventData] = useState({
    title: '',
    description: '',
    type: 'comment'
  })
  const { loadOrderTasks, loadOrderCommunications, addTimelineEvent } = useStore()
  // تحميل بيانات الجدول الزمني
  useEffect(() => {
    if (order?.id) {
      loadTimelineData()
    }
  }, [order?.id])

  const loadTimelineData = async () => {
    setLoading(true)
    try {
      // إنشاء أحداث الجدول الزمني من بيانات الطلب
      const events = []

      // حدث إنشاء الطلب
      events.push({
        id: 'created',
        type: 'created',
        title: 'تم إنشاء الطلب',
        description: 'تم إنشاء الطلب بواسطة العميل',
        timestamp: order.created_at,
        user: order.users?.name || 'العميل',
        icon: FileText,
        color: 'blue'
      })

      // أحداث تغيير الحالة
      if (order.status === 'in_progress' || order.status === 'completed') {
        events.push({
          id: 'accepted',
          type: 'status_change',
          title: 'تم قبول الطلب',
          description: 'تم مراجعة الطلب وقبوله للتنفيذ',
          timestamp: order.updated_at,
          user: 'فريق نماء',
          icon: CheckCircle,
          color: 'green'
        })
      }

      if (order.status === 'in_progress') {
        events.push({
          id: 'started',
          type: 'status_change',
          title: 'بدء التنفيذ',
          description: 'تم بدء العمل على الطلب',
          timestamp: order.start_date || order.updated_at,
          user: 'فريق التطوير',
          icon: AlertCircle,
          color: 'yellow'
        })
      }

      if (order.status === 'completed') {
        events.push({
          id: 'completed',
          type: 'status_change',
          title: 'تم إكمال الطلب',
          description: 'تم إنجاز جميع متطلبات الطلب بنجاح',
          timestamp: order.completion_date || order.updated_at,
          user: 'فريق نماء',
          icon: CheckCircle,
          color: 'green'
        })
      }

      // أحداث الدفع
      if (order.payment_status === 'paid' || order.payment_status === 'partial') {
        events.push({
          id: 'payment',
          type: 'payment',
          title: order.payment_status === 'paid' ? 'تم الدفع كاملاً' : 'دفعة جزئية',
          description: `تم استلام ${order.payment_status === 'paid' ? 'كامل' : 'جزء من'} المبلغ المطلوب`,
          timestamp: order.updated_at,
          user: 'قسم المالية',
          icon: DollarSign,
          color: 'green'
        })
      }

      // تحميل الأحداث الحقيقية من قاعدة البيانات
      const realEvents = await loadOrderCommunications(order.id)

      // تحويل الأحداث الحقيقية لتنسيق الجدول الزمني
      realEvents.forEach(comm => {
        // تحديد نوع الحدث الأصلي من metadata أو subject
        const originalType = comm.metadata?.original_type ||
                           (comm.subject?.includes('تعليق') ? 'comment' :
                            comm.subject?.includes('حالة') ? 'status_change' :
                            comm.subject?.includes('ملف') ? 'file_upload' :
                            comm.subject?.includes('دفع') ? 'payment' : 'comment')

        events.push({
          id: comm.id,
          type: originalType,
          title: comm.subject,
          description: comm.content,
          timestamp: comm.created_at,
          user: comm.sender_name || 'فريق نماء',
          icon: originalType === 'comment' ? MessageSquare :
                originalType === 'file_upload' ? Upload :
                originalType === 'status_change' ? CheckCircle :
                originalType === 'payment' ? DollarSign :
                MessageSquare,
          color: originalType === 'comment' ? 'gray' :
                 originalType === 'file_upload' ? 'purple' :
                 originalType === 'status_change' ? 'green' :
                 originalType === 'payment' ? 'green' :
                 'blue'
        })
      })

      // ترتيب الأحداث حسب التاريخ (الأحدث أولاً)
      events.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))

      setTimelineEvents(events)
    } catch (error) {
      console.error('خطأ في تحميل بيانات الجدول الزمني:', error)
    } finally {
      setLoading(false)
    }
  }

  // بيانات تجريبية إضافية للعرض
  const demoEvents = [
    {
      id: 1,
      type: 'created',
      title: 'تم إنشاء الطلب',
      description: 'تم إنشاء الطلب بواسطة العميل',
      timestamp: order.created_at,
      user: 'العميل',
      icon: FileText,
      color: 'blue'
    },
    {
      id: 2,
      type: 'status_change',
      title: 'تم قبول الطلب',
      description: 'تم مراجعة الطلب وقبوله للتنفيذ',
      timestamp: '2024-06-16T10:30:00Z',
      user: 'فريق نماء',
      icon: CheckCircle,
      color: 'green'
    },
    {
      id: 3,
      type: 'comment',
      title: 'تعليق جديد',
      description: 'تم إضافة تعليق حول متطلبات إضافية',
      timestamp: '2024-06-17T14:15:00Z',
      user: 'أحمد محمد',
      icon: MessageSquare,
      color: 'gray'
    },
    {
      id: 4,
      type: 'status_change',
      title: 'بدء التنفيذ',
      description: 'تم بدء العمل على الطلب',
      timestamp: '2024-06-18T09:00:00Z',
      user: 'فريق التطوير',
      icon: AlertCircle,
      color: 'yellow'
    },
    {
      id: 5,
      type: 'file_upload',
      title: 'رفع ملفات',
      description: 'تم رفع المخططات الأولية للمراجعة',
      timestamp: '2024-06-20T16:45:00Z',
      user: 'فريق التصميم',
      icon: FileText,
      color: 'purple'
    },
    {
      id: 6,
      type: 'payment',
      title: 'دفعة مقدمة',
      description: 'تم استلام الدفعة المقدمة بقيمة 7,500 ريال',
      timestamp: '2024-06-21T11:20:00Z',
      user: 'قسم المالية',
      icon: DollarSign,
      color: 'green'
    },
    {
      id: 7,
      type: 'status_change',
      title: 'مراجعة المرحلة الأولى',
      description: 'تم الانتهاء من المرحلة الأولى وإرسالها للمراجعة',
      timestamp: '2024-06-23T13:30:00Z',
      user: 'فريق التطوير',
      icon: CheckCircle,
      color: 'blue'
    },
    {
      id: 8,
      type: 'comment',
      title: 'ملاحظات العميل',
      description: 'تم استلام ملاحظات العميل على المرحلة الأولى',
      timestamp: '2024-06-24T10:15:00Z',
      user: 'العميل',
      icon: MessageSquare,
      color: 'yellow'
    },
    {
      id: 9,
      type: 'status_change',
      title: 'تطبيق التعديلات',
      description: 'تم تطبيق التعديلات المطلوبة من العميل',
      timestamp: '2024-06-25T15:45:00Z',
      user: 'فريق التطوير',
      icon: Edit,
      color: 'purple'
    }
  ]

  // دالة لتنسيق التاريخ
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // دالة لتحديد ألوان الأيقونة
  const getIconColor = (color) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-100 text-blue-600'
      case 'green':
        return 'bg-green-100 text-green-600'
      case 'yellow':
        return 'bg-yellow-100 text-yellow-600'
      case 'red':
        return 'bg-red-100 text-red-600'
      case 'purple':
        return 'bg-purple-100 text-purple-600'
      default:
        return 'bg-gray-100 text-gray-600'
    }
  }

  // دالة إضافة حدث جديد
  const handleAddEvent = async (e) => {
    e.preventDefault()

    if (!newEventData.title.trim() || !newEventData.description.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    setLoading(true)

    try {
      const result = await addTimelineEvent(order.id, newEventData)

      if (result.success) {
        // إعادة تحميل البيانات من قاعدة البيانات
        await loadTimelineData()

        // إعادة تعيين النموذج
        setNewEventData({
          title: '',
          description: '',
          type: 'comment'
        })
        setShowAddEventModal(false)
      }
    } catch (error) {
      console.error('خطأ في إضافة الحدث:', error)
      toast.error('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  // فلترة الأحداث
  const filteredEvents = filterType === 'all'
    ? timelineEvents
    : timelineEvents.filter(event => event.type === filterType)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-large">
          الجدول الزمني للطلب
        </h2>
        <div className="flex items-center space-x-3 space-x-reverse">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="input text-sm min-w-[140px]"
          >
            <option value="all">جميع الأحداث</option>
            <option value="status_change">تغييرات الحالة</option>
            <option value="comment">التعليقات</option>
            <option value="file_upload">رفع الملفات</option>
            <option value="payment">المدفوعات</option>
          </select>
          <button
            onClick={() => setShowAddEventModal(true)}
            className="btn btn-ghost btn-sm flex items-center space-x-2 space-x-reverse"
          >
            <Edit className="w-4 h-4" />
            <span>إضافة حدث</span>
          </button>
        </div>
      </div>

      <div>
        <div className="relative">
          {/* الخط الزمني */}
          <div className="absolute right-6 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-gray-200 to-gray-300"></div>

          {/* الأحداث */}
          <div className="space-y-6">
            {filteredEvents.map((event, index) => {
              const Icon = event.icon
              const isLast = index === timelineEvents.length - 1

              return (
                <div key={event.id} className="relative flex items-start space-x-4 space-x-reverse">
                  {/* الأيقونة */}
                  <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-2xl shadow-sm ${getIconColor(event.color)}`}>
                    <Icon className="w-5 h-5" />
                  </div>

                  {/* المحتوى */}
                  <div className="flex-1 min-w-0">
                    <div className="bg-gray-50/60 rounded-xl p-4 hover:bg-gray-50 transition-colors duration-200">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-subtitle font-semibold">
                          {event.title}
                        </h3>
                        <time className="text-caption">
                          {formatDate(event.timestamp)}
                        </time>
                      </div>

                      <p className="text-body mb-3">
                        {event.description}
                      </p>

                      <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="flex items-center space-x-1 space-x-reverse text-caption">
                          <User className="w-3 h-3" />
                          <span>{event.user}</span>
                        </div>
                        <span className={`badge ${
                          event.color === 'green' ? 'badge-success' :
                          event.color === 'blue' ? 'badge-primary' :
                          event.color === 'yellow' ? 'badge-warning' :
                          event.color === 'red' ? 'badge-error' :
                          event.color === 'purple' ? 'badge-primary' :
                          'badge-gray'
                        }`}>
                          {event.type === 'created' ? 'إنشاء' :
                           event.type === 'status_change' ? 'تغيير حالة' :
                           event.type === 'comment' ? 'تعليق' :
                           event.type === 'file_upload' ? 'رفع ملف' :
                           event.type === 'payment' ? 'دفع' : 'حدث'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {/* نقطة النهاية */}
          <div className="relative flex items-center space-x-4 space-x-reverse mt-6">
            <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-2xl shadow-sm ${
              order.status === 'completed' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
            }`}>
              {order.status === 'completed' ? <CheckCircle className="w-5 h-5" /> : <Clock className="w-5 h-5" />}
            </div>
            <div className="flex-1">
              <div className="bg-gray-50/60 rounded-xl p-4">
                <p className="text-body font-medium">
                  {order.status === 'completed' ? '✅ تم إكمال الطلب بنجاح' : '⏳ الطلب قيد المتابعة...'}
                </p>
                <p className="text-caption mt-1">
                  {order.status === 'completed' ? 'جميع المراحل اكتملت' : 'سيتم تحديث الجدول الزمني تلقائياً'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="bg-gray-50/60 rounded-xl p-6">
        <h3 className="text-title mb-4">إحصائيات الجدول الزمني</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center bg-blue-50/60 rounded-xl p-4">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {timelineEvents.length}
            </div>
            <div className="text-caption">إجمالي الأحداث</div>
          </div>
          <div className="text-center bg-green-50/60 rounded-xl p-4">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {timelineEvents.filter(e => e.type === 'status_change').length}
            </div>
            <div className="text-caption">تغييرات الحالة</div>
          </div>
          <div className="text-center bg-purple-50/60 rounded-xl p-4">
            <div className="text-2xl font-bold text-purple-600 mb-1">
              {timelineEvents.filter(e => e.type === 'comment').length}
            </div>
            <div className="text-caption">التعليقات</div>
          </div>
          <div className="text-center bg-orange-50/60 rounded-xl p-4">
            <div className="text-2xl font-bold text-orange-600 mb-1">
              {timelineEvents.filter(e => e.type === 'file_upload').length}
            </div>
            <div className="text-caption">الملفات المرفوعة</div>
          </div>
        </div>
      </div>

      {/* Modal إضافة حدث جديد */}
      {showAddEventModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">إضافة حدث جديد</h3>
              <button
                onClick={() => setShowAddEventModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleAddEvent} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع الحدث
                </label>
                <select
                  value={newEventData.type}
                  onChange={(e) => setNewEventData({...newEventData, type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="comment">💬 تعليق</option>
                  <option value="status_change">🔄 تغيير حالة</option>
                  <option value="file_upload">📁 رفع ملف</option>
                  <option value="payment">💰 دفع</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الحدث *
                </label>
                <input
                  type="text"
                  value={newEventData.title}
                  onChange={(e) => setNewEventData({...newEventData, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="مثال: تحديث على المشروع"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف الحدث *
                </label>
                <textarea
                  rows={3}
                  value={newEventData.description}
                  onChange={(e) => setNewEventData({...newEventData, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="اكتب تفاصيل الحدث..."
                  required
                ></textarea>
              </div>

              <div className="flex justify-end space-x-3 space-x-reverse pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddEventModal(false)}
                  className="btn btn-secondary"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="btn btn-primary"
                >
                  {loading ? 'جاري الإضافة...' : 'إضافة الحدث'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default OrderTimeline
