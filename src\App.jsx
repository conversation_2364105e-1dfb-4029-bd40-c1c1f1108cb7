import React, { useState, useEffect } from 'react'
import { Toaster } from 'react-hot-toast'
import useStore from './store/useStore'
import LoginPage from './components/auth/LoginPage'
import Dashboard from './components/dashboard/Dashboard'
import LoadingSpinner from './components/ui/LoadingSpinner'
import UserSetup from './components/admin/UserSetup'

function App() {
  const { isAuthenticated, isLoading, checkAuth } = useStore()
  const [appReady, setAppReady] = useState(false)
  const [showUserSetup, setShowUserSetup] = useState(false)

  useEffect(() => {
    // التحقق من الجلسة المحفوظة
    const initApp = async () => {
      await checkAuth()
      setAppReady(true)
    }

    initApp()
  }, [checkAuth])

  // شاشة التحميل الأولية
  if (!appReady) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-teal-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-900 to-teal-500 rounded-2xl shadow-2xl mb-6 animate-pulse">
            <span className="text-white text-3xl font-bold">ن</span>
          </div>
          <LoadingSpinner size="large" text="جاري تحميل النظام..." />
          <button 
            onClick={() => setAppReady(true)}
            className="mt-4 text-blue-600 hover:text-blue-800 text-sm underline"
          >
            تخطي التحميل
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="App">
      <Toaster 
        position="top-center"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
      
      {/* المحتوى الرئيسي */}
      {showUserSetup ? (
        <div>
          <div className="bg-white shadow-sm border-b p-4">
            <button
              onClick={() => setShowUserSetup(false)}
              className="text-blue-600 hover:text-blue-800"
            >
              ← العودة للتطبيق
            </button>
          </div>
          <UserSetup />
        </div>
      ) : isLoading ? (
        <LoadingSpinner fullScreen text="جاري المعالجة..." />
      ) : isAuthenticated ? (
        <Dashboard />
      ) : (
        <LoginPage />
      )}
    </div>
  )
}

export default App
