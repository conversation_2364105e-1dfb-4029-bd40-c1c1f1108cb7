[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

# إعادة توجيه للتطبيقات أحادية الصفحة
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# إعدادات الأمان
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

# إعدادات خاصة لـ manifest.json
[[headers]]
  for = "/manifest.json"
  [headers.values]
    X-Content-Type-Options = "nosniff"
    Content-Type = "application/manifest+json"

# إعدادات التخزين المؤقت للملفات الثابتة
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# إعدادات التخزين المؤقت للخطوط
[[headers]]
  for = "/*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# إعدادات الضغط (إزالة Content-Encoding لتجنب المشاكل)
[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# إعدادات الأمان للملفات الحساسة
[[headers]]
  for = "/.env"
  [headers.values]
    X-Robots-Tag = "noindex"

# إعدادات النشر
[context.production]
  command = "npm run build"

[context.deploy-preview]
  command = "npm run build"

[context.branch-deploy]
  command = "npm run build"

# إعدادات النماذج (إذا كانت مطلوبة لاحقاً)
[build.processing]
  skip_processing = true
