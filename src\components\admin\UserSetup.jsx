import React, { useState, useEffect } from 'react'
import { Users, Plus, Check, X, RefreshCw } from 'lucide-react'
import { createDemoUsers, checkExistingUsers } from '../../utils/createDemoUsers'
import toast from 'react-hot-toast'

const UserSetup = () => {
  const [isCreating, setIsCreating] = useState(false)
  const [existingUsers, setExistingUsers] = useState([])
  const [results, setResults] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  // تحميل المستخدمين الموجودين
  const loadExistingUsers = async () => {
    setIsLoading(true)
    const users = await checkExistingUsers()
    setExistingUsers(users)
    setIsLoading(false)
  }

  useEffect(() => {
    loadExistingUsers()
  }, [])

  // إنشاء المستخدمين التجريبيين
  const handleCreateUsers = async () => {
    setIsCreating(true)
    setResults([])
    
    try {
      const creationResults = await createDemoUsers()
      setResults(creationResults)
      
      const successCount = creationResults.filter(r => r.success).length
      const failCount = creationResults.filter(r => !r.success).length
      
      if (successCount > 0) {
        toast.success(`تم إنشاء ${successCount} مستخدم بنجاح`)
      }
      
      if (failCount > 0) {
        toast.error(`فشل في إنشاء ${failCount} مستخدم`)
      }
      
      // إعادة تحميل المستخدمين
      await loadExistingUsers()
      
    } catch (error) {
      console.error('خطأ في إنشاء المستخدمين:', error)
      toast.error('حدث خطأ في إنشاء المستخدمين')
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Users className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">إعداد المستخدمين التجريبيين</h1>
              <p className="text-gray-600">إنشاء وإدارة المستخدمين التجريبيين للنظام</p>
            </div>
          </div>
          
          <div className="flex space-x-3 space-x-reverse">
            <button
              onClick={loadExistingUsers}
              disabled={isLoading}
              className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>تحديث</span>
            </button>
            
            <button
              onClick={handleCreateUsers}
              disabled={isCreating}
              className="flex items-center space-x-2 space-x-reverse px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <Plus className="h-4 w-4" />
              <span>{isCreating ? 'جاري الإنشاء...' : 'إنشاء المستخدمين'}</span>
            </button>
          </div>
        </div>

        {/* المستخدمين الموجودين */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">المستخدمين الموجودين</h2>
          
          {isLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400 mb-2" />
              <p className="text-gray-500">جاري التحميل...</p>
            </div>
          ) : existingUsers.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <Users className="h-12 w-12 mx-auto text-gray-400 mb-2" />
              <p className="text-gray-500">لا يوجد مستخدمين</p>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {existingUsers.map((user, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">{user.name}</h3>
                      <p className="text-sm text-gray-600">{user.email}</p>
                      <span className={`inline-block px-2 py-1 text-xs rounded-full mt-2 ${
                        user.membership_level === 'premium' ? 'bg-purple-100 text-purple-800' :
                        user.membership_level === 'gold' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {user.membership_level}
                      </span>
                    </div>
                    <Check className="h-5 w-5 text-green-500" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* نتائج الإنشاء */}
        {results.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">نتائج الإنشاء</h2>
            <div className="space-y-2">
              {results.map((result, index) => (
                <div key={index} className={`flex items-center justify-between p-3 rounded-lg ${
                  result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                }`}>
                  <div className="flex items-center space-x-3 space-x-reverse">
                    {result.success ? (
                      <Check className="h-5 w-5 text-green-500" />
                    ) : (
                      <X className="h-5 w-5 text-red-500" />
                    )}
                    <span className="font-medium">{result.email}</span>
                  </div>
                  {!result.success && (
                    <span className="text-sm text-red-600">{result.error}</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-2">معلومات مهمة</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• كلمة المرور لجميع الحسابات: <code className="bg-blue-100 px-1 rounded">123456</code></li>
            <li>• الحساب الإداري: <code className="bg-blue-100 px-1 rounded"><EMAIL></code></li>
            <li>• سيتم تأكيد جميع الحسابات تلقائياً</li>
            <li>• يمكن استخدام هذه الحسابات لتسجيل الدخول فوراً</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default UserSetup
