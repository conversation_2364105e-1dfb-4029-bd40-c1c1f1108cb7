import React, { useState, useEffect } from 'react'
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  FileText,
  Users,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  Clock,
  Award,
  Target,
  Star,
  Zap,
  ArrowUp,
  ArrowDown,
  BarChart3
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'
import useStore from '../../store/useStore'

const StatsCardsManager = ({ onClose }) => {
  const { user } = useStore()
  const [statsCards, setStatsCards] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedCard, setSelectedCard] = useState(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    value: '',
    trend: '',
    trend_direction: 'up',
    icon: 'FileText',
    color: 'blue',
    sort_order: 0,
    is_active: true
  })

  // الأيقونات المتاحة
  const availableIcons = [
    { name: 'FileText', icon: FileText, label: 'ملف' },
    { name: 'Users', icon: Users, label: 'مستخدمون' },
    { name: 'CheckCircle', icon: CheckCircle, label: 'علامة صح' },
    { name: 'TrendingUp', icon: TrendingUp, label: 'اتجاه صاعد' },
    { name: 'DollarSign', icon: DollarSign, label: 'دولار' },
    { name: 'Activity', icon: Activity, label: 'نشاط' },
    { name: 'Clock', icon: Clock, label: 'ساعة' },
    { name: 'Award', icon: Award, label: 'جائزة' },
    { name: 'Target', icon: Target, label: 'هدف' },
    { name: 'Star', icon: Star, label: 'نجمة' },
    { name: 'Zap', icon: Zap, label: 'برق' },
    { name: 'BarChart3', icon: BarChart3, label: 'رسم بياني' }
  ]

  // الألوان المتاحة
  const availableColors = [
    { name: 'blue', label: 'أزرق', bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-600' },
    { name: 'green', label: 'أخضر', bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-600' },
    { name: 'purple', label: 'بنفسجي', bg: 'bg-purple-50', border: 'border-purple-200', text: 'text-purple-600' },
    { name: 'yellow', label: 'أصفر', bg: 'bg-yellow-50', border: 'border-yellow-200', text: 'text-yellow-600' },
    { name: 'red', label: 'أحمر', bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-600' },
    { name: 'teal', label: 'تركوازي', bg: 'bg-teal-50', border: 'border-teal-200', text: 'text-teal-600' }
  ]

  // اتجاهات الاتجاه
  const trendDirections = [
    { value: 'up', label: 'صاعد', icon: TrendingUp, color: 'text-green-500' },
    { value: 'down', label: 'هابط', icon: TrendingDown, color: 'text-red-500' }
  ]

  // تحميل بطاقات الإحصائيات
  const loadStatsCards = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('stats_cards')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true })

      if (error) throw error
      setStatsCards(data || [])
    } catch (error) {
      console.error('خطأ في تحميل بطاقات الإحصائيات:', error)
      toast.error('فشل في تحميل بطاقات الإحصائيات')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadStatsCards()
  }, [])

  // إضافة بطاقة جديدة
  const handleAddCard = async () => {
    try {
      const { data, error } = await supabase
        .from('stats_cards')
        .insert([{
          ...formData,
          sort_order: statsCards.length + 1,
          created_by: user.id
        }])
        .select()

      if (error) throw error

      setStatsCards([...statsCards, data[0]])
      setShowAddModal(false)
      resetForm()
      toast.success('تم إضافة البطاقة بنجاح')
    } catch (error) {
      console.error('خطأ في إضافة البطاقة:', error)
      toast.error('فشل في إضافة البطاقة')
    }
  }

  // تعديل بطاقة
  const handleEditCard = async () => {
    try {
      const { data, error } = await supabase
        .from('stats_cards')
        .update({
          ...formData,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedCard.id)
        .select()

      if (error) throw error

      setStatsCards(statsCards.map(card => 
        card.id === selectedCard.id ? data[0] : card
      ))
      setShowEditModal(false)
      setSelectedCard(null)
      resetForm()
      toast.success('تم تحديث البطاقة بنجاح')
    } catch (error) {
      console.error('خطأ في تحديث البطاقة:', error)
      toast.error('فشل في تحديث البطاقة')
    }
  }

  // حذف بطاقة
  const handleDeleteCard = async (cardId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه البطاقة؟')) return

    try {
      const { error } = await supabase
        .from('stats_cards')
        .delete()
        .eq('id', cardId)

      if (error) throw error

      setStatsCards(statsCards.filter(card => card.id !== cardId))
      toast.success('تم حذف البطاقة بنجاح')
    } catch (error) {
      console.error('خطأ في حذف البطاقة:', error)
      toast.error('فشل في حذف البطاقة')
    }
  }

  // تحريك البطاقة لأعلى أو أسفل
  const moveCard = async (cardId, direction) => {
    console.log('🔄 moveCard called:', { cardId, direction, cardsLength: statsCards.length })

    const currentIndex = statsCards.findIndex(c => c.id === cardId)
    console.log('📍 Current index:', currentIndex)

    if (currentIndex === -1) {
      console.error('❌ Card not found')
      return
    }

    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === statsCards.length - 1)
    ) {
      console.log('⚠️ Cannot move - at boundary')
      return
    }

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    console.log('📍 New index:', newIndex)

    const newCards = [...statsCards]

    // تبديل المواضع
    [newCards[currentIndex], newCards[newIndex]] =
    [newCards[newIndex], newCards[currentIndex]]

    try {
      console.log('💾 Updating database...')

      // تحديث sort_order للعنصرين المتبادلين فقط
      const currentCard = newCards[currentIndex]
      const swappedCard = newCards[newIndex]

      // تحديث البطاقة الأولى
      const { error: error1 } = await supabase
        .from('stats_cards')
        .update({ sort_order: currentIndex + 1 })
        .eq('id', currentCard.id)

      if (error1) throw error1

      // تحديث البطاقة الثانية
      const { error: error2 } = await supabase
        .from('stats_cards')
        .update({ sort_order: newIndex + 1 })
        .eq('id', swappedCard.id)

      if (error2) throw error2

      // تحديث الحالة المحلية
      currentCard.sort_order = currentIndex + 1
      swappedCard.sort_order = newIndex + 1

      setStatsCards(newCards)
      console.log('✅ Cards reordered successfully')
      toast.success('تم تحديث ترتيب البطاقات')
    } catch (error) {
      console.error('❌ Error updating order:', error)
      toast.error('فشل في تحديث الترتيب')
      // إعادة تحميل البطاقات في حالة الخطأ
      loadStatsCards()
    }
  }

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      value: '',
      trend: '',
      trend_direction: 'up',
      icon: 'FileText',
      color: 'blue',
      sort_order: 0,
      is_active: true
    })
  }

  // فتح نموذج التعديل
  const openEditModal = (card) => {
    setSelectedCard(card)
    setFormData({
      title: card.title || '',
      description: card.description || '',
      value: card.value || '',
      trend: card.trend || '',
      trend_direction: card.trend_direction || 'up',
      icon: card.icon || 'FileText',
      color: card.color || 'blue',
      sort_order: card.sort_order || 0,
      is_active: card.is_active
    })
    setShowEditModal(true)
  }

  // الحصول على الأيقونة
  const getIcon = (iconName) => {
    const iconData = availableIcons.find(i => i.name === iconName)
    return iconData ? iconData.icon : FileText
  }

  // الحصول على اللون
  const getColorClasses = (colorName) => {
    const colorData = availableColors.find(c => c.name === colorName)
    return colorData || availableColors[0]
  }

  // الحصول على أيقونة الاتجاه
  const getTrendIcon = (direction) => {
    const trendData = trendDirections.find(t => t.value === direction)
    return trendData ? trendData.icon : TrendingUp
  }

  // الحصول على لون الاتجاه
  const getTrendColor = (direction) => {
    const trendData = trendDirections.find(t => t.value === direction)
    return trendData ? trendData.color : 'text-green-500'
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-5xl w-full max-h-[90vh] overflow-y-auto">
        {/* رأس النافذة */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">إدارة بطاقات الإحصائيات</h2>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => setShowAddModal(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <Plus className="w-4 h-4" />
                <span>إضافة بطاقة</span>
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* قائمة البطاقات */}
        <div className="p-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-4">جاري تحميل البطاقات...</p>
            </div>
          ) : statsCards.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {statsCards.map((card, index) => {
                const Icon = getIcon(card.icon)
                const colorClasses = getColorClasses(card.color)
                const TrendIcon = getTrendIcon(card.trend_direction)
                const trendColor = getTrendColor(card.trend_direction)
                
                return (
                  <div
                    key={card.id}
                    className={`${colorClasses.bg} rounded-xl p-6 border ${colorClasses.border} relative`}
                  >
                    {/* أزرار التحكم */}
                    <div className="absolute top-2 left-2 flex items-center space-x-1 space-x-reverse">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          console.log('⬆️ Up button clicked for card:', card.id, 'index:', index)
                          moveCard(card.id, 'up')
                        }}
                        disabled={index === 0}
                        className={`p-1 transition-colors ${
                          index === 0
                            ? 'text-gray-300 cursor-not-allowed'
                            : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded'
                        }`}
                        title="تحريك لأعلى"
                      >
                        <ArrowUp className="w-3 h-3" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          console.log('⬇️ Down button clicked for card:', card.id, 'index:', index)
                          moveCard(card.id, 'down')
                        }}
                        disabled={index === statsCards.length - 1}
                        className={`p-1 transition-colors ${
                          index === statsCards.length - 1
                            ? 'text-gray-300 cursor-not-allowed'
                            : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded'
                        }`}
                        title="تحريك لأسفل"
                      >
                        <ArrowDown className="w-3 h-3" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          openEditModal(card)
                        }}
                        className="p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
                        title="تعديل"
                      >
                        <Edit className="w-3 h-3" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteCard(card.id)
                        }}
                        className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                        title="حذف"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>

                    {/* رأس البطاقة */}
                    <div className="flex items-center justify-between mb-4">
                      <p className="text-sm font-medium text-gray-600 truncate pr-16">
                        {card.title}
                      </p>
                      <div className={`w-10 h-10 ${colorClasses.bg} border ${colorClasses.border} rounded-lg flex items-center justify-center flex-shrink-0`}>
                        <Icon className={`w-5 h-5 ${colorClasses.text}`} />
                      </div>
                    </div>
                    
                    {/* المحتوى الرئيسي */}
                    <div className="flex-1 flex flex-col justify-between">
                      {/* القيمة */}
                      <div className="mb-3">
                        <p className="text-3xl font-bold text-gray-900 leading-none">
                          {card.value}
                        </p>
                        
                        {/* الوصف */}
                        {card.description && (
                          <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                            {card.description}
                          </p>
                        )}
                      </div>
                      
                      {/* الاتجاه */}
                      {card.trend && (
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <TrendIcon className={`w-4 h-4 ${trendColor} flex-shrink-0`} />
                          <span className={`text-sm font-medium truncate ${trendColor}`}>
                            {card.trend}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <BarChart3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بطاقات إحصائيات</h3>
              <p className="text-gray-500 mb-4">لم يتم إضافة أي بطاقات بعد</p>
              <button
                onClick={() => setShowAddModal(true)}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                إضافة أول بطاقة
              </button>
            </div>
          )}
        </div>

        {/* نموذج إضافة بطاقة */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">إضافة بطاقة إحصائيات جديدة</h3>
                  <button
                    onClick={() => {
                      setShowAddModal(false)
                      resetForm()
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">عنوان البطاقة *</label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: الخدمات المتاحة"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="وصف مختصر للبطاقة"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">القيمة *</label>
                    <input
                      type="text"
                      value={formData.value}
                      onChange={(e) => setFormData({...formData, value: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثال: 12 أو 98.5%"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الاتجاه</label>
                    <input
                      type="text"
                      value={formData.trend}
                      onChange={(e) => setFormData({...formData, trend: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثال: +3 جديدة"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اتجاه الاتجاه</label>
                  <div className="grid grid-cols-2 gap-2">
                    {trendDirections.map((direction) => {
                      const TrendIcon = direction.icon
                      return (
                        <button
                          key={direction.value}
                          type="button"
                          onClick={() => setFormData({...formData, trend_direction: direction.value})}
                          className={`p-3 border rounded-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors ${
                            formData.trend_direction === direction.value
                              ? 'border-blue-500 bg-blue-50 text-blue-600'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <TrendIcon className={`w-4 h-4 ${direction.color}`} />
                          <span className="text-sm">{direction.label}</span>
                        </button>
                      )
                    })}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الأيقونة</label>
                  <div className="grid grid-cols-4 gap-2">
                    {availableIcons.map((iconData) => {
                      const Icon = iconData.icon
                      return (
                        <button
                          key={iconData.name}
                          type="button"
                          onClick={() => setFormData({...formData, icon: iconData.name})}
                          className={`p-3 border rounded-lg flex flex-col items-center space-y-1 transition-colors ${
                            formData.icon === iconData.name
                              ? 'border-blue-500 bg-blue-50 text-blue-600'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span className="text-xs">{iconData.label}</span>
                        </button>
                      )
                    })}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                  <div className="grid grid-cols-3 gap-2">
                    {availableColors.map((colorData) => (
                      <button
                        key={colorData.name}
                        type="button"
                        onClick={() => setFormData({...formData, color: colorData.name})}
                        className={`p-3 border rounded-lg flex items-center space-x-2 space-x-reverse transition-colors ${
                          formData.color === colorData.name
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <div className={`w-4 h-4 rounded-full ${colorData.bg} border ${colorData.border}`}></div>
                        <span className="text-sm">{colorData.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-3 space-x-reverse">
                <button
                  onClick={() => {
                    setShowAddModal(false)
                    resetForm()
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleAddCard}
                  disabled={!formData.title.trim() || !formData.value.trim()}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse"
                >
                  <Save className="w-4 h-4" />
                  <span>حفظ</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* نموذج تعديل بطاقة */}
        {showEditModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">تعديل بطاقة الإحصائيات</h3>
                  <button
                    onClick={() => {
                      setShowEditModal(false)
                      setSelectedCard(null)
                      resetForm()
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">عنوان البطاقة *</label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: الخدمات المتاحة"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="وصف مختصر للبطاقة"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">القيمة *</label>
                    <input
                      type="text"
                      value={formData.value}
                      onChange={(e) => setFormData({...formData, value: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثال: 12 أو 98.5%"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الاتجاه</label>
                    <input
                      type="text"
                      value={formData.trend}
                      onChange={(e) => setFormData({...formData, trend: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثال: +3 جديدة"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اتجاه الاتجاه</label>
                  <div className="grid grid-cols-2 gap-2">
                    {trendDirections.map((direction) => {
                      const TrendIcon = direction.icon
                      return (
                        <button
                          key={direction.value}
                          type="button"
                          onClick={() => setFormData({...formData, trend_direction: direction.value})}
                          className={`p-3 border rounded-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors ${
                            formData.trend_direction === direction.value
                              ? 'border-blue-500 bg-blue-50 text-blue-600'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <TrendIcon className={`w-4 h-4 ${direction.color}`} />
                          <span className="text-sm">{direction.label}</span>
                        </button>
                      )
                    })}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الأيقونة</label>
                  <div className="grid grid-cols-4 gap-2">
                    {availableIcons.map((iconData) => {
                      const Icon = iconData.icon
                      return (
                        <button
                          key={iconData.name}
                          type="button"
                          onClick={() => setFormData({...formData, icon: iconData.name})}
                          className={`p-3 border rounded-lg flex flex-col items-center space-y-1 transition-colors ${
                            formData.icon === iconData.name
                              ? 'border-blue-500 bg-blue-50 text-blue-600'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span className="text-xs">{iconData.label}</span>
                        </button>
                      )
                    })}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                  <div className="grid grid-cols-3 gap-2">
                    {availableColors.map((colorData) => (
                      <button
                        key={colorData.name}
                        type="button"
                        onClick={() => setFormData({...formData, color: colorData.name})}
                        className={`p-3 border rounded-lg flex items-center space-x-2 space-x-reverse transition-colors ${
                          formData.color === colorData.name
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <div className={`w-4 h-4 rounded-full ${colorData.bg} border ${colorData.border}`}></div>
                        <span className="text-sm">{colorData.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-3 space-x-reverse">
                <button
                  onClick={() => {
                    setShowEditModal(false)
                    setSelectedCard(null)
                    resetForm()
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleEditCard}
                  disabled={!formData.title.trim() || !formData.value.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse"
                >
                  <Save className="w-4 h-4" />
                  <span>حفظ التغييرات</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default StatsCardsManager
