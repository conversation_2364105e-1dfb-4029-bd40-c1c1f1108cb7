import React, { useState } from 'react'
import { 
  Send, 
  Paperclip, 
  Smile,
  MoreH<PERSON>zon<PERSON>,
  Edit,
  Trash2,
  <PERSON><PERSON>,
  Heart,
  User,
  Calendar
} from 'lucide-react'

const OrderComments = ({ orderId }) => {
  const [newComment, setNewComment] = useState('')
  const [replyTo, setReplyTo] = useState(null)

  // بيانات تجريبية للتعليقات
  const comments = [
    {
      id: 1,
      content: 'تم مراجعة المتطلبات وهي واضحة ومفصلة. سنبدأ العمل على التصميم الأولي خلال الأيام القادمة.',
      user_name: 'أحمد محمد',
      user_role: 'مدير المشروع',
      created_at: '2024-06-16T10:30:00Z',
      updated_at: null,
      likes: 3,
      replies: [
        {
          id: 11,
          content: 'ممتاز، نتطلع لرؤية التصميم الأولي',
          user_name: 'العميل',
          user_role: 'عميل',
          created_at: '2024-06-16T14:15:00Z',
          likes: 1
        }
      ]
    },
    {
      id: 2,
      content: 'تم الانتهاء من التصميم الأولي لواجهة المستخدم. يرجى مراجعة الملفات المرفقة وإبداء الملاحظات.',
      user_name: 'فاطمة علي',
      user_role: 'مصممة UI/UX',
      created_at: '2024-06-20T16:45:00Z',
      updated_at: null,
      likes: 5,
      replies: []
    },
    {
      id: 3,
      content: 'التصميم رائع جداً! لدي بعض الملاحظات البسيطة:\n\n1. يمكن تكبير حجم الخط في القائمة الرئيسية\n2. تغيير لون الأزرار إلى اللون الأزرق\n3. إضافة المزيد من المساحات البيضاء\n\nبشكل عام العمل ممتاز ونقدر الجهد المبذول.',
      user_name: 'العميل',
      user_role: 'عميل',
      created_at: '2024-06-21T09:20:00Z',
      updated_at: null,
      likes: 2,
      replies: [
        {
          id: 31,
          content: 'شكراً لك على الملاحظات القيمة. سنقوم بتطبيق التعديلات المطلوبة خلال يومين.',
          user_name: 'فاطمة علي',
          user_role: 'مصممة UI/UX',
          created_at: '2024-06-21T11:30:00Z',
          likes: 1
        }
      ]
    },
    {
      id: 4,
      content: 'تم تطبيق جميع التعديلات المطلوبة. النسخة المحدثة متاحة الآن للمراجعة.',
      user_name: 'فاطمة علي',
      user_role: 'مصممة UI/UX',
      created_at: '2024-06-23T14:10:00Z',
      updated_at: null,
      likes: 4,
      replies: []
    }
  ]

  // دالة لتنسيق التاريخ
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return 'منذ دقائق'
    } else if (diffInHours < 24) {
      return `منذ ${diffInHours} ساعة`
    } else if (diffInHours < 48) {
      return 'منذ يوم'
    } else {
      return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  // دالة لتحديد لون الدور
  const getRoleColor = (role) => {
    switch (role) {
      case 'عميل':
        return 'bg-blue-100 text-blue-800'
      case 'مدير المشروع':
        return 'bg-purple-100 text-purple-800'
      case 'مصممة UI/UX':
        return 'bg-green-100 text-green-800'
      case 'مطور':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // دالة لإرسال تعليق جديد
  const handleSubmitComment = (e) => {
    e.preventDefault()
    if (!newComment.trim()) return

    // هنا يمكن إضافة منطق إرسال التعليق إلى الخادم
    console.log('New comment:', newComment)
    setNewComment('')
    setReplyTo(null)
  }

  // دالة للرد على تعليق
  const handleReply = (commentId) => {
    setReplyTo(commentId)
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات التعليقات */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-gray-900">{comments.length}</div>
          <div className="text-sm text-gray-600">إجمالي التعليقات</div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-blue-600">
            {comments.reduce((sum, comment) => sum + comment.replies.length, 0)}
          </div>
          <div className="text-sm text-gray-600">الردود</div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-2xl font-bold text-green-600">
            {comments.reduce((sum, comment) => sum + comment.likes + comment.replies.reduce((replySum, reply) => replySum + reply.likes, 0), 0)}
          </div>
          <div className="text-sm text-gray-600">الإعجابات</div>
        </div>
      </div>

      {/* نموذج إضافة تعليق جديد */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {replyTo ? 'إضافة رد' : 'إضافة تعليق جديد'}
        </h3>
        
        <form onSubmit={handleSubmitComment}>
          <div className="mb-4">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder={replyTo ? 'اكتب ردك هنا...' : 'اكتب تعليقك هنا...'}
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 space-x-reverse">
              <button
                type="button"
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Paperclip className="w-5 h-5" />
              </button>
              <button
                type="button"
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Smile className="w-5 h-5" />
              </button>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              {replyTo && (
                <button
                  type="button"
                  onClick={() => setReplyTo(null)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  إلغاء
                </button>
              )}
              <button
                type="submit"
                disabled={!newComment.trim()}
                className="btn-primary flex items-center space-x-2 space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="w-4 h-4" />
                <span>{replyTo ? 'إرسال الرد' : 'إرسال التعليق'}</span>
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* قائمة التعليقات */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            التعليقات والمناقشات
          </h2>
        </div>

        <div className="divide-y divide-gray-200">
          {comments.map((comment) => (
            <div key={comment.id} className="p-6">
              {/* التعليق الرئيسي */}
              <div className="flex space-x-4 space-x-reverse">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-teal-500 rounded-full flex items-center justify-center text-white font-bold">
                    {comment.user_name.charAt(0)}
                  </div>
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                    <h4 className="font-semibold text-gray-900">{comment.user_name}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(comment.user_role)}`}>
                      {comment.user_role}
                    </span>
                    <span className="text-sm text-gray-500">{formatDate(comment.created_at)}</span>
                    {comment.updated_at && (
                      <span className="text-xs text-gray-400">(محرر)</span>
                    )}
                  </div>
                  
                  <div className="prose prose-sm max-w-none mb-3">
                    <p className="text-gray-700 whitespace-pre-line">{comment.content}</p>
                  </div>
                  
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <button className="flex items-center space-x-1 space-x-reverse text-gray-500 hover:text-red-600 transition-colors">
                      <Heart className="w-4 h-4" />
                      <span className="text-sm">{comment.likes}</span>
                    </button>
                    <button 
                      onClick={() => handleReply(comment.id)}
                      className="flex items-center space-x-1 space-x-reverse text-gray-500 hover:text-blue-600 transition-colors"
                    >
                      <Reply className="w-4 h-4" />
                      <span className="text-sm">رد</span>
                    </button>
                    <button className="text-gray-400 hover:text-gray-600 transition-colors">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* الردود */}
              {comment.replies.length > 0 && (
                <div className="mr-14 mt-4 space-y-4">
                  {comment.replies.map((reply) => (
                    <div key={reply.id} className="flex space-x-3 space-x-reverse">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                          {reply.user_name.charAt(0)}
                        </div>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 space-x-reverse mb-1">
                          <h5 className="font-medium text-gray-900 text-sm">{reply.user_name}</h5>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(reply.user_role)}`}>
                            {reply.user_role}
                          </span>
                          <span className="text-xs text-gray-500">{formatDate(reply.created_at)}</span>
                        </div>
                        
                        <p className="text-gray-700 text-sm mb-2">{reply.content}</p>
                        
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <button className="flex items-center space-x-1 space-x-reverse text-gray-500 hover:text-red-600 transition-colors">
                            <Heart className="w-3 h-3" />
                            <span className="text-xs">{reply.likes}</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default OrderComments
