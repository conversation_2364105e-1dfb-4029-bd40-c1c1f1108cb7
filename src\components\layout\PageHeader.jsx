import React from 'react'
import { ArrowRight, User } from 'lucide-react'
import useStore from '../../store/useStore'

const PageHeader = ({ 
  backButton = null, // { text: "العودة للخدمات", onClick: () => {} }
  title = "",
  subtitle = "",
  showUserInfo = true 
}) => {
  const { user } = useStore()

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-10">
      <div className="flex items-center justify-between">
        {/* زر العودة - بعد السايد بار مباشرة */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {backButton && (
            <button
              onClick={backButton.onClick}
              className="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-gray-900 transition-colors text-sm"
            >
              <ArrowRight className="w-4 h-4" />
              <span>{backButton.text}</span>
            </button>
          )}
        </div>

        {/* منطقة التنقل - الوسط */}
        <div className="flex-1 flex justify-center">
          <div className="text-center">
            {/* العنوان */}
            {title && (
              <h1 className="text-xl font-bold text-gray-900 mb-1">{title}</h1>
            )}

            {/* الوصف الفرعي */}
            {subtitle && (
              <p className="text-gray-600 text-sm">{subtitle}</p>
            )}
          </div>
        </div>

        {/* معلومات الحساب - اليسار */}
        {showUserInfo && user && (
          <div className="flex items-center space-x-3 space-x-reverse">
            {/* صورة الملف الشخصي */}
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-blue-600" />
            </div>
            
            {/* معلومات المستخدم */}
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                {user.name || user.organization_name || 'المستخدم'}
              </div>
              <div className="text-xs text-gray-500">
                {user.account_type === 'admin' ? 'حساب إداري' : 
                 user.account_type === 'company' ? 'حساب شركة' : 
                 'عضوية مميزة'}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PageHeader
