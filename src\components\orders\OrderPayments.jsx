import React, { useState } from 'react'
import { 
  Plus, 
  Download, 
  Eye, 
  CreditCard,
  DollarSign,
  Calendar,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Receipt,
  TrendingUp
} from 'lucide-react'

const OrderPayments = ({ orderId }) => {
  const [showAddPayment, setShowAddPayment] = useState(false)

  // بيانات تجريبية للمدفوعات
  const payments = [
    {
      id: 1,
      invoice_number: 'INV-2024-001',
      amount: 7500,
      payment_type: 'advance',
      payment_method: 'bank_transfer',
      payment_status: 'completed',
      payment_date: '2024-06-21T11:20:00Z',
      due_date: '2024-06-20T23:59:59Z',
      description: 'دفعة مقدمة - 50% من قيمة المشروع',
      tax_amount: 1125,
      discount_amount: 0,
      total_amount: 8625,
      transaction_id: 'TXN-2024-001',
      receipt_url: '/receipts/receipt-001.pdf',
      created_at: '2024-06-15T10:00:00Z'
    },
    {
      id: 2,
      invoice_number: 'INV-2024-002',
      amount: 7500,
      payment_type: 'final',
      payment_method: 'credit_card',
      payment_status: 'pending',
      payment_date: null,
      due_date: '2024-07-15T23:59:59Z',
      description: 'الدفعة النهائية - 50% من قيمة المشروع',
      tax_amount: 1125,
      discount_amount: 500,
      total_amount: 8125,
      transaction_id: null,
      receipt_url: null,
      created_at: '2024-06-15T10:00:00Z'
    }
  ]

  // دالة لتحديد لون حالة الدفع
  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'refunded':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // دالة لتحديد أيقونة حالة الدفع
  const getPaymentStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'failed':
        return <XCircle className="w-4 h-4" />
      case 'refunded':
        return <AlertCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // دالة لتحديد نص حالة الدفع
  const getPaymentStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'مكتملة'
      case 'pending':
        return 'في الانتظار'
      case 'failed':
        return 'فاشلة'
      case 'refunded':
        return 'مسترد'
      default:
        return 'غير محدد'
    }
  }

  // دالة لتحديد طريقة الدفع
  const getPaymentMethodText = (method) => {
    switch (method) {
      case 'cash':
        return 'نقداً'
      case 'bank_transfer':
        return 'تحويل بنكي'
      case 'credit_card':
        return 'بطاقة ائتمان'
      case 'check':
        return 'شيك'
      case 'online':
        return 'دفع إلكتروني'
      default:
        return 'غير محدد'
    }
  }

  // دالة لتحديد نوع الدفعة
  const getPaymentTypeText = (type) => {
    switch (type) {
      case 'advance':
        return 'دفعة مقدمة'
      case 'partial':
        return 'دفعة جزئية'
      case 'final':
        return 'دفعة نهائية'
      case 'full':
        return 'دفعة كاملة'
      default:
        return 'غير محدد'
    }
  }

  // دالة لتنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد'
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // دالة لتنسيق المبلغ
  const formatAmount = (amount) => {
    return `${amount.toLocaleString()} ريال`
  }

  // حساب الإحصائيات
  const stats = {
    totalPaid: payments.filter(p => p.payment_status === 'completed').reduce((sum, p) => sum + p.total_amount, 0),
    totalPending: payments.filter(p => p.payment_status === 'pending').reduce((sum, p) => sum + p.total_amount, 0),
    totalAmount: payments.reduce((sum, p) => sum + p.total_amount, 0),
    completedCount: payments.filter(p => p.payment_status === 'completed').length,
    pendingCount: payments.filter(p => p.payment_status === 'pending').length
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات المدفوعات */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-green-600">{formatAmount(stats.totalPaid)}</div>
              <div className="text-sm text-gray-600">المبلغ المدفوع</div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-yellow-600">{formatAmount(stats.totalPending)}</div>
              <div className="text-sm text-gray-600">المبلغ المعلق</div>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-blue-600">{formatAmount(stats.totalAmount)}</div>
              <div className="text-sm text-gray-600">المبلغ الإجمالي</div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-gray-900">{payments.length}</div>
              <div className="text-sm text-gray-600">إجمالي الفواتير</div>
            </div>
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <Receipt className="w-6 h-6 text-gray-600" />
            </div>
          </div>
        </div>
      </div>

      {/* شريط التقدم */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">تقدم المدفوعات</h3>
          <span className="text-sm text-gray-600">
            {Math.round((stats.totalPaid / stats.totalAmount) * 100)}% مكتمل
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-4">
          <div 
            className="bg-gradient-to-r from-green-500 to-green-600 h-4 rounded-full transition-all duration-300"
            style={{ width: `${(stats.totalPaid / stats.totalAmount) * 100}%` }}
          ></div>
        </div>
        
        <div className="flex justify-between text-sm text-gray-600 mt-2">
          <span>مدفوع: {formatAmount(stats.totalPaid)}</span>
          <span>متبقي: {formatAmount(stats.totalAmount - stats.totalPaid)}</span>
        </div>
      </div>

      {/* قائمة المدفوعات */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              الفواتير والمدفوعات
            </h2>
            <button
              onClick={() => setShowAddPayment(true)}
              className="btn-primary flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="w-4 h-4" />
              <span>إضافة فاتورة</span>
            </button>
          </div>
        </div>

        <div className="divide-y divide-gray-200">
          {payments.map((payment) => (
            <div key={payment.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  {/* رقم الفاتورة والحالة */}
                  <div className="flex items-center space-x-3 space-x-reverse mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {payment.invoice_number}
                    </h3>
                    <span className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(payment.payment_status)}`}>
                      {getPaymentStatusIcon(payment.payment_status)}
                      <span>{getPaymentStatusText(payment.payment_status)}</span>
                    </span>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                      {getPaymentTypeText(payment.payment_type)}
                    </span>
                  </div>

                  {/* وصف الفاتورة */}
                  <p className="text-gray-600 mb-3">
                    {payment.description}
                  </p>

                  {/* تفاصيل الدفع */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">طريقة الدفع:</span>
                      <div className="flex items-center space-x-1 space-x-reverse mt-1">
                        <CreditCard className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{getPaymentMethodText(payment.payment_method)}</span>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">تاريخ الدفع:</span>
                      <div className="flex items-center space-x-1 space-x-reverse mt-1">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{formatDate(payment.payment_date)}</span>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">تاريخ الاستحقاق:</span>
                      <div className="flex items-center space-x-1 space-x-reverse mt-1">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{formatDate(payment.due_date)}</span>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">رقم المعاملة:</span>
                      <div className="mt-1">
                        <span className="text-gray-900 font-mono text-xs">
                          {payment.transaction_id || 'غير متاح'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* تفاصيل المبالغ */}
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">المبلغ الأساسي:</span>
                        <div className="font-semibold text-gray-900">{formatAmount(payment.amount)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">الضريبة:</span>
                        <div className="font-semibold text-gray-900">{formatAmount(payment.tax_amount)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">الخصم:</span>
                        <div className="font-semibold text-green-600">-{formatAmount(payment.discount_amount)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">المبلغ الإجمالي:</span>
                        <div className="font-bold text-lg text-gray-900">{formatAmount(payment.total_amount)}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex items-center space-x-2 space-x-reverse mr-4">
                  <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                    <Eye className="w-4 h-4" />
                  </button>
                  {payment.receipt_url && (
                    <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                      <Download className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* ملخص المدفوعات */}
        <div className="p-6 bg-gray-50 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">إجمالي المبلغ الأساسي:</span>
              <span className="font-semibold text-gray-900">
                {formatAmount(payments.reduce((sum, p) => sum + p.amount, 0))}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">إجمالي الضرائب:</span>
              <span className="font-semibold text-gray-900">
                {formatAmount(payments.reduce((sum, p) => sum + p.tax_amount, 0))}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">إجمالي الخصومات:</span>
              <span className="font-semibold text-green-600">
                -{formatAmount(payments.reduce((sum, p) => sum + p.discount_amount, 0))}
              </span>
            </div>
          </div>
          <div className="border-t border-gray-300 mt-4 pt-4">
            <div className="flex justify-between text-lg font-bold">
              <span className="text-gray-900">المبلغ الإجمالي النهائي:</span>
              <span className="text-gray-900">{formatAmount(stats.totalAmount)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OrderPayments
