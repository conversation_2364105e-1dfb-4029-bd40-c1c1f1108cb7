{"build": {"command": "npm run build", "publish": "dist", "environment": {"NODE_VERSION": "18", "NPM_VERSION": "9"}}, "redirects": [{"from": "/*", "to": "/index.html", "status": 200}], "headers": [{"for": "/*", "values": {"X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "strict-origin-when-cross-origin"}}, {"for": "/assets/*", "values": {"Cache-Control": "public, max-age=31536000, immutable"}}]}