@echo off
chcp 65001 >nul
title نماء الاحترافية - Build Script

echo 🚀 بدء بناء مشروع نماء الاحترافية...
echo.

REM تحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo تحميل من: https://nodejs.org
    pause
    exit /b 1
)

REM تحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير مثبت
    pause
    exit /b 1
)

echo ✅ Node.js و npm متوفران
echo.

REM تثبيت التبعيات
echo 📦 تثبيت التبعيات...
npm install

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)

echo ✅ تم تثبيت التبعيات بنجاح
echo.

REM بناء المشروع
echo 🔨 بناء المشروع...
npm run build

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

REM تحقق من وجود مجلد dist
if not exist "dist" (
    echo ❌ مجلد dist غير موجود
    pause
    exit /b 1
)

echo 📁 محتويات مجلد dist:
dir dist
echo.

echo 🎉 تم بناء المشروع بنجاح!
echo.
echo 📋 خطوات النشر:
echo 1. اذهب إلى https://app.netlify.com/drop
echo 2. اسحب مجلد 'dist' إلى الصفحة
echo 3. انتظر التحميل
echo 4. احصل على الرابط!
echo.
echo 🧪 للاختبار المحلي:
echo npm run preview
echo.
echo 📞 للدعم: <EMAIL>
echo.

REM فتح مجلد dist في Explorer
echo 📂 فتح مجلد dist...
start explorer dist

pause
