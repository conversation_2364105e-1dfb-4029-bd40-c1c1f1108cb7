import React, { useState, useEffect } from 'react'
import {
  X,
  Edit,
  Save,
  User,
  Mail,
  Phone,
  Building,
  Shield,
  Trash2
} from 'lucide-react'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'

const EditCustomerModal = ({ isOpen, onClose, customer, onSuccess }) => {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    contact_person: '',
    account_type: 'customer',
    is_active: true
  })

  // تحديث البيانات عند تغيير العميل
  useEffect(() => {
    if (customer) {
      setFormData({
        name: customer.name || '',
        email: customer.email || '',
        phone: customer.phone || '',
        contact_person: customer.contact_person || '',
        account_type: customer.account_type || 'customer',
        is_active: customer.is_active !== false
      })
    }
  }, [customer])

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    // التحقق من البيانات المطلوبة
    if (!formData.name.trim() || !formData.email.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    // التحقق من صحة الإيميل
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      toast.error('يرجى إدخال بريد إلكتروني صحيح')
      return
    }

    try {
      setLoading(true)

      // تحديث بيانات العميل
      const { error } = await supabase
        .from('users')
        .update({
          name: formData.name.trim(),
          email: formData.email.trim(),
          phone: formData.phone.trim() || null,
          contact_person: formData.contact_person.trim() || null,
          account_type: formData.account_type,
          is_active: formData.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', customer.id)

      if (error) throw error

      toast.success('تم تحديث بيانات العميل بنجاح!')
      
      // إغلاق النموذج وتحديث القائمة
      onSuccess && onSuccess()
      onClose()

    } catch (error) {
      console.error('خطأ في تحديث العميل:', error)
      toast.error('فشل في تحديث بيانات العميل')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!window.confirm('هل أنت متأكد من حذف هذا العميل؟ سيتم حذف جميع بياناته المرتبطة.')) {
      return
    }

    try {
      setLoading(true)

      // حذف العميل
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', customer.id)

      if (error) throw error

      toast.success('تم حذف العميل بنجاح!')
      
      // إغلاق النموذج وتحديث القائمة
      onSuccess && onSuccess()
      onClose()

    } catch (error) {
      console.error('خطأ في حذف العميل:', error)
      toast.error('فشل في حذف العميل')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen || !customer) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* رأس النموذج */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
              <Edit className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">تعديل بيانات العميل</h2>
              <p className="text-sm text-gray-500">تحديث معلومات {customer.name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* محتوى النموذج */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* المعلومات الأساسية */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* الاسم */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم العميل *
                </label>
                <div className="relative">
                  <User className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثال: أحمد محمد أو شركة التقنية المتقدمة"
                    required
                  />
                </div>
              </div>

              {/* البريد الإلكتروني */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني *
                </label>
                <div className="relative">
                  <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  تغيير الإيميل قد يؤثر على تسجيل الدخول
                </p>
              </div>

              {/* رقم الهاتف */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الهاتف
                </label>
                <div className="relative">
                  <Phone className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+966 50 123 4567"
                  />
                </div>
              </div>

              {/* جهة الاتصال */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم جهة الاتصال
                </label>
                <div className="relative">
                  <Building className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    name="contact_person"
                    value={formData.contact_person}
                    onChange={handleInputChange}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اسم الشخص المسؤول (للشركات)"
                  />
                </div>
              </div>
            </div>

            {/* نوع الحساب */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع الحساب
              </label>
              <div className="grid grid-cols-2 gap-4">
                <label className="relative">
                  <input
                    type="radio"
                    name="account_type"
                    value="customer"
                    checked={formData.account_type === 'customer'}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    formData.account_type === 'customer'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <User className={`w-5 h-5 ${
                        formData.account_type === 'customer' ? 'text-blue-600' : 'text-gray-400'
                      }`} />
                      <div>
                        <p className={`font-medium ${
                          formData.account_type === 'customer' ? 'text-blue-900' : 'text-gray-700'
                        }`}>
                          عميل
                        </p>
                        <p className="text-sm text-gray-500">حساب عميل عادي</p>
                      </div>
                    </div>
                  </div>
                </label>

                <label className="relative">
                  <input
                    type="radio"
                    name="account_type"
                    value="admin"
                    checked={formData.account_type === 'admin'}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    formData.account_type === 'admin'
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Shield className={`w-5 h-5 ${
                        formData.account_type === 'admin' ? 'text-purple-600' : 'text-gray-400'
                      }`} />
                      <div>
                        <p className={`font-medium ${
                          formData.account_type === 'admin' ? 'text-purple-900' : 'text-gray-700'
                        }`}>
                          مدير
                        </p>
                        <p className="text-sm text-gray-500">حساب إداري</p>
                      </div>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* حالة الحساب */}
            <div className="flex items-center space-x-3 space-x-reverse p-4 bg-gray-50 rounded-lg">
              <input
                type="checkbox"
                name="is_active"
                id="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                تفعيل الحساب (سيتمكن المستخدم من تسجيل الدخول)
              </label>
            </div>

            {/* معلومات إضافية */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">معلومات الحساب</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                <div>
                  <span className="font-medium">معرف العميل:</span>
                  <span className="ml-2 font-mono">{customer.id}</span>
                </div>
                <div>
                  <span className="font-medium">تاريخ التسجيل:</span>
                  <span className="ml-2">
                    {customer.created_at ? new Date(customer.created_at).toLocaleDateString('ar-SA') : 'غير محدد'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">عدد الخدمات:</span>
                  <span className="ml-2">{customer.servicesCount || 0} خدمة</span>
                </div>
                <div>
                  <span className="font-medium">آخر تحديث:</span>
                  <span className="ml-2">
                    {customer.updated_at ? new Date(customer.updated_at).toLocaleDateString('ar-SA') : 'لم يحدث'}
                  </span>
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* أزرار التحكم */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          {/* زر الحذف */}
          <button
            type="button"
            onClick={handleDelete}
            disabled={loading}
            className="px-4 py-2 text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2 space-x-reverse"
          >
            <Trash2 className="w-4 h-4" />
            <span>حذف العميل</span>
          </button>

          {/* أزرار الحفظ والإلغاء */}
          <div className="flex items-center space-x-3 space-x-reverse">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2 space-x-reverse"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>جاري الحفظ...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  <span>حفظ التغييرات</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EditCustomerModal
