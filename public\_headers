# إعدادات الأمان العامة
/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co wss://*.supabase.co

# إعدادات خاصة لـ manifest.json
/manifest.json
  X-Content-Type-Options: nosniff
  Content-Type: application/manifest+json

# إعدادات التخزين المؤقت للملفات الثابتة
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# إعدادات التخزين المؤقت للخطوط
/*.woff2
  Cache-Control: public, max-age=31536000, immutable

/*.woff
  Cache-Control: public, max-age=31536000, immutable

/*.ttf
  Cache-Control: public, max-age=31536000, immutable

# إعدادات التخزين المؤقت للصور
/*.jpg
  Cache-Control: public, max-age=2592000

/*.jpeg
  Cache-Control: public, max-age=2592000

/*.png
  Cache-Control: public, max-age=2592000

/*.svg
  Cache-Control: public, max-age=2592000

/*.webp
  Cache-Control: public, max-age=2592000

# إعدادات التخزين المؤقت للملفات الأخرى
/*.js
  Cache-Control: public, max-age=31536000, immutable

/*.css
  Cache-Control: public, max-age=31536000, immutable

# منع الوصول للملفات الحساسة
/.env
  X-Robots-Tag: noindex

/.env.*
  X-Robots-Tag: noindex

/config/*
  X-Robots-Tag: noindex
